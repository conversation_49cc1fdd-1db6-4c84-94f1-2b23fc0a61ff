---
type: "manual"
---

Use determinstic types (dfloat, dfloat2, dfloat3, dquaternion) and dmath instead of Unity types for simulations.
Do not build/run the project to verify errors.
Do not create tests unless asked for tests to be created.
Create and maintain directory level README.md's for clarity and understanding.
Review directory README.md's to gain additional context and understanding as needed.
Utilize feature folders (ex: Movement, Combat) instead of Systems/Jobs/Components for project structure.