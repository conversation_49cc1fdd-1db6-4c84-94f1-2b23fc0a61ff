using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;

namespace FlowField
{
    public static class FlowFieldUtils
    {
        public static dfloat2 SampleFlowField(DynamicBuffer<FlowFieldCellBuffer> flowField,
            dfloat2 worldPos, dfloat2 worldOrigin,
            dfloat2 cellSize, int2 gridSize, MovementType movementType)
        {
            var relativePos = worldPos - worldOrigin;
            var gridPos = new int2((int)(relativePos.x / cellSize.x), (int)(relativePos.y / cellSize.y));

            if (gridPos.x < 0 || gridPos.x >= gridSize.x || gridPos.y < 0 || gridPos.y >= gridSize.y)
                return dfloat2.zero;

            var index = gridPos.y * gridSize.x + gridPos.x;
            var cell = flowField[index].cell;

            // Check if this movement type can use this cell
            if (!cell.IsWalkableFor(movementType))
                return dfloat2.zero;

            return cell.direction;
        }

        public static void SetObstacle(DynamicBuffer<FlowFieldCellBuffer> flowField,
            int2 gridPos, int2 gridSize, MovementType movementType, bool isWalkable)
        {
            if (gridPos.x < 0 || gridPos.x >= gridSize.x || gridPos.y < 0 || gridPos.y >= gridSize.y)
                return;

            var index = gridPos.y * gridSize.x + gridPos.x;
            var cell = flowField[index].cell;
            cell.SetWalkable(movementType, isWalkable);
            flowField[index] = new FlowFieldCellBuffer { cell = cell };
        }

        public static void SetMovementCost(DynamicBuffer<FlowFieldCellBuffer> flowField,
            int2 gridPos, int2 gridSize, dfloat cost)
        {
            if (gridPos.x < 0 || gridPos.x >= gridSize.x || gridPos.y < 0 || gridPos.y >= gridSize.y)
                return;

            var index = gridPos.y * gridSize.x + gridPos.x;
            var cell = flowField[index].cell;
            cell.cost = cost;
            flowField[index] = new FlowFieldCellBuffer { cell = cell };
        }

        public static FlowFieldGrid CreateFlowFieldGrid(int2 gridSize, dfloat2 cellSize, dfloat2 worldOrigin,
            Entity targetEntity, int targetId, MovementType movementType)
        {
            return new FlowFieldGrid
            {
                gridSize = gridSize,
                cellSize = cellSize,
                worldOrigin = worldOrigin,
                targetEntity = targetEntity,
                targetId = targetId,
                movementType = movementType,
                needsUpdate = true,
                lastUpdateTime = dfloat.Zero,
                updateInterval = new dfloat(60) // Update every minute
            };
        }
    }
}