using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Transforms;
using Unity.Deterministic.Mathematics;
using System.Collections.Generic;
using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;

namespace FlowField.Visualization
{
    /// <summary>
    /// Visualization component for projectiles, their trajectories, and combat-related visual effects.
    /// Shows projectile paths, collision areas, and combat effects.
    /// </summary>
    public class ProjectileVisualization : BaseVisualization
    {
        [Header("Projectile Display Options")]
        [SerializeField] private bool showProjectiles = true;
        [SerializeField] private bool showProjectileTrajectories = true;
        [SerializeField] private bool showCollisionAreas = true;
        [SerializeField] private bool showVelocityVectors = true;
        [SerializeField] private bool showProjectileInfo = false;
        [SerializeField] private bool showExplosionAreas = false;
        
        [Header("Projectile Colors")]
        [SerializeField] private Color linearProjectileColor = Color.red;
        [SerializeField] private Color homingProjectileColor = Color.orange;
        [SerializeField] private Color arcProjectileColor = Color.yellow;
        [SerializeField] private Color bouncingProjectileColor = Color.purple;
        [SerializeField] private Color seekingProjectileColor = Color.cyan;
        [SerializeField] private Color trajectoryColor = Color.white;
        [SerializeField] private Color collisionColor = Color.red;
        [SerializeField] private Color velocityColor = Color.green;
        [SerializeField] private Color explosionColor = Color.orange;
        
        [Header("Projectile Properties")]
        [SerializeField] private float projectileSize = 0.2f;
        [SerializeField] private float trajectoryLength = 5.0f;
        [SerializeField] private float velocityScale = 1.0f;
        [SerializeField] private float collisionAlpha = 0.3f;
        [SerializeField] private float explosionAlpha = 0.2f;
        [SerializeField] private int trajectorySteps = 20;
        
        [Header("Projectile Configuration")]
        [SerializeField] private List<Entity> projectilesToVisualize = new List<Entity>();
        [SerializeField] private bool autoFindProjectiles = true;
        [SerializeField] private bool showOnlySelectedProjectile = false;
        [SerializeField] private Entity selectedProjectile = Entity.Null;
        
        private EntityQuery projectileQuery;
        private int lastProjectileCount = 0;
        
        public override string VisualizationName => "Projectile Visualization";
        
        // Properties for external configuration
        public List<Entity> ProjectilesToVisualize 
        { 
            get => projectilesToVisualize; 
            set => projectilesToVisualize = value; 
        }
        
        public Entity SelectedProjectile 
        { 
            get => selectedProjectile; 
            set => selectedProjectile = value; 
        }
        
        public bool ShowProjectiles 
        { 
            get => showProjectiles; 
            set => showProjectiles = value; 
        }
        
        public bool ShowProjectileTrajectories 
        { 
            get => showProjectileTrajectories; 
            set => showProjectileTrajectories = value; 
        }
        
        public bool ShowCollisionAreas 
        { 
            get => showCollisionAreas; 
            set => showCollisionAreas = value; 
        }
        
        public bool ShowVelocityVectors 
        { 
            get => showVelocityVectors; 
            set => showVelocityVectors = value; 
        }
        
        public bool AutoFindProjectiles 
        { 
            get => autoFindProjectiles; 
            set => autoFindProjectiles = value; 
        }
        
        protected override void OnInitialize()
        {
            if (autoFindProjectiles)
            {
                CreateProjectileQuery();
            }
        }
        
        protected override void OnDrawGizmos()
        {
            if (!showProjectiles)
                return;
                
            if (showOnlySelectedProjectile && IsEntityValid(selectedProjectile))
            {
                DrawProjectileVisualization(selectedProjectile);
            }
            else if (autoFindProjectiles)
            {
                DrawProjectilesFromQuery();
            }
            else
            {
                DrawProjectilesFromList();
            }
        }
        
        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Projectile Visualization not initialized";
                
            var projectileCount = autoFindProjectiles ? GetProjectileCountFromQuery() : projectilesToVisualize.Count;
            var selectedProjectileInfo = IsEntityValid(selectedProjectile) ? "Valid" : "None";
            
            return $"Projectiles: {projectileCount}\n" +
                   $"Selected: {selectedProjectileInfo}\n" +
                   $"Show: Trajectories={showProjectileTrajectories}, Collisions={showCollisionAreas}, Velocity={showVelocityVectors}\n" +
                   $"Trajectory Steps: {trajectorySteps}, Length: {trajectoryLength}";
        }
        
        private void CreateProjectileQuery()
        {
            projectileQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(ProjectileData)
            );
        }
        
        private void DrawProjectilesFromQuery()
        {
            if (projectileQuery.IsEmpty)
                CreateProjectileQuery();
                
            var entities = projectileQuery.ToEntityArray(Allocator.Temp);
            
            try
            {
                foreach (var projectile in entities)
                {
                    DrawProjectileVisualization(projectile);
                }
            }
            finally
            {
                entities.Dispose();
            }
        }
        
        private void DrawProjectilesFromList()
        {
            foreach (var projectile in projectilesToVisualize)
            {
                if (IsEntityValid(projectile))
                {
                    DrawProjectileVisualization(projectile);
                }
            }
        }
        
        private void DrawProjectileVisualization(Entity projectile)
        {
            if (!entityManager.HasComponent<LocalTransform>(projectile) || 
                !entityManager.HasComponent<ProjectileData>(projectile))
                return;
                
            var transform = entityManager.GetComponentData<LocalTransform>(projectile);
            var projectileData = entityManager.GetComponentData<ProjectileData>(projectile);
            var position = transform.Position;
            
            // Draw basic projectile representation
            DrawBasicProjectile(position, projectileData);
            
            // Draw collision area
            if (showCollisionAreas)
            {
                DrawCollisionArea(position, projectileData);
            }
            
            // Draw velocity vector
            if (showVelocityVectors)
            {
                DrawVelocityVector(position, projectileData);
            }
            
            // Draw trajectory prediction
            if (showProjectileTrajectories)
            {
                DrawProjectileTrajectory(position, projectileData);
            }
            
            // Draw explosion area for AOE projectiles
            if (showExplosionAreas && projectileData.aoeRadius > dfloat.Zero)
            {
                DrawExplosionArea(position, projectileData);
            }
            
            // Draw projectile info
            if (showProjectileInfo)
            {
                DrawProjectileInfo(position, projectileData);
            }
        }
        
        private void DrawBasicProjectile(float3 position, ProjectileData projectileData)
        {
            Color projectileColor = GetProjectileColor(projectileData.projectileType);
            Gizmos.color = ApplyAlpha(projectileColor);
            
            // Draw projectile as a sphere
            Gizmos.DrawSphere(position, projectileSize);
            
            // Draw wireframe for better visibility
            Gizmos.color = ApplyAlpha(projectileColor, 0.7f);
            Gizmos.DrawWireSphere(position, projectileSize * 1.2f);
        }
        
        private Color GetProjectileColor(ProjectileType projectileType)
        {
            return projectileType switch
            {
                ProjectileType.Linear => linearProjectileColor,
                ProjectileType.Homing => homingProjectileColor,
                ProjectileType.Arc => arcProjectileColor,
                ProjectileType.Bouncing => bouncingProjectileColor,
                ProjectileType.Seeking => seekingProjectileColor,
                _ => linearProjectileColor
            };
        }
        
        private void DrawCollisionArea(float3 position, ProjectileData projectileData)
        {
            Gizmos.color = ApplyAlpha(collisionColor, collisionAlpha);
            Gizmos.DrawWireSphere(position, (float)projectileData.collisionRadius);
        }
        
        private void DrawVelocityVector(float3 position, ProjectileData projectileData)
        {
            var velocity = new float3((float)projectileData.velocity.x, (float)projectileData.velocity.y, (float)projectileData.velocity.z);
            var velocityEnd = position + velocity * velocityScale;
            
            Gizmos.color = ApplyAlpha(velocityColor);
            Gizmos.DrawLine(position, velocityEnd);
            
            // Draw arrowhead
            var direction = math.normalize(velocity);
            if (math.lengthsq(direction) > 0.001f)
            {
                var arrowSize = 0.3f;
                var perpendicular = new float3(-direction.y, direction.x, 0) * arrowSize;
                var arrowHead1 = velocityEnd - direction * arrowSize + perpendicular;
                var arrowHead2 = velocityEnd - direction * arrowSize - perpendicular;
                
                Gizmos.DrawLine(velocityEnd, arrowHead1);
                Gizmos.DrawLine(velocityEnd, arrowHead2);
            }
        }
        
        private void DrawProjectileTrajectory(float3 position, ProjectileData projectileData)
        {
            var currentPos = new dfloat3((dfloat)position.x, (dfloat)position.y, (dfloat)position.z);
            var currentVel = projectileData.velocity;
            var stepTime = (dfloat)(trajectoryLength / trajectorySteps);
            
            Gizmos.color = ApplyAlpha(trajectoryColor, 0.6f);
            
            for (int i = 0; i < trajectorySteps; i++)
            {
                var nextPos = PredictNextPosition(currentPos, currentVel, projectileData, stepTime);
                
                Gizmos.DrawLine(
                    new float3((float)currentPos.x, (float)currentPos.y, (float)currentPos.z),
                    new float3((float)nextPos.x, (float)nextPos.y, (float)nextPos.z)
                );
                
                currentPos = nextPos;
                
                // Update velocity for arc projectiles (gravity effect)
                if (projectileData.projectileType == ProjectileType.Arc)
                {
                    currentVel += projectileData.acceleration * stepTime;
                }
            }
        }
        
        private dfloat3 PredictNextPosition(dfloat3 currentPos, dfloat3 currentVel, ProjectileData projectileData, dfloat deltaTime)
        {
            switch (projectileData.projectileType)
            {
                case ProjectileType.Linear:
                    return currentPos + currentVel * deltaTime;
                    
                case ProjectileType.Arc:
                    return currentPos + currentVel * deltaTime + (dfloat)0.5f * projectileData.acceleration * deltaTime * deltaTime;
                    
                case ProjectileType.Homing:
                    // Simplified homing prediction - would need target position for accuracy
                    return currentPos + currentVel * deltaTime;
                    
                default:
                    return currentPos + currentVel * deltaTime;
            }
        }
        
        private void DrawExplosionArea(float3 position, ProjectileData projectileData)
        {
            Gizmos.color = ApplyAlpha(explosionColor, explosionAlpha);
            Gizmos.DrawWireSphere(position, (float)projectileData.aoeRadius);
            
            // Draw inner explosion core
            Gizmos.color = ApplyAlpha(explosionColor, explosionAlpha * 2);
            Gizmos.DrawWireSphere(position, (float)projectileData.aoeRadius * 0.5f);
        }
        
        private void DrawProjectileInfo(float3 position, ProjectileData projectileData)
        {
            // Draw a small indicator for projectile info
            var offset = new float3(0, projectileSize + 0.3f, 0);
            Gizmos.color = ApplyAlpha(Color.white);
            Gizmos.DrawWireCube(position + offset, Vector3.one * 0.15f);
            
            // Could be enhanced to draw actual text in scene view
            // For now, we rely on the detailed info system
        }
        
        private int GetProjectileCountFromQuery()
        {
            if (projectileQuery.IsEmpty)
                return 0;
            return projectileQuery.CalculateEntityCount();
        }
        
        protected override void OnCleanup()
        {
            if (!projectileQuery.IsEmpty)
            {
                projectileQuery.Dispose();
            }
        }
        
        /// <summary>
        /// Add a projectile to the visualization list (when not using auto-find)
        /// </summary>
        public void AddProjectile(Entity projectile)
        {
            if (!projectilesToVisualize.Contains(projectile))
            {
                projectilesToVisualize.Add(projectile);
            }
        }
        
        /// <summary>
        /// Remove a projectile from the visualization list
        /// </summary>
        public void RemoveProjectile(Entity projectile)
        {
            projectilesToVisualize.Remove(projectile);
        }
        
        /// <summary>
        /// Clear all projectiles from the visualization list
        /// </summary>
        public void ClearProjectiles()
        {
            projectilesToVisualize.Clear();
        }
    }
}
