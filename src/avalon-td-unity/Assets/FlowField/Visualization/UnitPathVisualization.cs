using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Transforms;
using Unity.Deterministic.Mathematics;
using System.Collections.Generic;
using Avalon.Simulation.Movement;

namespace FlowField.Visualization
{
    /// <summary>
    /// Visualization component for unit paths, movement trails, and pathfinding information.
    /// Shows predicted paths, movement history, and pathfinding debug information.
    /// </summary>
    public class UnitPathVisualization : BaseVisualization
    {
        [Header("Path Display Options")]
        [SerializeField] private bool showPredictedPaths = true;
        [SerializeField] private bool showMovementTrails = true;
        [SerializeField] private bool showDirectPaths = true;
        [SerializeField] private bool showFlowFieldSampling = false;
        [SerializeField] private bool showPathNodes = false;
        
        [Header("Path Colors")]
        [SerializeField] private Color predictedPathColor = Color.cyan;
        [SerializeField] private Color movementTrailColor = Color.blue;
        [SerializeField] private Color directPathColor = Color.yellow;
        [SerializeField] private Color flowSampleColor = Color.green;
        [SerializeField] private Color pathNodeColor = Color.white;
        
        [Header("Path Properties")]
        [SerializeField] private float pathLineWidth = 2.0f;
        [SerializeField] private float trailFadeTime = 5.0f;
        [SerializeField] private int maxTrailPoints = 50;
        [SerializeField] private int pathPredictionSteps = 20;
        [SerializeField] private float pathPredictionStepSize = 0.5f;
        [SerializeField] private float flowSampleSpacing = 1.0f;
        
        [Header("Path Configuration")]
        [SerializeField] private List<Entity> unitsToTrack = new List<Entity>();
        [SerializeField] private bool autoFindUnits = true;
        [SerializeField] private bool showOnlySelectedUnit = false;
        [SerializeField] private Entity selectedUnit = Entity.Null;
        
        // Trail data storage
        private Dictionary<Entity, Queue<TrailPoint>> unitTrails = new Dictionary<Entity, Queue<TrailPoint>>();
        private EntityQuery unitQuery;
        private EntityQuery flowFieldQuery;
        
        private struct TrailPoint
        {
            public float3 position;
            public float timestamp;
        }
        
        public override string VisualizationName => "Unit Path Visualization";
        
        // Properties for external configuration
        public List<Entity> UnitsToTrack 
        { 
            get => unitsToTrack; 
            set => unitsToTrack = value; 
        }
        
        public Entity SelectedUnit 
        { 
            get => selectedUnit; 
            set => selectedUnit = value; 
        }
        
        public bool ShowPredictedPaths 
        { 
            get => showPredictedPaths; 
            set => showPredictedPaths = value; 
        }
        
        public bool ShowMovementTrails 
        { 
            get => showMovementTrails; 
            set => showMovementTrails = value; 
        }
        
        public bool ShowDirectPaths 
        { 
            get => showDirectPaths; 
            set => showDirectPaths = value; 
        }
        
        public bool ShowOnlySelectedUnit 
        { 
            get => showOnlySelectedUnit; 
            set => showOnlySelectedUnit = value; 
        }
        
        public bool AutoFindUnits 
        { 
            get => autoFindUnits; 
            set => autoFindUnits = value; 
        }
        
        protected override void OnInitialize()
        {
            if (autoFindUnits)
            {
                CreateQueries();
            }
        }
        
        protected override void OnDrawGizmos()
        {
            UpdateTrails();
            
            if (showOnlySelectedUnit && IsEntityValid(selectedUnit))
            {
                DrawUnitPaths(selectedUnit);
            }
            else if (autoFindUnits)
            {
                DrawPathsFromQuery();
            }
            else
            {
                DrawPathsFromList();
            }
        }
        
        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Unit Path Visualization not initialized";
                
            var unitCount = autoFindUnits ? GetUnitCountFromQuery() : unitsToTrack.Count;
            var trailCount = unitTrails.Count;
            var selectedUnitInfo = IsEntityValid(selectedUnit) ? "Valid" : "None";
            
            return $"Units: {unitCount} (Trails: {trailCount})\n" +
                   $"Selected Unit: {selectedUnitInfo}\n" +
                   $"Show: Predicted={showPredictedPaths}, Trails={showMovementTrails}, Direct={showDirectPaths}\n" +
                   $"Trail Points: {maxTrailPoints}, Prediction Steps: {pathPredictionSteps}";
        }
        
        private void CreateQueries()
        {
            unitQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldFollower)
            );
            
            flowFieldQuery = entityManager.CreateEntityQuery(
                typeof(FlowFieldGrid),
                typeof(FlowFieldCellBuffer)
            );
        }
        
        private void UpdateTrails()
        {
            var currentTime = Time.time;
            var unitsToProcess = showOnlySelectedUnit && IsEntityValid(selectedUnit) 
                ? new List<Entity> { selectedUnit }
                : GetUnitsToProcess();
            
            foreach (var unit in unitsToProcess)
            {
                if (!IsEntityValid(unit) || !entityManager.HasComponent<LocalTransform>(unit))
                    continue;
                    
                var transform = entityManager.GetComponentData<LocalTransform>(unit);
                UpdateUnitTrail(unit, transform.Position, currentTime);
            }
            
            // Clean up old trail points
            CleanupOldTrailPoints(currentTime);
        }
        
        private List<Entity> GetUnitsToProcess()
        {
            if (autoFindUnits && !unitQuery.IsEmpty)
            {
                var entities = unitQuery.ToEntityArray(Allocator.Temp);
                var result = new List<Entity>(entities.ToArray());
                entities.Dispose();
                return result;
            }
            else
            {
                return unitsToTrack;
            }
        }
        
        private void UpdateUnitTrail(Entity unit, float3 position, float currentTime)
        {
            if (!unitTrails.ContainsKey(unit))
            {
                unitTrails[unit] = new Queue<TrailPoint>();
            }
            
            var trail = unitTrails[unit];
            
            // Add new point if unit has moved significantly
            if (trail.Count == 0 || math.distance(trail.ToArray()[trail.Count - 1].position, position) > 0.1f)
            {
                trail.Enqueue(new TrailPoint { position = position, timestamp = currentTime });
                
                // Limit trail length
                while (trail.Count > maxTrailPoints)
                {
                    trail.Dequeue();
                }
            }
        }
        
        private void CleanupOldTrailPoints(float currentTime)
        {
            var unitsToRemove = new List<Entity>();
            
            foreach (var kvp in unitTrails)
            {
                var unit = kvp.Key;
                var trail = kvp.Value;
                
                // Remove old points
                while (trail.Count > 0 && currentTime - trail.Peek().timestamp > trailFadeTime)
                {
                    trail.Dequeue();
                }
                
                // Remove empty trails for non-existent units
                if (trail.Count == 0 && !IsEntityValid(unit))
                {
                    unitsToRemove.Add(unit);
                }
            }
            
            foreach (var unit in unitsToRemove)
            {
                unitTrails.Remove(unit);
            }
        }
        
        private void DrawPathsFromQuery()
        {
            if (unitQuery.IsEmpty)
                return;
                
            var entities = unitQuery.ToEntityArray(Allocator.Temp);
            
            try
            {
                foreach (var unit in entities)
                {
                    DrawUnitPaths(unit);
                }
            }
            finally
            {
                entities.Dispose();
            }
        }
        
        private void DrawPathsFromList()
        {
            foreach (var unit in unitsToTrack)
            {
                if (IsEntityValid(unit))
                {
                    DrawUnitPaths(unit);
                }
            }
        }
        
        private void DrawUnitPaths(Entity unit)
        {
            if (!entityManager.HasComponent<LocalTransform>(unit) || 
                !entityManager.HasComponent<FlowFieldFollower>(unit))
                return;
                
            var transform = entityManager.GetComponentData<LocalTransform>(unit);
            var movementTarget = entityManager.GetComponentData<MovementTarget>(unit);
            var position = transform.Position;
            
            // Draw movement trail
            if (showMovementTrails)
            {
                DrawMovementTrail(unit);
            }
            
            // Draw direct path to target
            if (showDirectPaths)
            {
                DrawDirectPath(unit, position, movementTarget);
            }
            
            // Draw predicted path using flow field
            if (showPredictedPaths)
            {
                DrawPredictedPath(unit, position, movementTarget);
            }
            
            // Draw flow field sampling points
            if (showFlowFieldSampling)
            {
                DrawFlowFieldSampling(position, movementTarget);
            }
        }
        
        private void DrawMovementTrail(Entity unit)
        {
            if (!unitTrails.ContainsKey(unit))
                return;
                
            var trail = unitTrails[unit].ToArray();
            if (trail.Length < 2)
                return;
                
            var currentTime = Time.time;
            
            for (int i = 0; i < trail.Length - 1; i++)
            {
                var age = currentTime - trail[i].timestamp;
                var alpha = 1.0f - (age / trailFadeTime);
                alpha = Mathf.Clamp01(alpha);
                
                Gizmos.color = ApplyAlpha(movementTrailColor, alpha);
                Gizmos.DrawLine(trail[i].position, trail[i + 1].position);
            }
        }
        
        private void DrawDirectPath(Entity unit, float3 position, MovementTarget movementTarget)
        {
            var target = GetTargetPosition(movementTarget.targetId);
            if (target.HasValue)
            {
                Gizmos.color = ApplyAlpha(directPathColor, 0.5f);
                Gizmos.DrawLine(position, target.Value);
            }
        }
        
        private void DrawPredictedPath(Entity unit, float3 position, MovementTarget movementTarget)
        {
            // Map 3D position (x,y,z) to flow field coordinates (x,z)
            var currentPos = new dfloat2((dfloat)position.x, (dfloat)position.z);
            var pathPoints = new List<float3> { position };

            for (int i = 0; i < pathPredictionSteps; i++)
            {
                var flowDirection = SampleFlowField(currentPos, movementTarget);
                if (math.lengthsq(new float2((float)flowDirection.x, (float)flowDirection.y)) < 0.001f)
                    break;

                currentPos += flowDirection * (dfloat)pathPredictionStepSize;
                // Map flow field coordinates (x,y) back to 3D world coordinates (x,y,z)
                pathPoints.Add(new float3((float)currentPos.x, position.y, (float)currentPos.y));
            }
            
            // Draw predicted path
            Gizmos.color = ApplyAlpha(predictedPathColor);
            for (int i = 0; i < pathPoints.Count - 1; i++)
            {
                Gizmos.DrawLine(pathPoints[i], pathPoints[i + 1]);
            }
            
            // Draw path nodes
            if (showPathNodes)
            {
                Gizmos.color = ApplyAlpha(pathNodeColor);
                foreach (var point in pathPoints)
                {
                    Gizmos.DrawWireSphere(point, 0.1f);
                }
            }
        }
        
        private void DrawFlowFieldSampling(float3 position, MovementTarget movementTarget)
        {
            var samplePositions = new List<dfloat2>();
            // Map 3D position (x,y,z) to flow field coordinates (x,z)
            var basePos = new dfloat2((dfloat)position.x, (dfloat)position.z);

            // Sample in a grid around the unit
            for (float x = -2; x <= 2; x += flowSampleSpacing)
            {
                for (float y = -2; y <= 2; y += flowSampleSpacing)
                {
                    samplePositions.Add(basePos + new dfloat2((dfloat)x, (dfloat)y));
                }
            }

            Gizmos.color = ApplyAlpha(flowSampleColor, 0.7f);
            foreach (var samplePos in samplePositions)
            {
                var flowDirection = SampleFlowField(samplePos, movementTarget);
                if (math.lengthsq(new float2((float)flowDirection.x, (float)flowDirection.y)) > 0.001f)
                {
                    // Map flow field coordinates (x,y) back to 3D world coordinates (x,y,z)
                    var worldPos = new float3((float)samplePos.x, position.y, (float)samplePos.y);
                    // Map flow field direction (x,y) to 3D world direction (x,0,z)
                    var endPos = worldPos + new float3((float)flowDirection.x, 0, (float)flowDirection.y) * 0.5f;
                    Gizmos.DrawLine(worldPos, endPos);
                }
            }
        }
        
        private dfloat2 SampleFlowField(dfloat2 worldPos, MovementTarget movementTarget)
        {
            // This is a simplified flow field sampling - in a real implementation,
            // you would access the actual flow field data
            if (flowFieldQuery.IsEmpty)
                return dfloat2.zero;
                
            // For now, return a simple direction towards target
            var target = GetTargetPosition(movementTarget.targetId);
            if (target.HasValue)
            {
                // Map 3D target position (x,y,z) to flow field coordinates (x,z) and calculate direction
                var direction = target.Value.xz - new float2((float)worldPos.x, (float)worldPos.y);
                direction = math.normalize(direction);
                return new dfloat2((dfloat)direction.x, (dfloat)direction.y);
            }
            
            return dfloat2.zero;
        }
        
        private float3? GetTargetPosition(int? targetId)
        {
            // Find target entity with matching ID
            if (!isInitialized)
                return null;
            
            if (targetId == null)
                return null;
                
            var targetQuery = entityManager.CreateEntityQuery(typeof(FlowFieldTarget), typeof(LocalTransform));
            var entities = targetQuery.ToEntityArray(Allocator.Temp);
            
            try
            {
                foreach (var entity in entities)
                {
                    var target = entityManager.GetComponentData<FlowFieldTarget>(entity);
                    if (target.targetId == targetId)
                    {
                        var transform = entityManager.GetComponentData<LocalTransform>(entity);
                        return transform.Position;
                    }
                }
            }
            finally
            {
                entities.Dispose();
                targetQuery.Dispose();
            }
            
            return null;
        }
        
        private int GetUnitCountFromQuery()
        {
            if (unitQuery.IsEmpty)
                return 0;
            return unitQuery.CalculateEntityCount();
        }
        
        protected override void OnCleanup()
        {
            if (!unitQuery.IsEmpty)
            {
                unitQuery.Dispose();
            }
            
            if (!flowFieldQuery.IsEmpty)
            {
                flowFieldQuery.Dispose();
            }
            
            unitTrails.Clear();
        }
        
        /// <summary>
        /// Add a unit to track for path visualization
        /// </summary>
        public void AddUnit(Entity unit)
        {
            if (!unitsToTrack.Contains(unit))
            {
                unitsToTrack.Add(unit);
            }
        }
        
        /// <summary>
        /// Remove a unit from path tracking
        /// </summary>
        public void RemoveUnit(Entity unit)
        {
            unitsToTrack.Remove(unit);
            if (unitTrails.ContainsKey(unit))
            {
                unitTrails.Remove(unit);
            }
        }
        
        /// <summary>
        /// Clear all tracked units and trails
        /// </summary>
        public void ClearUnits()
        {
            unitsToTrack.Clear();
            unitTrails.Clear();
        }
    }
}
