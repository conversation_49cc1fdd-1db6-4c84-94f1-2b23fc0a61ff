using Unity.Entities;
using UnityEngine;

namespace FlowField.Visualization
{
    /// <summary>
    /// Base abstract class for all visualization components.
    /// Provides common functionality and default implementations for the IVisualization interface.
    /// </summary>
    public abstract class BaseVisualization : MonoBehaviour, IVisualization
    {
        [Header("Visualization Settings")]
        [SerializeField] protected bool isEnabled = true;
        [SerializeField] protected bool showDetailedInfo = false;
        
        [Header("Visual Properties")]
        [SerializeField] protected Color primaryColor = Color.white;
        [SerializeField] protected Color secondaryColor = Color.gray;
        [SerializeField] protected float alpha = 1.0f;
        
        protected EntityManager entityManager;
        protected World world;
        protected bool isInitialized = false;
        
        /// <summary>
        /// Whether this visualization component is enabled and should draw gizmos
        /// </summary>
        public virtual bool IsEnabled 
        { 
            get => isEnabled; 
            set => isEnabled = value; 
        }
        
        /// <summary>
        /// Whether to show detailed information about what this component visualizes
        /// </summary>
        public virtual bool ShowDetailedInfo 
        { 
            get => showDetailedInfo; 
            set => showDetailedInfo = value; 
        }
        
        /// <summary>
        /// The name of this visualization component for UI display
        /// </summary>
        public abstract string VisualizationName { get; }
        
        /// <summary>
        /// Initialize the visualization component with required dependencies
        /// </summary>
        /// <param name="entityManager">The EntityManager for accessing ECS data</param>
        /// <param name="world">The World instance</param>
        public virtual void Initialize(EntityManager entityManager, World world)
        {
            this.entityManager = entityManager;
            this.world = world;
            this.isInitialized = true;
            OnInitialize();
        }
        
        /// <summary>
        /// Called after base initialization is complete.
        /// Override this in derived classes for custom initialization logic.
        /// </summary>
        protected virtual void OnInitialize() { }
        
        /// <summary>
        /// Draw gizmos for this visualization component
        /// Called from OnDrawGizmos
        /// </summary>
        public void DrawGizmos()
        {
            if (!isEnabled || !isInitialized || !Application.isPlaying)
                return;
                
            var originalColor = Gizmos.color;
            
            try
            {
                OnDrawGizmos();
                
                if (showDetailedInfo)
                {
                    DrawDetailedInfo();
                }
            }
            finally
            {
                Gizmos.color = originalColor;
            }
        }
        
        /// <summary>
        /// Override this method in derived classes to implement specific gizmo drawing logic
        /// </summary>
        protected abstract void OnDrawGizmos();
        
        /// <summary>
        /// Draw detailed information as gizmos or GUI elements
        /// Override this method to customize how detailed info is displayed
        /// </summary>
        protected virtual void DrawDetailedInfo()
        {
            // Default implementation - can be overridden by derived classes
            var info = GetDetailedInfo();
            if (!string.IsNullOrEmpty(info))
            {
                // This could be enhanced to draw text in the scene view
                Debug.Log($"[{VisualizationName}] {info}");
            }
        }
        
        /// <summary>
        /// Get detailed information about what this component is currently visualizing
        /// Used for debug display and inspector information
        /// </summary>
        /// <returns>String containing detailed information</returns>
        public abstract string GetDetailedInfo();
        
        /// <summary>
        /// Cleanup any resources used by this visualization component
        /// </summary>
        public virtual void Cleanup()
        {
            isInitialized = false;
            OnCleanup();
        }
        
        /// <summary>
        /// Called during cleanup. Override this in derived classes for custom cleanup logic.
        /// </summary>
        protected virtual void OnCleanup() { }
        
        /// <summary>
        /// Helper method to apply alpha to a color
        /// </summary>
        /// <param name="color">The base color</param>
        /// <param name="alphaMultiplier">Alpha multiplier (0-1)</param>
        /// <returns>Color with applied alpha</returns>
        protected Color ApplyAlpha(Color color, float alphaMultiplier = 1.0f)
        {
            return new Color(color.r, color.g, color.b, color.a * alpha * alphaMultiplier);
        }
        
        /// <summary>
        /// Helper method to check if the EntityManager and required entities are valid
        /// </summary>
        /// <param name="entity">Entity to check</param>
        /// <returns>True if valid, false otherwise</returns>
        protected bool IsEntityValid(Entity entity)
        {
            return isInitialized && entityManager.Exists(entity);
        }
        
        /// <summary>
        /// Unity lifecycle method - ensure cleanup on destroy
        /// </summary>
        protected virtual void OnDestroy()
        {
            Cleanup();
        }
    }
}
