using UnityEngine;
using FlowField.Visualization;
using System.Collections.Generic;

namespace FlowField.Tests
{
    /// <summary>
    /// Demo script showcasing the benefits of the new modular visualization system.
    /// Demonstrates easy enable/disable, independent configuration, and detailed information display.
    /// </summary>
    public class VisualizationSystemDemo : MonoBehaviour
    {
        [Header("Visualization Control")]
        [SerializeField] private bool enableGridVisualization = true;
        [SerializeField] private bool enableUnitVisualization = true;
        [SerializeField] private bool enableTargetVisualization = true;
        [SerializeField] private bool enableUnitPathVisualization = false;
        [SerializeField] private bool enableProjectileVisualization = false;
        
        [Header("Grid Visualization Settings")]
        [SerializeField] private bool showGrid = true;
        [SerializeField] private bool showDirections = true;
        [SerializeField] private bool showCosts = false;
        [SerializeField] private bool showWalkability = true;
        
        [Head<PERSON>("Unit Visualization Settings")]
        [SerializeField] private bool showUnits = true;
        [SerializeField] private bool showUnitVelocity = true;
        [SerializeField] private bool showUnitDesiredDirection = true;
        [SerializeField] private bool showAvoidanceForces = true;
        
        [Header("Target Visualization Settings")]
        [SerializeField] private bool showTargets = true;
        [SerializeField] private bool showTargetInfo = true;
        [SerializeField] private bool showTargetRange = false;
        [SerializeField] private bool showTargetConnections = false;
        
        [Header("Path Visualization Settings")]
        [SerializeField] private bool showPredictedPaths = true;
        [SerializeField] private bool showMovementTrails = true;
        [SerializeField] private bool showDirectPaths = true;
        
        [Header("Projectile Visualization Settings")]
        [SerializeField] private bool showProjectiles = true;
        [SerializeField] private bool showProjectileTrajectories = true;
        [SerializeField] private bool showCollisionAreas = true;
        [SerializeField] private bool showVelocityVectors = true;
        
        [Header("Demo Controls")]
        [SerializeField] private KeyCode toggleAllKey = KeyCode.Space;
        [SerializeField] private KeyCode toggleGridKey = KeyCode.G;
        [SerializeField] private KeyCode toggleUnitsKey = KeyCode.U;
        [SerializeField] private KeyCode toggleTargetsKey = KeyCode.T;
        [SerializeField] private KeyCode togglePathsKey = KeyCode.P;
        [SerializeField] private KeyCode toggleProjectilesKey = KeyCode.R;
        [SerializeField] private KeyCode showInfoKey = KeyCode.I;
        
        // Component references
        private GridVisualization gridViz;
        private UnitVisualization unitViz;
        private TargetVisualization targetViz;
        private UnitPathVisualization pathViz;
        private ProjectileVisualization projectileViz;
        
        private List<IVisualization> allComponents = new List<IVisualization>();
        private bool allEnabled = true;
        
        void Start()
        {
            // Find existing visualization components
            FindVisualizationComponents();
            
            // Apply initial settings
            ApplyVisualizationSettings();
            
            // Show instructions
            ShowInstructions();
        }
        
        void Update()
        {
            HandleInput();
            
            // Continuously apply settings (for real-time tweaking in inspector)
            ApplyVisualizationSettings();
        }
        
        private void FindVisualizationComponents()
        {
            gridViz = GetComponent<GridVisualization>();
            unitViz = GetComponent<UnitVisualization>();
            targetViz = GetComponent<TargetVisualization>();
            pathViz = GetComponent<UnitPathVisualization>();
            projectileViz = GetComponent<ProjectileVisualization>();
            
            // Add to list for batch operations
            allComponents.Clear();
            if (gridViz != null) allComponents.Add(gridViz);
            if (unitViz != null) allComponents.Add(unitViz);
            if (targetViz != null) allComponents.Add(targetViz);
            if (pathViz != null) allComponents.Add(pathViz);
            if (projectileViz != null) allComponents.Add(projectileViz);
        }
        
        private void ApplyVisualizationSettings()
        {
            // Apply main enable/disable settings
            if (gridViz != null)
            {
                gridViz.IsEnabled = enableGridVisualization;
                gridViz.ShowGrid = showGrid;
                gridViz.ShowDirections = showDirections;
                gridViz.ShowCosts = showCosts;
                gridViz.ShowWalkability = showWalkability;
            }
            
            if (unitViz != null)
            {
                unitViz.IsEnabled = enableUnitVisualization;
                unitViz.ShowUnits = showUnits;
                unitViz.ShowUnitVelocity = showUnitVelocity;
                unitViz.ShowUnitDesiredDirection = showUnitDesiredDirection;
                unitViz.ShowAvoidanceForces = showAvoidanceForces;
            }
            
            if (targetViz != null)
            {
                targetViz.IsEnabled = enableTargetVisualization;
                targetViz.ShowTargets = showTargets;
                targetViz.ShowTargetInfo = showTargetInfo;
                targetViz.ShowTargetRange = showTargetRange;
                targetViz.ShowTargetConnections = showTargetConnections;
            }
            
            if (pathViz != null)
            {
                pathViz.IsEnabled = enableUnitPathVisualization;
                pathViz.ShowPredictedPaths = showPredictedPaths;
                pathViz.ShowMovementTrails = showMovementTrails;
                pathViz.ShowDirectPaths = showDirectPaths;
            }
            
            if (projectileViz != null)
            {
                projectileViz.IsEnabled = enableProjectileVisualization;
                projectileViz.ShowProjectiles = showProjectiles;
                projectileViz.ShowProjectileTrajectories = showProjectileTrajectories;
                projectileViz.ShowCollisionAreas = showCollisionAreas;
                projectileViz.ShowVelocityVectors = showVelocityVectors;
            }
        }
        
        private void HandleInput()
        {
            // Toggle all visualizations
            if (Input.GetKeyDown(toggleAllKey))
            {
                ToggleAllVisualizations();
            }
            
            // Toggle individual visualizations
            if (Input.GetKeyDown(toggleGridKey))
            {
                enableGridVisualization = !enableGridVisualization;
                Debug.Log($"Grid Visualization: {(enableGridVisualization ? "ON" : "OFF")}");
            }
            
            if (Input.GetKeyDown(toggleUnitsKey))
            {
                enableUnitVisualization = !enableUnitVisualization;
                Debug.Log($"Unit Visualization: {(enableUnitVisualization ? "ON" : "OFF")}");
            }
            
            if (Input.GetKeyDown(toggleTargetsKey))
            {
                enableTargetVisualization = !enableTargetVisualization;
                Debug.Log($"Target Visualization: {(enableTargetVisualization ? "ON" : "OFF")}");
            }
            
            if (Input.GetKeyDown(togglePathsKey))
            {
                enableUnitPathVisualization = !enableUnitPathVisualization;
                Debug.Log($"Unit Path Visualization: {(enableUnitPathVisualization ? "ON" : "OFF")}");
            }
            
            if (Input.GetKeyDown(toggleProjectilesKey))
            {
                enableProjectileVisualization = !enableProjectileVisualization;
                Debug.Log($"Projectile Visualization: {(enableProjectileVisualization ? "ON" : "OFF")}");
            }
            
            // Show detailed information
            if (Input.GetKeyDown(showInfoKey))
            {
                ShowDetailedInfo();
            }
        }
        
        private void ToggleAllVisualizations()
        {
            allEnabled = !allEnabled;
            
            enableGridVisualization = allEnabled;
            enableUnitVisualization = allEnabled;
            enableTargetVisualization = allEnabled;
            enableUnitPathVisualization = allEnabled;
            enableProjectileVisualization = allEnabled;
            
            Debug.Log($"All Visualizations: {(allEnabled ? "ON" : "OFF")}");
        }
        
        private void ShowDetailedInfo()
        {
            Debug.Log("=== VISUALIZATION SYSTEM STATUS ===");
            
            foreach (var component in allComponents)
            {
                if (component != null)
                {
                    Debug.Log($"{component.VisualizationName}: {(component.IsEnabled ? "ENABLED" : "DISABLED")}");
                    Debug.Log($"  Details: {component.GetDetailedInfo()}");
                }
            }
            
            Debug.Log("=== END STATUS ===");
        }
        
        private void ShowInstructions()
        {
            Debug.Log("=== VISUALIZATION SYSTEM DEMO ===");
            Debug.Log("This demo showcases the new modular visualization system benefits:");
            Debug.Log("1. EASY ENABLE/DISABLE: Each visualization type can be toggled independently");
            Debug.Log("2. DETAILED CONFIGURATION: Fine-grained control over what each component shows");
            Debug.Log("3. RUNTIME CONTROL: Change settings in inspector or via code during play");
            Debug.Log("4. DETAILED INFO: Each component provides status and debug information");
            Debug.Log("");
            Debug.Log("CONTROLS:");
            Debug.Log($"  {toggleAllKey} - Toggle all visualizations");
            Debug.Log($"  {toggleGridKey} - Toggle grid visualization");
            Debug.Log($"  {toggleUnitsKey} - Toggle unit visualization");
            Debug.Log($"  {toggleTargetsKey} - Toggle target visualization");
            Debug.Log($"  {togglePathsKey} - Toggle unit path visualization");
            Debug.Log($"  {toggleProjectilesKey} - Toggle projectile visualization");
            Debug.Log($"  {showInfoKey} - Show detailed component information");
            Debug.Log("");
            Debug.Log("You can also adjust settings in the inspector during play for real-time tweaking!");
            Debug.Log("=== END DEMO INFO ===");
        }
        
        void OnGUI()
        {
            // Simple on-screen display
            var rect = new Rect(10, 10, 300, 200);
            GUI.Box(rect, "Visualization Demo");
            
            var y = 30;
            GUI.Label(new Rect(20, y, 280, 20), $"Grid: {(enableGridVisualization ? "ON" : "OFF")} (Press {toggleGridKey})");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Units: {(enableUnitVisualization ? "ON" : "OFF")} (Press {toggleUnitsKey})");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Targets: {(enableTargetVisualization ? "ON" : "OFF")} (Press {toggleTargetsKey})");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Paths: {(enableUnitPathVisualization ? "ON" : "OFF")} (Press {togglePathsKey})");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Projectiles: {(enableProjectileVisualization ? "ON" : "OFF")} (Press {toggleProjectilesKey})");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Press {toggleAllKey} to toggle all");
            y += 20;
            GUI.Label(new Rect(20, y, 280, 20), $"Press {showInfoKey} for detailed info");
        }
    }
}
