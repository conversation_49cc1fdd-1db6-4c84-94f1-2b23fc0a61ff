﻿using Avalon.Visualization;
using FlowField.Core;
using Unity.Entities;
using Unity.Transforms;
using UnityEngine;

namespace FlowField.Visualization
{
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct GameObjectTransformSyncSystem : ISystem
    {
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<LocalTransform>();
            state.RequireForUpdate<GameObjectRef>();
        }

        public void OnUpdate(ref SystemState state)
        {
            Debug.Log("GameObjectTransformSyncSystem");
            foreach (var (localTransform, gameObjectRef, entity) in SystemAPI
                         .Query<RefRO<LocalTransform>, RefRO<GameObjectRef>>()
                         .WithChangeFilter<LocalTransform>()
                         .WithEntityAccess())
            {
                Debug.Log($"Attempting to update transform for Entity {entity}");
                var gameObject = GameObjectPool.Instance.GetById(gameObjectRef.ValueRO.gameObjectId);
                if (gameObject == null)
                {
                    Debug.Log($"Entity {entity} has no GameObjectRef component or the ref is null");
                    continue;
                }
                
                var transform = gameObject.transform;
                if (transform != null)
                {
                    Debug.Log($"Update transform for Entity {entity}");
                    transform.position = localTransform.ValueRO.Position;
                    transform.rotation = localTransform.ValueRO.Rotation;
                    transform.localScale = Vector3.one * localTransform.ValueRO.Scale;
                }
            }
        }
    }
}