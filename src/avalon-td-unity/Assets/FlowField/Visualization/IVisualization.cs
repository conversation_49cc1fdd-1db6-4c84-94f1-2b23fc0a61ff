using Unity.Entities;
using UnityEngine;

namespace FlowField.Visualization
{
    /// <summary>
    /// Interface for all visualization components in the FlowField system.
    /// Provides common functionality for enable/disable, detailed information display, and gizmo drawing.
    /// </summary>
    public interface IVisualization
    {
        /// <summary>
        /// Whether this visualization component is enabled and should draw gizmos
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// Whether to show detailed information about what this component visualizes
        /// </summary>
        bool ShowDetailedInfo { get; set; }
        
        /// <summary>
        /// The name of this visualization component for UI display
        /// </summary>
        string VisualizationName { get; }
        
        /// <summary>
        /// Initialize the visualization component with required dependencies
        /// </summary>
        /// <param name="entityManager">The EntityManager for accessing ECS data</param>
        /// <param name="world">The World instance</param>
        void Initialize(EntityManager entityManager, World world);
        
        /// <summary>
        /// Draw gizmos for this visualization component
        /// Called from OnDrawGizmos
        /// </summary>
        void DrawGizmos();
        
        /// <summary>
        /// Get detailed information about what this component is currently visualizing
        /// Used for debug display and inspector information
        /// </summary>
        /// <returns>String containing detailed information</returns>
        string GetDetailedInfo();
        
        /// <summary>
        /// Cleanup any resources used by this visualization component
        /// </summary>
        void Cleanup();
    }
}
