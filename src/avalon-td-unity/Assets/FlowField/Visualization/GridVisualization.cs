using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Deterministic.Mathematics;

namespace FlowField.Visualization
{
    /// <summary>
    /// Visualization component for flow field grids.
    /// Handles grid lines, flow directions, costs, walkability, and obstacles.
    /// </summary>
    public class GridVisualization : BaseVisualization
    {
        [Header("Grid Display Options")]
        [SerializeField] private bool showGrid = true;
        [SerializeField] private bool showDirections = true;
        [SerializeField] private bool showCosts = false;
        [SerializeField] private bool showWalkability = true;
        
        [Header("Grid Colors")]
        [SerializeField] private Color gridColor = Color.white;
        [SerializeField] private Color directionColor = Color.green;
        [SerializeField] private Color obstacleColor = Color.red;
        [SerializeField] private Color costColor = Color.red;
        
        [Header("Grid Properties")]
        [SerializeField] private float arrowScale = 0.8f;
        [SerializeField] private float gridAlpha = 0.3f;
        [SerializeField] private float costAlphaMultiplier = 0.5f;
        
        [Header("Grid Configuration")]
        [SerializeField] private Entity flowFieldEntity;
        [SerializeField] private int2 gridSize = new int2(20, 20);
        [SerializeField] private dfloat2 cellSize = new dfloat2(dfloat.One, dfloat.One);
        [SerializeField] private dfloat2 worldOrigin = new dfloat2(-(dfloat)10.0f, -(dfloat)10.0f);
        [SerializeField] private MovementType movementType = MovementType.Ground;
        
        private DynamicBuffer<FlowFieldCellBuffer> flowFieldBuffer;
        private int lastCellCount = 0;
        
        public override string VisualizationName => "Grid Visualization";
        
        // Properties for external configuration
        public Entity FlowFieldEntity 
        { 
            get => flowFieldEntity; 
            set => flowFieldEntity = value; 
        }
        
        public int2 GridSize 
        { 
            get => gridSize; 
            set => gridSize = value; 
        }
        
        public dfloat2 CellSize 
        { 
            get => cellSize; 
            set => cellSize = value; 
        }
        
        public dfloat2 WorldOrigin 
        { 
            get => worldOrigin; 
            set => worldOrigin = value; 
        }
        
        public MovementType MovementType 
        { 
            get => movementType; 
            set => movementType = value; 
        }
        
        public bool ShowGrid 
        { 
            get => showGrid; 
            set => showGrid = value; 
        }
        
        public bool ShowDirections 
        { 
            get => showDirections; 
            set => showDirections = value; 
        }
        
        public bool ShowCosts 
        { 
            get => showCosts; 
            set => showCosts = value; 
        }
        
        public bool ShowWalkability 
        { 
            get => showWalkability; 
            set => showWalkability = value; 
        }
        
        protected override void OnInitialize()
        {
            // Try to get the flow field buffer if entity is set
            RefreshFlowFieldBuffer();
        }
        
        protected override void OnDrawGizmos()
        {
            if (!ValidateFlowFieldData())
                return;
                
            if (showGrid)
            {
                DrawGridLines();
            }
            
            DrawCellContents();
        }
        
        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Grid Visualization not initialized";
                
            if (!IsEntityValid(flowFieldEntity))
                return "Flow Field Entity is invalid";
                
            if (!entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity))
                return "Flow Field Entity missing FlowFieldCellBuffer";
                
            var buffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
            var cellCount = buffer.Length;
            var expectedCells = gridSize.x * gridSize.y;
            
            var walkableCells = 0;
            var targetCells = 0;
            var cellsWithDirection = 0;
            
            for (int i = 0; i < cellCount; i++)
            {
                var cell = buffer[i].cell;
                if (cell.IsWalkableFor(movementType)) walkableCells++;
                if (cell.isTarget) targetCells++;
                if (cell.direction.x != dfloat.Zero || cell.direction.y != dfloat.Zero) cellsWithDirection++;
            }
            
            return $"Grid: {gridSize.x}x{gridSize.y} ({cellCount}/{expectedCells} cells)\n" +
                   $"Movement Type: {movementType}\n" +
                   $"Walkable: {walkableCells}, Targets: {targetCells}, With Direction: {cellsWithDirection}\n" +
                   $"Cell Size: {cellSize}, Origin: {worldOrigin}";
        }
        
        private bool ValidateFlowFieldData()
        {
            if (!IsEntityValid(flowFieldEntity))
                return false;
                
            if (!entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity))
                return false;
                
            RefreshFlowFieldBuffer();
            return flowFieldBuffer.IsCreated && flowFieldBuffer.Length > 0;
        }
        
        private void RefreshFlowFieldBuffer()
        {
            if (IsEntityValid(flowFieldEntity) && entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity))
            {
                flowFieldBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
            }
        }
        
        private void DrawGridLines()
        {
            Gizmos.color = ApplyAlpha(gridColor, gridAlpha);

            // Vertical lines (running along Z-axis)
            for (int x = 0; x <= gridSize.x; x++)
            {
                var worldX = (float)worldOrigin.x + x * (float)cellSize.x;
                var startZ = (float)worldOrigin.y;
                var endZ = (float)worldOrigin.y + gridSize.y * (float)cellSize.y;

                Gizmos.DrawLine(
                    new Vector3(worldX, 0, startZ),
                    new Vector3(worldX, 0, endZ)
                );
            }

            // Horizontal lines (running along X-axis)
            for (int y = 0; y <= gridSize.y; y++)
            {
                var worldZ = (float)worldOrigin.y + y * (float)cellSize.y;
                var startX = (float)worldOrigin.x;
                var endX = (float)worldOrigin.x + gridSize.x * (float)cellSize.x;

                Gizmos.DrawLine(
                    new Vector3(startX, 0, worldZ),
                    new Vector3(endX, 0, worldZ)
                );
            }
        }
        
        private void DrawCellContents()
        {
            for (int y = 0; y < gridSize.y; y++)
            {
                for (int x = 0; x < gridSize.x; x++)
                {
                    var index = y * gridSize.x + x;
                    if (index >= flowFieldBuffer.Length) continue;
                    
                    var cell = flowFieldBuffer[index].cell;
                    var cellCenter = GetCellCenter(x, y);
                    
                    DrawObstacles(cell, cellCenter);
                    DrawFlowDirections(cell, cellCenter);
                    DrawCosts(cell, cellCenter);
                }
            }
        }
        
        private Vector3 GetCellCenter(int x, int y)
        {
            return new Vector3(
                (float)worldOrigin.x + (x + 0.5f) * (float)cellSize.x,
                0,
                (float)worldOrigin.y + (y + 0.5f) * (float)cellSize.y
            );
        }
        
        private void DrawObstacles(FlowFieldCell cell, Vector3 cellCenter)
        {
            if (showWalkability && !cell.IsWalkableFor(movementType))
            {
                Gizmos.color = ApplyAlpha(obstacleColor);
                Gizmos.DrawCube(cellCenter,
                    new Vector3((float)cellSize.x * 0.9f, 1.0f, (float)cellSize.y * 0.9f));
            }
        }
        
        private void DrawFlowDirections(FlowFieldCell cell, Vector3 cellCenter)
        {
            if (showDirections && (cell.direction.x != dfloat.Zero || cell.direction.y != dfloat.Zero))
            {
                Gizmos.color = ApplyAlpha(directionColor);
                // Map flow field direction (x,y) to world direction (x,z)
                var direction = new Vector3((float)cell.direction.x, 0, (float)cell.direction.y);
                var arrowEnd = cellCenter + direction * (float)cellSize.x * arrowScale * 0.4f;

                Gizmos.DrawLine(cellCenter, arrowEnd);

                // Draw arrowhead using Vector3.up as the perpendicular axis for x/z plane
                var arrowHead1 = arrowEnd - direction * 0.2f + Vector3.Cross(direction, Vector3.up) * 0.1f;
                var arrowHead2 = arrowEnd - direction * 0.2f - Vector3.Cross(direction, Vector3.up) * 0.1f;

                Gizmos.DrawLine(arrowEnd, arrowHead1);
                Gizmos.DrawLine(arrowEnd, arrowHead2);
            }
        }
        
        private void DrawCosts(FlowFieldCell cell, Vector3 cellCenter)
        {
            if (showCosts)
            {
                var costAlpha = Mathf.Clamp01((float)cell.cost - 1.0f);
                if (costAlpha > 0)
                {
                    Gizmos.color = ApplyAlpha(costColor, costAlpha * costAlphaMultiplier);
                    Gizmos.DrawCube(cellCenter,
                        new Vector3((float)cellSize.x * 0.8f, 0.05f, (float)cellSize.y * 0.8f));
                }
            }
        }
    }
}
