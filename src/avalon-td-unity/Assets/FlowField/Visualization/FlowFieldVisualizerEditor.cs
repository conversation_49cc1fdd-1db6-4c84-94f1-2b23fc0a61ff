﻿// #if UNITY_EDITOR
// using UnityEditor;
// using UnityEngine;
//
// namespace FlowField
// {
//
//     [CustomEditor(typeof(FlowFieldVisualizer))]
//     public class FlowFieldVisualizerEditor : Editor
//     {
//         public override void OnInspectorGUI()
//         {
//             DrawDefaultInspector();
//
//             FlowFieldVisualizer visualizer = (FlowFieldVisualizer)target;
//
//             EditorGUILayout.Space();
//             EditorGUILayout.LabelField("Runtime Controls", EditorStyles.boldLabel);
//
//             if (GUILayout.Button("Regenerate Flow Field"))
//             {
//                 if (Application.isPlaying)
//                 {
//                     visualizer.ForceFlowFieldUpdate();
//                 }
//             }
//
//             if (GUILayout.Button("Recreate Test Units"))
//             {
//                 if (Application.isPlaying)
//                 {
//                     // Clean up existing units
//                     foreach (var unit in visualizer.testUnits)
//                     {
//                         if (visualizer.entityManager.Exists(unit))
//                             visualizer.entityManager.DestroyEntity(unit);
//                     }
//
//                     // Create new units
//                     visualizer.CreateTestUnits();
//                 }
//             }
//
//             if (GUILayout.Button("Move Target to Center"))
//             {
//                 if (Application.isPlaying)
//                 {
//                     visualizer.MoveTarget(Vector3.zero);
//                 }
//             }
//
//             EditorGUILayout.Space();
//
//             // Show validation status
//             if (Application.isPlaying)
//             {
//                 bool isValid = visualizer.IsValid();
//                 EditorGUILayout.LabelField("Status", isValid ? "Valid" : "Invalid",
//                     isValid ? EditorStyles.label : EditorStyles.boldLabel);
//
//                 if (!isValid)
//                 {
//                     EditorGUILayout.HelpBox("FlowFieldVisualizer is not properly initialized!", MessageType.Warning);
//                 }
//
//                 // Show debug info
//                 if (GUILayout.Button("Show Debug Info"))
//                 {
//                     Debug.Log(visualizer.GetDebugInfo());
//                 }
//
//                 if (GUILayout.Button("Test Flow Field at Origin"))
//                 {
//                     Debug.Log(visualizer.TestFlowFieldSampling(Vector3.zero));
//                 }
//
//                 if (GUILayout.Button("Test Flow Field at (-5, -5)"))
//                 {
//                     Debug.Log(visualizer.TestFlowFieldSampling(new Vector3(-5, -5, 0)));
//                 }
//
//                 EditorGUILayout.Space();
//                 EditorGUILayout.LabelField("Speed Effect Testing", EditorStyles.boldLabel);
//
//                 if (GUILayout.Button("Apply Slow to All Units"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         visualizer.ApplySlowToAllUnits();
//                     }
//                 }
//
//                 if (GUILayout.Button("Apply Haste to All Units"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         visualizer.ApplyHasteToAllUnits();
//                     }
//                 }
//
//                 if (GUILayout.Button("Clear All Speed Effects"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         visualizer.ClearAllSpeedEffects();
//                     }
//                 }
//
//                 if (GUILayout.Button("Show Speed Modifier Info"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         Debug.Log(visualizer.GetSpeedModifierInfo());
//                     }
//                 }
//
//                 EditorGUILayout.Space();
//                 EditorGUILayout.LabelField("Fixed Timestep Testing", EditorStyles.boldLabel);
//
//                 if (GUILayout.Button("Show Fixed Timestep Info"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         Debug.Log(visualizer.GetFixedTimestepInfo());
//                     }
//                 }
//
//                 if (GUILayout.Button("Show Interpolation Info"))
//                 {
//                     if (Application.isPlaying)
//                     {
//                         Debug.Log(visualizer.GetInterpolationInfo());
//                     }
//                 }
//
//                 EditorGUILayout.Space();
//                 EditorGUILayout.LabelField("Avoidance Tuning", EditorStyles.boldLabel);
//
//                 // Target proximity threshold slider
//                 EditorGUI.BeginChangeCheck();
//                 float newThreshold = EditorGUILayout.Slider("Target Proximity Threshold",
//                     visualizer.targetProximityThreshold, 0.5f, 5.0f);
//                 if (EditorGUI.EndChangeCheck())
//                 {
//                     visualizer.UpdateTargetProximityThreshold(newThreshold);
//                 }
//
//                 EditorGUILayout.HelpBox(
//                     "Units will disable avoidance forces when within this distance of their target. This prevents circling behavior.",
//                     MessageType.Info);
//             }
//
//             EditorGUILayout.Space();
//             EditorGUILayout.HelpBox("Left click and drag in Scene view to move the target at runtime.",
//                 MessageType.Info);
//         }
//     }
// }
// #endif