using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Transforms;
using Unity.Deterministic.Mathematics;
using System.Collections.Generic;
using Avalon.Simulation.Movement;

namespace FlowField.Visualization
{
    /// <summary>
    /// Visualization component for units in the flow field system.
    /// Handles unit rendering, velocity vectors, desired directions, and avoidance forces.
    /// </summary>
    public class UnitVisualization : BaseVisualization
    {
        [Header("Unit Display Options")]
        [SerializeField] private bool showUnits = true;
        [SerializeField] private bool showUnitVelocity = true;
        [SerializeField] private bool showUnitDesiredDirection = true;
        [SerializeField] private bool showAvoidanceForces = true;
        [SerializeField] private bool showProximityThreshold = true;
        [SerializeField] private bool showAvoidanceRadius = true;
        
        [Header("Unit Colors")]
        [SerializeField] private Color unitColor = Color.blue;
        [SerializeField] private Color nearTargetColor = Color.green;
        [SerializeField] private Color velocityColor = Color.cyan;
        [SerializeField] private Color desiredDirectionColor = Color.magenta;
        [SerializeField] private Color avoidanceColor = Color.orange;
        [SerializeField] private Color proximityColor = Color.green;
        
        [Header("Unit Properties")]
        [SerializeField] private float unitSize = 0.5f;
        [SerializeField] private float velocityScale = 1.0f;
        [SerializeField] private float proximityAlpha = 0.2f;
        [SerializeField] private float avoidanceAlpha = 0.1f;
        
        [Header("Unit Configuration")]
        [SerializeField] private List<Entity> unitsToVisualize = new List<Entity>();
        [SerializeField] private Entity targetEntity = Entity.Null;
        [SerializeField] private bool autoFindUnits = true;
        [SerializeField] private bool showNonAvoidanceUnits = true;
        
        private EntityQuery unitQuery;
        private int lastUnitCount = 0;
        
        public override string VisualizationName => "Unit Visualization";
        
        // Properties for external configuration
        public List<Entity> UnitsToVisualize 
        { 
            get => unitsToVisualize; 
            set => unitsToVisualize = value; 
        }
        
        public Entity TargetEntity 
        { 
            get => targetEntity; 
            set => targetEntity = value; 
        }
        
        public bool ShowUnits 
        { 
            get => showUnits; 
            set => showUnits = value; 
        }
        
        public bool ShowUnitVelocity 
        { 
            get => showUnitVelocity; 
            set => showUnitVelocity = value; 
        }
        
        public bool ShowUnitDesiredDirection 
        { 
            get => showUnitDesiredDirection; 
            set => showUnitDesiredDirection = value; 
        }
        
        public bool ShowAvoidanceForces 
        { 
            get => showAvoidanceForces; 
            set => showAvoidanceForces = value; 
        }
        
        public bool AutoFindUnits 
        { 
            get => autoFindUnits; 
            set => autoFindUnits = value; 
        }
        
        protected override void OnInitialize()
        {
            if (autoFindUnits)
            {
                CreateUnitQuery();
            }
        }
        
        protected override void OnDrawGizmos()
        {
            if (!showUnits)
                return;
                
            if (autoFindUnits)
            {
                DrawUnitsFromQuery();
            }
            else
            {
                DrawUnitsFromList();
            }
        }
        
        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Unit Visualization not initialized";
                
            var unitCount = autoFindUnits ? GetUnitCountFromQuery() : unitsToVisualize.Count;
            var avoidanceUnitCount = autoFindUnits ? GetAvoidanceUnitCountFromQuery() : GetAvoidanceUnitCountFromList();
            var targetInfo = IsEntityValid(targetEntity) ? "Valid" : "Invalid/None";
            
            return $"Units: {unitCount} (Avoidance: {avoidanceUnitCount})\n" +
                   $"Target: {targetInfo}\n" +
                   $"Auto Find: {autoFindUnits}\n" +
                   $"Show: Velocity={showUnitVelocity}, Direction={showUnitDesiredDirection}, Avoidance={showAvoidanceForces}";
        }
        
        private void CreateUnitQuery()
        {
            unitQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldFollower)
            );
        }
        
        private void DrawUnitsFromQuery()
        {
            if (unitQuery.IsEmpty)
                CreateUnitQuery();
                
            var entities = unitQuery.ToEntityArray(Allocator.Temp);
            
            try
            {
                foreach (var unit in entities)
                {
                    DrawUnit(unit);
                }
            }
            finally
            {
                entities.Dispose();
            }
        }
        
        private void DrawUnitsFromList()
        {
            foreach (var unit in unitsToVisualize)
            {
                if (IsEntityValid(unit))
                {
                    DrawUnit(unit);
                }
            }
        }
        
        private void DrawUnit(Entity unit)
        {
            if (!entityManager.HasComponent<LocalTransform>(unit) || 
                !entityManager.HasComponent<FlowFieldFollower>(unit))
                return;
                
            var transform = entityManager.GetComponentData<LocalTransform>(unit);
            var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
            var position = transform.Position;
            
            // Check if unit is near target
            bool isNearTarget = IsUnitNearTarget(position, follower);
            
            // Draw basic unit representation
            DrawBasicUnit(position, isNearTarget);
            
            // Draw proximity threshold
            if (showProximityThreshold && follower.useAvoidance)
            {
                DrawProximityThreshold(position, follower);
            }
            
            // Draw avoidance-specific visualizations
            if (follower.useAvoidance && entityManager.HasComponent<AvoidanceData>(unit))
            {
                DrawAvoidanceUnit(unit, position, follower);
            }
            else if (showNonAvoidanceUnits)
            {
                DrawNonAvoidanceUnit(position);
            }
        }
        
        private bool IsUnitNearTarget(float3 unitPosition, FlowFieldFollower follower)
        {
            if (!IsEntityValid(targetEntity))
                return false;

            if (!entityManager.HasComponent<LocalTransform>(targetEntity))
                return false;

            var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);
            // Calculate distance in x/z plane (horizontal movement plane) to match movement system
            var distanceToTarget = math.distance(unitPosition.xz, targetTransform.Position.xz);
            return distanceToTarget < (float)follower.targetProximityThreshold;
        }
        
        private void DrawBasicUnit(float3 position, bool isNearTarget)
        {
            Gizmos.color = ApplyAlpha(isNearTarget ? nearTargetColor : unitColor);
            Gizmos.DrawWireSphere(position, unitSize);
        }
        
        private void DrawProximityThreshold(float3 position, FlowFieldFollower follower)
        {
            Gizmos.color = ApplyAlpha(proximityColor, proximityAlpha);
            Gizmos.DrawWireSphere(position, (float)follower.targetProximityThreshold);
        }
        
        private void DrawAvoidanceUnit(Entity unit, float3 position, FlowFieldFollower follower)
        {
            var avoidance = entityManager.GetComponentData<AvoidanceData>(unit);
            
            // Draw velocity vector
            if (showUnitVelocity)
            {
                DrawVelocityVector(position, avoidance);
            }
            
            // Draw desired direction
            if (showUnitDesiredDirection)
            {
                DrawDesiredDirection(position, avoidance);
            }
            
            // Draw avoidance radius
            if (showAvoidanceRadius && showAvoidanceForces)
            {
                DrawAvoidanceRadius(position, follower);
            }
        }
        
        private void DrawVelocityVector(float3 position, AvoidanceData avoidance)
        {
            Gizmos.color = ApplyAlpha(velocityColor);
            // Map 2D velocity (x,z movement) to 3D world coordinates (x,0,z)
            var velocityEnd = position + new float3((float)avoidance.velocity.x, 0, (float)avoidance.velocity.y) * velocityScale;
            Gizmos.DrawLine(position, velocityEnd);
            Gizmos.DrawWireSphere(velocityEnd, 0.1f);
        }
        
        private void DrawDesiredDirection(float3 position, AvoidanceData avoidance)
        {
            Gizmos.color = ApplyAlpha(desiredDirectionColor);
            // Map 2D desired direction (x,z movement) to 3D world coordinates (x,0,z)
            var desiredEnd = position + new float3((float)avoidance.desiredDirection.x, 0, (float)avoidance.desiredDirection.y) * velocityScale;
            Gizmos.DrawLine(position, desiredEnd);
        }
        
        private void DrawAvoidanceRadius(float3 position, FlowFieldFollower follower)
        {
            Gizmos.color = ApplyAlpha(avoidanceColor, avoidanceAlpha);
            Gizmos.DrawWireSphere(position, (float)follower.avoidanceRadius);
        }
        
        private void DrawNonAvoidanceUnit(float3 position)
        {
            Gizmos.color = ApplyAlpha(Color.white);
            Gizmos.DrawWireSphere(position, unitSize * 0.8f);
        }
        
        private int GetUnitCountFromQuery()
        {
            if (unitQuery.IsEmpty)
                return 0;
            return unitQuery.CalculateEntityCount();
        }
        
        private int GetAvoidanceUnitCountFromQuery()
        {
            if (!isInitialized)
                return 0;
                
            var avoidanceQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldFollower),
                typeof(AvoidanceData)
            );
            
            var count = avoidanceQuery.CalculateEntityCount();
            avoidanceQuery.Dispose();
            return count;
        }
        
        private int GetAvoidanceUnitCountFromList()
        {
            int count = 0;
            foreach (var unit in unitsToVisualize)
            {
                if (IsEntityValid(unit) && 
                    entityManager.HasComponent<AvoidanceData>(unit))
                {
                    count++;
                }
            }
            return count;
        }
        
        protected override void OnCleanup()
        {
            if (!unitQuery.IsEmpty)
            {
                unitQuery.Dispose();
            }
        }
        
        /// <summary>
        /// Add a unit to the visualization list (when not using auto-find)
        /// </summary>
        public void AddUnit(Entity unit)
        {
            if (!unitsToVisualize.Contains(unit))
            {
                unitsToVisualize.Add(unit);
            }
        }
        
        /// <summary>
        /// Remove a unit from the visualization list
        /// </summary>
        public void RemoveUnit(Entity unit)
        {
            unitsToVisualize.Remove(unit);
        }
        
        /// <summary>
        /// Clear all units from the visualization list
        /// </summary>
        public void ClearUnits()
        {
            unitsToVisualize.Clear();
        }
    }
}
