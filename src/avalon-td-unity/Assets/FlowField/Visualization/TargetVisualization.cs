using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Transforms;
using Unity.Deterministic.Mathematics;
using System.Collections.Generic;

namespace FlowField.Visualization
{
    /// <summary>
    /// Visualization component for targets in the flow field system.
    /// Handles target rendering and target-related visual information.
    /// </summary>
    public class TargetVisualization : BaseVisualization
    {
        [Header("Target Display Options")]
        [SerializeField] private bool showTargets = true;
        [SerializeField] private bool showTargetInfo = true;
        [SerializeField] private bool showTargetRange = false;
        [SerializeField] private bool showTargetConnections = false;
        
        [Header("Target Colors")]
        [SerializeField] private Color targetColor = Color.yellow;
        [SerializeField] private Color activeTargetColor = Color.red;
        [SerializeField] private Color inactiveTargetColor = Color.gray;
        [SerializeField] private Color targetRangeColor = Color.yellow;
        [SerializeField] private Color connectionColor = Color.white;
        
        [Header("Target Properties")]
        [SerializeField] private float targetOuterRadius = 0.8f;
        [SerializeField] private float targetInnerRadius = 0.5f;
        [SerializeField] private float targetRangeRadius = 5.0f;
        [SerializeField] private float connectionAlpha = 0.3f;
        [SerializeField] private float rangeAlpha = 0.1f;
        
        [Header("Target Configuration")]
        [SerializeField] private List<Entity> targetsToVisualize = new List<Entity>();
        [SerializeField] private bool autoFindTargets = true;
        [SerializeField] private bool showInactiveTargets = true;
        [SerializeField] private Entity connectedUnit = Entity.Null;
        
        private EntityQuery targetQuery;
        private int lastTargetCount = 0;
        
        public override string VisualizationName => "Target Visualization";
        
        // Properties for external configuration
        public List<Entity> TargetsToVisualize 
        { 
            get => targetsToVisualize; 
            set => targetsToVisualize = value; 
        }
        
        public Entity ConnectedUnit 
        { 
            get => connectedUnit; 
            set => connectedUnit = value; 
        }
        
        public bool ShowTargets 
        { 
            get => showTargets; 
            set => showTargets = value; 
        }
        
        public bool ShowTargetInfo 
        { 
            get => showTargetInfo; 
            set => showTargetInfo = value; 
        }
        
        public bool ShowTargetRange 
        { 
            get => showTargetRange; 
            set => showTargetRange = value; 
        }
        
        public bool ShowTargetConnections 
        { 
            get => showTargetConnections; 
            set => showTargetConnections = value; 
        }
        
        public bool AutoFindTargets 
        { 
            get => autoFindTargets; 
            set => autoFindTargets = value; 
        }
        
        protected override void OnInitialize()
        {
            if (autoFindTargets)
            {
                CreateTargetQuery();
            }
        }
        
        protected override void OnDrawGizmos()
        {
            if (!showTargets)
                return;
                
            if (autoFindTargets)
            {
                DrawTargetsFromQuery();
            }
            else
            {
                DrawTargetsFromList();
            }
        }
        
        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Target Visualization not initialized";
                
            var targetCount = autoFindTargets ? GetTargetCountFromQuery() : targetsToVisualize.Count;
            var activeTargetCount = autoFindTargets ? GetActiveTargetCountFromQuery() : GetActiveTargetCountFromList();
            var connectedUnitInfo = IsEntityValid(connectedUnit) ? "Valid" : "None";
            
            return $"Targets: {targetCount} (Active: {activeTargetCount})\n" +
                   $"Connected Unit: {connectedUnitInfo}\n" +
                   $"Auto Find: {autoFindTargets}\n" +
                   $"Show: Info={showTargetInfo}, Range={showTargetRange}, Connections={showTargetConnections}";
        }
        
        private void CreateTargetQuery()
        {
            targetQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldTarget)
            );
        }
        
        private void DrawTargetsFromQuery()
        {
            if (targetQuery.IsEmpty)
                CreateTargetQuery();
                
            var entities = targetQuery.ToEntityArray(Allocator.Temp);
            
            try
            {
                foreach (var target in entities)
                {
                    DrawTarget(target);
                }
            }
            finally
            {
                entities.Dispose();
            }
        }
        
        private void DrawTargetsFromList()
        {
            foreach (var target in targetsToVisualize)
            {
                if (IsEntityValid(target))
                {
                    DrawTarget(target);
                }
            }
        }
        
        private void DrawTarget(Entity target)
        {
            if (!entityManager.HasComponent<LocalTransform>(target) || 
                !entityManager.HasComponent<FlowFieldTarget>(target))
                return;
                
            var transform = entityManager.GetComponentData<LocalTransform>(target);
            var targetData = entityManager.GetComponentData<FlowFieldTarget>(target);
            var position = transform.Position;
            
            // Skip inactive targets if not showing them
            if (!targetData.isActive && !showInactiveTargets)
                return;
            
            // Draw basic target representation
            DrawBasicTarget(position, targetData);
            
            // Draw target range
            if (showTargetRange)
            {
                DrawTargetRange(position, targetData);
            }
            
            // Draw connection to unit
            if (showTargetConnections && IsEntityValid(connectedUnit))
            {
                DrawTargetConnection(position, targetData);
            }
        }
        
        private void DrawBasicTarget(float3 position, FlowFieldTarget targetData)
        {
            Color currentTargetColor = targetData.isActive ? activeTargetColor : inactiveTargetColor;
            
            // Draw outer wire sphere
            Gizmos.color = ApplyAlpha(currentTargetColor);
            Gizmos.DrawWireSphere(position, targetOuterRadius);
            
            // Draw inner solid sphere
            Gizmos.color = ApplyAlpha(currentTargetColor, 0.8f);
            Gizmos.DrawSphere(position, targetInnerRadius);
            
            // Draw target ID if showing info
            if (showTargetInfo)
            {
                DrawTargetInfo(position, targetData);
            }
        }
        
        private void DrawTargetInfo(float3 position, FlowFieldTarget targetData)
        {
            // Draw a small indicator for target ID
            var offset = new float3(0, targetOuterRadius + 0.3f, 0);
            Gizmos.color = ApplyAlpha(Color.white);
            Gizmos.DrawWireCube(position + offset, Vector3.one * 0.2f);
            
            // Could be enhanced to draw actual text in scene view
            // For now, we rely on the detailed info system
        }
        
        private void DrawTargetRange(float3 position, FlowFieldTarget targetData)
        {
            Gizmos.color = ApplyAlpha(targetRangeColor, rangeAlpha);
            Gizmos.DrawWireSphere(position, targetRangeRadius);
        }
        
        private void DrawTargetConnection(float3 position, FlowFieldTarget targetData)
        {
            if (!entityManager.HasComponent<LocalTransform>(connectedUnit))
                return;
                
            var unitTransform = entityManager.GetComponentData<LocalTransform>(connectedUnit);
            
            // Check if this unit is targeting this specific target
            if (entityManager.HasComponent<MovementTarget>(connectedUnit))
            {
                var movementTarget = entityManager.GetComponentData<MovementTarget>(connectedUnit);
                if (movementTarget.targetId == targetData.targetId)
                {
                    Gizmos.color = ApplyAlpha(connectionColor, connectionAlpha);
                    Gizmos.DrawLine(unitTransform.Position, position);
                    
                    // Draw direction arrow
                    var direction = math.normalize(position - unitTransform.Position);
                    var arrowPos = math.lerp(unitTransform.Position, position, 0.7f);
                    DrawArrow(arrowPos, direction, 0.5f);
                }
            }
        }
        
        private void DrawArrow(float3 position, float3 direction, float size)
        {
            var arrowEnd = position + direction * size;
            Gizmos.DrawLine(position, arrowEnd);
            
            // Draw arrowhead
            var perpendicular = new float3(-direction.y, direction.x, 0) * size * 0.3f;
            var arrowHead1 = arrowEnd - direction * size * 0.3f + perpendicular;
            var arrowHead2 = arrowEnd - direction * size * 0.3f - perpendicular;
            
            Gizmos.DrawLine(arrowEnd, arrowHead1);
            Gizmos.DrawLine(arrowEnd, arrowHead2);
        }
        
        private int GetTargetCountFromQuery()
        {
            if (targetQuery.IsEmpty)
                return 0;
            return targetQuery.CalculateEntityCount();
        }
        
        private int GetActiveTargetCountFromQuery()
        {
            if (!isInitialized)
                return 0;
                
            var activeQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldTarget)
            );
            
            // Filter for active targets
            var entities = activeQuery.ToEntityArray(Allocator.Temp);
            int activeCount = 0;
            
            try
            {
                foreach (var entity in entities)
                {
                    if (entityManager.HasComponent<FlowFieldTarget>(entity))
                    {
                        var targetData = entityManager.GetComponentData<FlowFieldTarget>(entity);
                        if (targetData.isActive)
                            activeCount++;
                    }
                }
            }
            finally
            {
                entities.Dispose();
                activeQuery.Dispose();
            }
            
            return activeCount;
        }
        
        private int GetActiveTargetCountFromList()
        {
            int count = 0;
            foreach (var target in targetsToVisualize)
            {
                if (IsEntityValid(target) && 
                    entityManager.HasComponent<FlowFieldTarget>(target))
                {
                    var targetData = entityManager.GetComponentData<FlowFieldTarget>(target);
                    if (targetData.isActive)
                        count++;
                }
            }
            return count;
        }
        
        protected override void OnCleanup()
        {
            if (!targetQuery.IsEmpty)
            {
                targetQuery.Dispose();
            }
        }
        
        /// <summary>
        /// Add a target to the visualization list (when not using auto-find)
        /// </summary>
        public void AddTarget(Entity target)
        {
            if (!targetsToVisualize.Contains(target))
            {
                targetsToVisualize.Add(target);
            }
        }
        
        /// <summary>
        /// Remove a target from the visualization list
        /// </summary>
        public void RemoveTarget(Entity target)
        {
            targetsToVisualize.Remove(target);
        }
        
        /// <summary>
        /// Clear all targets from the visualization list
        /// </summary>
        public void ClearTargets()
        {
            targetsToVisualize.Clear();
        }
    }
}
