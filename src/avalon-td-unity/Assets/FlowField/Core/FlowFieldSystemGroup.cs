using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Integration;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using Unity.Entities;

namespace FlowField
{
    [WorldSystemFilter(WorldSystemFilterFlags.Default | WorldSystemFilterFlags.EntitySceneOptimizations)]
    public partial class FlowFieldSystemGroup : ComponentSystemGroup
    {
        protected override void OnCreate()
        {
            base.OnCreate();

            // Add systems in proper order
            var unitStatsSystem = World.GetOrCreateSystem<UnitStatsSystem>();
            var effectInitializationSystem = World.GetOrCreateSystem<EffectInitializationSystem>();
            var gameEffectSystem = World.GetOrCreateSystem<GameEffectSystem>();
            var combatInitializationSystem = World.GetOrCreateSystem<CombatInitializationSystem>();
            var combatIntegrationSystem = World.GetOrCreateSystem<CombatIntegrationSystem>();
            var combatGroup = World.GetOrCreateSystem<FlowFieldCombatSystemGroup>();
            var managerSystem = World.GetOrCreateSystem<FlowFieldManagerSystem>();
            var movementGroup = World.GetOrCreateSystem<MovementSystemGroup>();

            AddSystemToUpdateList(unitStatsSystem);
            AddSystemToUpdateList(effectInitializationSystem);
            AddSystemToUpdateList(gameEffectSystem);
            AddSystemToUpdateList(combatInitializationSystem);
            AddSystemToUpdateList(combatIntegrationSystem);
            AddSystemToUpdateList(combatGroup);
            AddSystemToUpdateList(managerSystem);
            AddSystemToUpdateList(movementGroup);
        }
    }
}