using Avalon.Simulation.Movement;
using Unity.Burst;
using Unity.Entities;

namespace FlowField
{
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateBefore(typeof(MovementSystemGroup))]
    public partial struct UnitStatsSystem : ISystem
    {
        private EntityQuery unitsWithAvoidanceQuery;
        private EntityQuery unitsWithoutAvoidanceQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EndSimulationEntityCommandBufferSystem.Singleton>();

            // Query for units with avoidance that need initialization
            unitsWithAvoidanceQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadWrite<FlowFieldFollower>(),
                ComponentType.ReadWrite<AvoidanceData>(),
                ComponentType.Exclude<UnitStatsInitialized>()
            );

            // Query for units without avoidance that need initialization
            unitsWithoutAvoidanceQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadWrite<FlowFieldFollower>(),
                ComponentType.Exclude<AvoidanceData>(),
                ComponentType.Exclude<UnitStatsInitialized>()
            );

            // Require that at least one of the queries has entities to process
            state.RequireAnyForUpdate(unitsWithAvoidanceQuery, unitsWithoutAvoidanceQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get entities to process before scheduling jobs to avoid dependency issues
            var entitiesWithAvoidance = unitsWithAvoidanceQuery.ToEntityArray(Unity.Collections.Allocator.TempJob);
            var entitiesWithoutAvoidance =
                unitsWithoutAvoidanceQuery.ToEntityArray(Unity.Collections.Allocator.TempJob);

            var jobHandle = state.Dependency;

            // Schedule jobs for units with avoidance
            if (entitiesWithAvoidance.Length > 0)
            {
                var initJobWithAvoidance = new InitializeUnitStatsWithAvoidanceJob();
                jobHandle = initJobWithAvoidance.ScheduleParallel(unitsWithAvoidanceQuery, jobHandle);
            }

            // Schedule jobs for units without avoidance
            if (entitiesWithoutAvoidance.Length > 0)
            {
                var initJobWithoutAvoidance = new InitializeUnitStatsWithoutAvoidanceJob();
                jobHandle = initJobWithoutAvoidance.ScheduleParallel(unitsWithoutAvoidanceQuery, jobHandle);
            }

            // Set dependency without completing jobs to avoid crashes
            state.Dependency = jobHandle;

            // Add tag to mark units as initialized using EntityCommandBuffer
            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);

            // Mark units with avoidance as initialized
            for (int i = 0; i < entitiesWithAvoidance.Length; i++)
            {
                ecb.AddComponent<UnitStatsInitialized>(entitiesWithAvoidance[i]);
            }

            // Mark units without avoidance as initialized
            for (int i = 0; i < entitiesWithoutAvoidance.Length; i++)
            {
                ecb.AddComponent<UnitStatsInitialized>(entitiesWithoutAvoidance[i]);
            }

            // Dispose the entity arrays
            entitiesWithAvoidance.Dispose();
            entitiesWithoutAvoidance.Dispose();
        }
    }
}