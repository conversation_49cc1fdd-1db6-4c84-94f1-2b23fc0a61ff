using Avalon.Simulation.Movement;
using Unity.Burst;
using Unity.Entities;

namespace FlowField
{
    [BurstCompile]
    public partial struct InitializeUnitStatsWithAvoidanceJob : IJobEntity
    {
        public void Execute(in UnitStats stats, ref FlowFieldFollower follower, ref AvoidanceData avoidance)
        {
            // Copy movement stats from UnitStats to FlowFieldFollower
            follower.maxSpeed = stats.maxSpeed;
            follower.acceleration = stats.acceleration;
            follower.deceleration = stats.deceleration;

            // Update avoidance data radius
            avoidance.radius = stats.radius;
        }
    }
}