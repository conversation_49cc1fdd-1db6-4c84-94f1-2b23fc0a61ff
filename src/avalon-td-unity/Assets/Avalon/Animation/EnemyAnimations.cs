using Animancer;
using Sirenix.OdinInspector;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Animation
{
    public class EnemyAnimations : MonoBehaviour
    {
        [SerializeField] private AnimancerComponent animancer;
        [Serialize<PERSON>ield, AssetSelector] private AnimationClip idle;
        [Ser<PERSON>izeField, AssetSelector] private AnimationClip move;
        [SerializeField, AssetSelector] private AnimationClip run;
        
        public dfloat RunSpeed { get; set; } = new dfloat(5);
        
        protected virtual void Update()
        {
            
        }

        public void ApplyAnimationState(EnemyAnimationState animationState)
        {
            var animationToPlay = idle;
            if (animationState.Speed > RunSpeed)
            {
                animationToPlay = run;
            } 
            else if (animationState.Speed > (dfloat)0.01d)
            {
                animationToPlay = move;
            }
            
            animancer.Play(animationToPlay);
        }
    }
}