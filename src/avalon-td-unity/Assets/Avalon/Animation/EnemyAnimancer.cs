using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using Avalon.Visualization;
using FlowField.Core;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Animation
{
    public struct EnemyAnimationState : IComponentData
    {
        public dfloat Speed;
        public bool IsStunned;
        public bool IsSlowed;
    }
    
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct EnemyAnimationSystem : ISystem
    {
        public void OnUpdate(ref SystemState state)
        {
            foreach (var (gameObjectRef, enemyAnimationState, movement, entity) in SystemAPI
                         .Query<RefRO<GameObjectRef>, RefRW<EnemyAnimationState>, RefRO<Movement>>()
                         .WithEntityAccess())
            {
                var gameObject = GameObjectPool.Instance.GetById(gameObjectRef.ValueRO.gameObjectId);
                if (gameObject == null)
                {
                    continue;
                }
                
                var animations = gameObject.GetComponent<EnemyAnimations>();
                if (animations == null)
                {
                    continue;
                }

                enemyAnimationState.ValueRW.Speed = dmath.length(movement.ValueRO.Vector);
                
                animations.ApplyAnimationState(enemyAnimationState.ValueRO);
            }
        }
    }
}