using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;

namespace Avalon.Visualization.Movement
{
    [BurstCompile]
    public partial struct InterpolationJob : IJobEntity
    {
        public float alpha;

        public void Execute(
            in SimulationTransform simulationTransform,
            in PreviousSimulationTransform previousSimulationTransform,
            ref LocalTransform localTransform
        )
        {
            // Clamp alpha to valid range for safety
            var clampedAlpha = math.clamp(alpha, 0.0f, 1.0f);

            // Convert deterministic simulation state to non-deterministic rendering state for interpolation
            var currentPos = new float3(
                (float)simulationTransform.position.x,
                (float)simulationTransform.position.y,
                (float)simulationTransform.position.z
            );
            var previousPos = new float3(
                (float)previousSimulationTransform.Position.x,
                (float)previousSimulationTransform.Position.y,
                (float)previousSimulationTransform.Position.z
            );
            var currentRot = new quaternion(
                (float)simulationTransform.rotation.value.x,
                (float)simulationTransform.rotation.value.y,
                (float)simulationTransform.rotation.value.z,
                (float)simulationTransform.rotation.value.w
            );
            var previousRot = new quaternion(
                (float)previousSimulationTransform.Rotation.value.x,
                (float)previousSimulationTransform.Rotation.value.y,
                (float)previousSimulationTransform.Rotation.value.z,
                (float)previousSimulationTransform.Rotation.value.w
            );
            
            var currentScale = (float)simulationTransform.scale;
            var previousScale = (float)previousSimulationTransform.Scale;
            
            // Check for valid positions (prevent NaN/Infinity interpolation)
            if (math.any(math.isnan(currentPos)) || math.any(math.isnan(previousPos)))
            {
                // Fallback to current position if previous is invalid
                localTransform.Position = currentPos;
            }
            else
            {
                // Linear interpolation for position
                localTransform.Position =  math.lerp(previousPos, currentPos, clampedAlpha);
            }

            // Safe scale interpolation
            if (math.isnan(currentScale) || math.isnan(previousScale))
            {
                localTransform.Scale = currentScale;
            }
            else
            {
                localTransform.Scale = math.lerp(previousScale, currentScale, clampedAlpha);
            }

            // Safe spherical interpolation for rotation (smoother for rotations)
            if (math.any(math.isnan(currentRot.value)) || math.any(math.isnan(previousRot.value)))
            {
                localTransform.Rotation = currentRot;
            }
            else
            {
                localTransform.Rotation = math.slerp(previousRot, currentRot, clampedAlpha);
            }
        }
    }
}