using Avalon.Simulation;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Entities;
using Unity.Transforms;

namespace Avalon.Visualization.Movement
{
    [BurstCompile]
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct InterpolationSystem : ISystem
    {
        private EntityQuery fixedTimestepQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<SimulationTransform>();
            state.RequireForUpdate<PreviousSimulationTransform>();
            state.RequireForUpdate<LocalTransform>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Interpolate positions for smooth rendering
            var interpolationJob = new InterpolationJob
            {
                alpha = fixedTimestep.interpolationAlpha
            };

            state.Dependency = interpolationJob.ScheduleParallel(state.Dependency);
        }
    }
}