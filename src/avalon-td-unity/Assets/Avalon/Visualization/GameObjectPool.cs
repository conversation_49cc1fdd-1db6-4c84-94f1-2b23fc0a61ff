﻿using System.Collections.Generic;
using UnityEngine;

namespace Avalon.Visualization
{
    public class GameObjectPool : MonoBehaviour
    {
        public static GameObjectPool Instance { get; private set; }
        
        public Dictionary<int, GameObject> pool = new Dictionary<int, GameObject>();
        
        private void Awake()
        {
            if (Instance == null)
                Instance = this;
            else
                Destroy(this);
        }

        public GameObject GetById(int id)
        {
            return pool.GetValueOrDefault(id);
        }
        
        public int Add(int id, GameObject gameObject)
        {
            pool.Add(id, gameObject);
            return id;
        }
    }
}