﻿using Avalon.Core;
using UnityEditor;
using UnityEngine;

namespace Avalon.Startup
{
    public class ScriptableObjectLoader<T> where T : IdentifiableScriptableObject<T>
    {
        private static ScriptableObjectDatabase<T> _database;
        private static bool _isLoaded = false;
        
        public static ScriptableObjectDatabase<T> Database
        {
            get
            {
                EnsureLoaded();
                return _database;
            }
        }

        private static void EnsureLoaded()
        {
            if (!_isLoaded)
            {
                LoadAllAssets();
            }
        }

        public static void LoadAllAssets()
        {
            _database = new ScriptableObjectDatabase<T>();

#if UNITY_EDITOR
            // In editor, search the entire project
            string[] guids = AssetDatabase.FindAssets($"t:{typeof(T).Name}");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                T asset = AssetDatabase.LoadAssetAtPath<T>(path);
                if (asset != null)
                {
                    _database.Add(asset);
                }
            }
#else
            // In build, load from Resources
            T[] assets = Resources.LoadAll<T>("");
            foreach (T asset in assets)
            {
                _database.Add(asset);
            }
#endif

            _isLoaded = true;
            Debug.Log($"Loaded {_database.Count} assets of type {typeof(T).Name}");
        }

        public static void RefreshAssets()
        {
            _isLoaded = false;
            LoadAllAssets();
        }
    }
}