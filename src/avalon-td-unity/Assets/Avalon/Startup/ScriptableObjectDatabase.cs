﻿using System.Collections.Generic;
using System.Linq;
using Avalon.Core;

namespace Avalon.Startup
{
    public class ScriptableObjectDatabase<T> where T : IdentifiableScriptableObject<T>
    {
        private Dictionary<int, T> Lookup { get; set; } = new Dictionary<int, T>();
        
        public T GetById(int id)
        {
            return Lookup.GetValueOrDefault(id);
        }
        
        public void Add(T asset)
        {
            Lookup.TryAdd(asset.Id, asset);
        }
        
        public List<T> GetAll()
        {
            return Lookup.Values.ToList();
        }
        
        public int Count => Lookup.Count;
    }
}