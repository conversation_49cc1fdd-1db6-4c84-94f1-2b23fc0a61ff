﻿using System;
using System.Collections.Generic;
using System.Linq;
using Avalon.Core;
using UnityEngine;

namespace Avalon.Startup
{
    /// <summary>
    /// Registry that automatically loads all marked ScriptableObjects
    /// </summary>
    public static class AutoScriptableObjectRegistry
    {
        private static Dictionary<Type, object> _loaders = new Dictionary<Type, object>();
        private static bool _initialized = false;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void Initialize()
        {
            if (_initialized) return;

            // Find all types with AutoLoad attribute
            var autoLoadTypes = AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => type.IsSubclassOf(typeof(ScriptableObject)) && 
                               type.GetCustomAttributes(typeof(AutoLoadAttribute), false).Length > 0);

            foreach (Type type in autoLoadTypes)
            {
                CreateLoader(type);
            }

            _initialized = true;
        }

        private static void CreateLoader(Type scriptableObjectType)
        {
            Type loaderType = typeof(ScriptableObjectLoader<>).MakeGenericType(scriptableObjectType);
            object loader = Activator.CreateInstance(loaderType);
            _loaders[scriptableObjectType] = loader;

            // Trigger loading
            var loadMethod = loaderType.GetMethod("LoadAllAssets");
            loadMethod?.Invoke(null, null);
        }

        public static ScriptableObjectLoader<T> GetLoader<T>() where T : IdentifiableScriptableObject<T>
        {
            return _loaders.TryGetValue(typeof(T), out var loader) ? 
                loader as ScriptableObjectLoader<T> : null;
        }
    }
}