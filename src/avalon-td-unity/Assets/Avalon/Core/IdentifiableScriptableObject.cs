﻿using Sirenix.OdinInspector;
using Sirenix.Serialization;
using UnityEngine;

namespace Avalon.Core
{
    /// <summary>
    /// Example base class for your ScriptableObjects with built-in ID system
    /// </summary>
    public abstract class IdentifiableScriptableObject<T> : SerializedScriptableObject
    {
        [DelayedProperty, OdinSerialize]
        [TitleGroup("Identifier", Order = 1)]
        public int Id { get; set; }

        [DelayedProperty, OdinSerialize]
        [TitleGroup("Identifier", Order = 1)]
        public string Name { get; set; }
        
        [ReadOnly, OdinSerialize]
        [TitleGroup("Identifier", Order = 1)]
        public string SafeName { get; set; }
        
        [OdinSerialize]
        [AssetSelector(FlattenTreeView = true)]
        [TitleGroup("Identifier", Order = 1)]
        public GameObject Prefab { get; set; }

        protected virtual void OnValidate()
        {
            #if UNITY_EDITOR
            if (string.IsNullOrEmpty(Name))
            {
                Name = name;
            }
            
            var sanitizedName = Name.Replace(" ", "_").Replace("(", "").Replace(")", "");
            var safeName = $"{Id:d4}_{sanitizedName}";
            if (safeName != SafeName)
            {
                SafeName = safeName;
                name = SafeName;
                
                // Get the asset path and rename the file
                var assetPath = UnityEditor.AssetDatabase.GetAssetPath(GetInstanceID());
                if (!string.IsNullOrEmpty(assetPath))
                {
                    var extension = System.IO.Path.GetExtension(assetPath);
                    UnityEditor.AssetDatabase.RenameAsset(assetPath, name + extension);
                    UnityEditor.AssetDatabase.SaveAssets();
                    UnityEditor.AssetDatabase.Refresh();
                }
                
                UnityEditor.EditorUtility.SetDirty(this);
            }
            #endif
        }
    }
}