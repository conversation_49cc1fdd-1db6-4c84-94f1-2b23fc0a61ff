﻿using UnityEditor;
using UnityEngine;

namespace Avalon.Core
{
    /// <summary>
    /// Generic singleton that loads all ScriptableObjects of type T and provides lookup functionality
    /// </summary>
    public abstract class ScriptableObjectSingleton<T> : ScriptableObject where T : ScriptableObject
    {
        private static T _instance;
        public static T Instance
        {
            get
            {
                if (_instance == null)
                {
                    LoadInstance();
                }
                return _instance;
            }
        }

        private static void LoadInstance()
        {
            // Try to find existing instance in Resources
            T[] instances = Resources.LoadAll<T>("");
            if (instances.Length > 0)
            {
                _instance = instances[0];
                if (instances.Length > 1)
                {
                    Debug.LogWarning($"Multiple instances of {typeof(T).Name} found in Resources. Using the first one.");
                }
            }
            else
            {
#if UNITY_EDITOR
                // In editor, try to find any instance in the project
                string[] guids = AssetDatabase.FindAssets($"t:{typeof(T).Name}");
                if (guids.Length > 0)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                    _instance = AssetDatabase.LoadAssetAtPath<T>(path);
                }
                else
                {
                    // Create a new instance if none exists
                    _instance = CreateInstance<T>();
                    AssetDatabase.CreateAsset(_instance, $"Assets/Resources/{typeof(T).Name}.asset");
                    AssetDatabase.SaveAssets();
                }
#else
                Debug.LogError($"No instance of {typeof(T).Name} found in Resources folder!");
#endif
            }
        }
    }
}