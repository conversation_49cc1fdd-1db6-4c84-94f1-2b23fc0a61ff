using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation
{
    public struct FixedTimestep : IComponentData
    {
        public dfloat tickRate; // Ticks per second (10 Hz = 10 ticks per second)
        public dfloat tickDuration; // Duration of each tick (0.1 seconds)
        public float accumulator; // Accumulated time since last tick
        public dfloat currentTime; // Current simulation time
        public dfloat previousTime; // Previous simulation time (for interpolation)
        public int tickCount; // Total number of ticks processed
        public bool shouldProcessTick; // Whether a tick should be processed this frame
        public float interpolationAlpha; // Alpha value for interpolation (0-1)
    }
}