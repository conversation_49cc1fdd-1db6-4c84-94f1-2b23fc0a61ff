﻿using Avalon.Visualization;
using FlowField.Core;
using Unity.Collections;
using Unity.Entities;
using UnityEngine;

namespace Avalon.Simulation
{
    public struct Destroyed : IComponentData { }
    
    public struct DelayedDestruction : IComponentData
    {
        public int EndTick;
    }
    
    [UpdateInGroup(typeof(SimulationSystemGroup), OrderLast = true)]
    public partial struct EntityDestructionSystem : ISystem
    {
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<FixedTimestep>();
        }
        
        public void OnUpdate(ref SystemState state)
        {
            var fixedTimestep = SystemAPI.GetSingleton<FixedTimestep>();
            var ecb = new EntityCommandBuffer(Allocator.TempJob);
            
            foreach (var (_, entity) in SystemAPI.Query<RefRO<Destroyed>>().WithNone<GameObjectRef>().WithEntityAccess())
            {
                ecb.DestroyEntity(entity);
            }

            foreach (var (_, gameObjectRef, entity) in SystemAPI.Query<RefRO<Destroyed>, RefRO<GameObjectRef>>().WithEntityAccess())
            {
                var gameObject = GameObjectPool.Instance.GetById(gameObjectRef.ValueRO.gameObjectId);
                if (gameObject != null)
                {
                    Object.Destroy(gameObject);
                }

                ecb.DestroyEntity(entity);
            }
            
            foreach (var (delay, entity) in SystemAPI.Query<RefRO<DelayedDestruction>>().WithNone<GameObjectRef>().WithEntityAccess())
            {
                if (delay.ValueRO.EndTick > fixedTimestep.tickCount) 
                    continue;
                
                ecb.DestroyEntity(entity);
            }

            foreach (var (delay, gameObjectRef, entity) in SystemAPI.Query<RefRO<DelayedDestruction>, RefRO<GameObjectRef>>().WithEntityAccess())
            {
                if (delay.ValueRO.EndTick > fixedTimestep.tickCount) 
                    continue;
                
                var gameObject = GameObjectPool.Instance.GetById(gameObjectRef.ValueRO.gameObjectId);
                if (gameObject != null)
                {
                    Object.Destroy(gameObject);
                }

                ecb.DestroyEntity(entity);
            }
            
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }
    }
}