﻿namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// Different types of damage
    /// </summary>
    public enum DamageType : byte
    {
        /// <summary>Physical damage affected by armor</summary>
        Physical = 0,
        
        /// <summary>Magical damage affected by magic resistance</summary>
        Magical = 1,
        
        /// <summary>True damage that ignores all resistances</summary>
        True = 2,
        
        /// <summary>Elemental damage with special effects</summary>
        Elemental = 3,
        
        /// <summary>Poison damage over time</summary>
        Poison = 4,
        
        /// <summary>Fire damage with burning effects</summary>
        Fire = 5,
        
        /// <summary>Ice damage with slowing effects</summary>
        Ice = 6,
        
        /// <summary>Lightning damage with chain effects</summary>
        Lightning = 7
    }
}