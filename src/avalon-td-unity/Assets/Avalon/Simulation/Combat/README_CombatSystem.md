# FlowField Combat System

## Overview

The FlowField Combat System provides a comprehensive, deterministic combat framework for tower defense gameplay. It supports various attack patterns, targeting strategies, projectile types, and special effects while maintaining ECS optimization and deterministic behavior.

## Architecture

### System Group Structure

The combat system is organized under `FlowFieldCombatSystemGroup` which executes after the Effects system and before Movement:

```
FlowFieldSystemGroup
├── UnitStatsSystem
├── EffectInitializationSystem  
├── GameEffectSystem
├── FlowFieldCombatSystemGroup
│   ├── TargetSelectionSystem
│   ├── AttackProcessingSystem
│   ├── ProjectileSystem
│   ├── DamageApplicationSystem
│   └── CombatEffectsProcessingSystem
├── FlowFieldManagerSystem
└── FlowFieldMovementGroup
```

### Directory Structure

```
FlowField/Combat/
├── Core/                    # Core components and data structures
├── Targeting/              # Target selection and acquisition
├── Attacks/                # Attack patterns and processing
├── Projectiles/            # Projectile systems and movement
├── Damage/                 # Damage calculation and application
├── Effects/                # Combat-specific effects
├── Configuration/          # ScriptableObject configurations
├── Debugging/              # Debug tools and visualization
└── Tests/                  # Unit tests
```

## Key Features

### Attack Patterns
- **Single Target**: Direct attacks on individual enemies
- **Linear**: Straight-line projectiles with optional piercing
- **Multi-Linear Wave**: Multiple projectiles in spread patterns
- **Homing**: Projectiles that track targets
- **AOE Position**: Area attacks at specific locations
- **AOE Self**: Area attacks centered on attacker
- **Cone**: Directional cone attacks
- **Circle**: Circular attacks around attacker
- **Full Map**: Global attacks hitting all enemies
- **Chain**: Attacks that jump between targets
- **Beam**: Continuous damage along a line

### Targeting Strategies
- Nearest/Farthest enemy
- Highest/Lowest health
- First/Last in path
- Strongest/Weakest overall
- Random selection

### Projectile Types
- Instant hit (no travel time)
- Linear movement
- Homing with tracking
- Arcing with gravity
- Bouncing projectiles
- Seeking with turn limits

### Damage Types
- Physical (affected by armor)
- Magical (affected by magic resistance)
- True (ignores resistances)
- Elemental (fire, ice, lightning, etc.)

## Integration with Existing Systems

### Effects System Integration
The combat system integrates seamlessly with the existing `GameEffect` system:
- Combat effects use the same duration-based system
- Status effects (stun, slow, DOT) are applied through `GameEffectUtils`
- Combat modifiers work with existing `CombatEffects` component

### UnitStats Integration
Combat parameters are defined in `UnitStats`:
- Attack damage, range, cooldown
- Health and armor values
- Special ability flags

### Deterministic Behavior
All combat calculations use deterministic types:
- `dfloat` for damage, range, timing
- `int` for counts and IDs
- `dmath` functions for calculations

## Usage Examples

### Basic Attack Setup
```csharp
// Add combat components to a unit
CombatUtils.AddCombatComponents(entityManager, unit, attackPattern, targetingStrategy);

// Configure attack parameters
var attackData = new AttackData
{
    damage = (dfloat)100,
    range = (dfloat)5,
    cooldown = (dfloat)1.5f,
    pattern = AttackPattern.Linear
};
```

### Applying Combat Effects
```csharp
// Apply damage over time
CombatEffectUtils.ApplyDamageOverTime(entity, damage, duration, tickRate);

// Apply stun effect
CombatEffectUtils.ApplyStun(entity, stunDuration);
```

## Configuration

Combat behaviors are configured through ScriptableObjects:
- `AttackPatternDefinition`: Defines attack patterns and parameters
- `ProjectileDefinition`: Configures projectile behavior
- `DamageTypeDefinition`: Sets up damage types and resistances

## Performance Considerations

- Burst-compiled systems for optimal performance
- Efficient spatial queries for target selection
- Pooled projectile entities to reduce allocations
- Batched damage application to minimize entity operations

## Debugging Tools

- Visual range indicators
- Projectile path visualization
- Damage number display
- Combat state inspection
- Performance profiling tools

## Next Steps

This architecture provides the foundation for implementing all combat system components. Each subsystem will be developed following this structure while maintaining integration with existing FlowField systems.
