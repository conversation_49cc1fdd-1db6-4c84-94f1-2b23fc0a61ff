﻿namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// Different types of attack patterns available in the combat system
    /// </summary>
    public enum AttackPattern : byte
    {
        /// <summary>Single target attack</summary>
        SingleTarget = 0,
        InstantSingleTarget = 0,
        
        /// <summary>Linear projectile that hits first target in line</summary>
        Linear = 1,
        
        /// <summary>Linear projectile that pierces through multiple targets</summary>
        LinearPiercing = 2,
        
        /// <summary>Multiple linear projectiles in a spread pattern</summary>
        MultiLinearWave = 3,
        
        /// <summary>Projectile that homes in on target</summary>
        Homing = 4,
        
        /// <summary>Area of effect attack at target position</summary>
        AOEPosition = 5,
        
        /// <summary>Area of effect attack around caster</summary>
        AOESelf = 6,
        
        /// <summary>Cone-shaped attack in front of caster</summary>
        Cone = 7,
        
        /// <summary>Circular attack around caster</summary>
        Circle = 8,
        
        /// <summary>Attack that hits all enemies on the map</summary>
        FullMap = 9,
        
        /// <summary>Chain attack that jumps between targets</summary>
        Chain = 10,
        
        /// <summary>Beam attack that continuously damages targets in line</summary>
        Beam = 11
    }
    
    public static class AttackPatternExtensions
    {
        public static bool IsProjectile(this AttackPattern attackPattern) => attackPattern switch
        {
            AttackPattern.Linear => true,
            AttackPattern.LinearPiercing => true,
            AttackPattern.MultiLinearWave => true,
            AttackPattern.Homing => true,
            AttackPattern.Beam => true,
            _ => false
        };
    }
}