namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// Combat state flags for units
    /// </summary>
    [System.Flags]
    public enum CombatState : byte
    {
        None = 0,
        
        /// <summary>Unit is currently attacking</summary>
        Attacking = 1 << 0,
        
        /// <summary>Unit is on attack cooldown</summary>
        OnCooldown = 1 << 1,
        
        /// <summary>Unit has a valid target</summary>
        HasTarget = 1 << 2,
        
        /// <summary>Unit is stunned and cannot attack</summary>
        Stunned = 1 << 3,
        
        /// <summary>Unit is silenced and cannot use abilities</summary>
        Silenced = 1 << 4,
        
        /// <summary>Unit is disarmed and cannot attack</summary>
        Disarmed = 1 << 5,
        
        /// <summary>Unit is channeling an ability</summary>
        Channeling = 1 << 6
    }
}
