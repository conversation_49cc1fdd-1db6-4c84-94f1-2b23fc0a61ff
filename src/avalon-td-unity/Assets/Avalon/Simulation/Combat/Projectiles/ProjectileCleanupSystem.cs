using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Projectiles
{
    /// <summary>
    /// System that handles cleanup of expired or destroyed projectiles
    /// Manages projectile lifetime and removes projectiles that have hit targets or expired
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    [UpdateAfter(typeof(ProjectileSystem))]
    public partial class ProjectileCleanupSystem : SystemBase
    {
        private EntityQuery expiredProjectileQuery;
        private EntityQuery fixedTimestepQuery;
        private EndSimulationEntityCommandBufferSystem ecbSystem;
        
        protected override void OnCreate()
        {
            // Query for projectiles that need cleanup
            expiredProjectileQuery = GetEntityQuery(
                ComponentType.ReadOnly<ProjectileData>(),
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.Exclude<Disabled>()
            );
            
            // Query for fixed timestep
            fixedTimestepQuery = GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());
            
            // Get ECB system for deferred entity operations
            ecbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
            
            RequireForUpdate(fixedTimestepQuery);
        }
        
        protected override void OnUpdate()
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            dfloat currentTime = fixedTimestep.currentTime;
            var ecb = ecbSystem.CreateCommandBuffer();
            
            // Clean up expired projectiles
            Entities
                .WithAll<ProjectileData>()
                .ForEach((Entity entity, in ProjectileData projectileData) =>
                {
                    // Check if projectile has expired
                    if (projectileData.HasExpired(currentTime))
                    {
                        // Spawn explosion effect if configured
                        if (projectileData.hitEffectPrefab != Entity.Null)
                        {
                            SpawnHitEffect(ecb, projectileData.hitEffectPrefab, 
                                GetComponent<SimulationTransform>(entity).position);
                        }
                        
                        // Destroy the projectile
                        ecb.DestroyEntity(entity);
                    }
                }).WithoutBurst().Run();
            
            // Clean up projectiles that are out of bounds
            CleanupOutOfBoundsProjectiles(ecb);
            
            // Add command buffer to system
            ecbSystem.AddJobHandleForProducer(Dependency);
        }
        
        private void CleanupOutOfBoundsProjectiles(EntityCommandBuffer ecb)
        {
            // Define world bounds (would typically come from a configuration)
            dfloat3 worldMin = new dfloat3((dfloat)(-1000), (dfloat)(-100), (dfloat)(-1000));
            dfloat3 worldMax = new dfloat3((dfloat)1000, (dfloat)100, (dfloat)1000);
            
            Entities
                .WithAll<ProjectileData>()
                .ForEach((Entity entity, in SimulationTransform transform) =>
                {
                    dfloat3 pos = transform.position;
                    
                    // Check if projectile is outside world bounds
                    if (pos.x < worldMin.x || pos.x > worldMax.x ||
                        pos.y < worldMin.y || pos.y > worldMax.y ||
                        pos.z < worldMin.z || pos.z > worldMax.z)
                    {
                        ecb.DestroyEntity(entity);
                    }
                }).WithoutBurst().Run();
        }
        
        private void SpawnHitEffect(EntityCommandBuffer ecb, Entity effectPrefab, dfloat3 position)
        {
            // Instantiate hit effect at the specified position
            var effectEntity = ecb.Instantiate(effectPrefab);
            ecb.SetComponent(effectEntity, new SimulationTransform
            {
                position = position,
                rotation = dquaternion.identity,
                scale = dfloat.One
            });
        }
    }
    
    /// <summary>
    /// Component to mark projectiles for destruction
    /// Used when projectiles hit targets or need to be cleaned up
    /// </summary>
    public struct ProjectileDestroyed : IComponentData
    {
        public dfloat destroyTime;
        public bool spawnEffect;
        public Entity effectPrefab;
        public dfloat3 effectPosition;
        public dfloat3 effectScale;
    }
    
    /// <summary>
    /// Utility functions for projectile management
    /// </summary>
    public static class ProjectileUtils
    {
        /// <summary>
        /// Mark a projectile for destruction
        /// </summary>
        public static void DestroyProjectile(EntityCommandBuffer ecb, Entity projectile, dfloat currentTime,
            bool spawnEffect = false, Entity effectPrefab = default, dfloat3 effectPosition = default)
        {
            ecb.AddComponent(projectile, new ProjectileDestroyed
            {
                destroyTime = currentTime,
                spawnEffect = spawnEffect,
                effectPrefab = effectPrefab,
                effectPosition = effectPosition,
                effectScale = new dfloat3(dfloat.One)
            });
        }
        
        /// <summary>
        /// Create a projectile pool for efficient projectile management
        /// </summary>
        public static void CreateProjectilePool(EntityManager entityManager, Entity projectilePrefab, int poolSize)
        {
            // Create a pool of inactive projectiles for reuse
            for (int i = 0; i < poolSize; i++)
            {
                var projectile = entityManager.Instantiate(projectilePrefab);
                entityManager.AddComponent<Disabled>(projectile);
            }
        }
        
        /// <summary>
        /// Get a projectile from the pool or create a new one
        /// </summary>
        public static Entity GetPooledProjectile(EntityManager entityManager, Entity projectilePrefab)
        {
            // Try to find a disabled projectile to reuse
            var poolQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<ProjectileData>(),
                ComponentType.ReadOnly<Disabled>()
            );
            
            if (!poolQuery.IsEmpty)
            {
                var pooledProjectiles = poolQuery.ToEntityArray(Allocator.Temp);
                if (pooledProjectiles.Length > 0)
                {
                    var projectile = pooledProjectiles[0];
                    entityManager.RemoveComponent<Disabled>(projectile);
                    pooledProjectiles.Dispose();
                    return projectile;
                }
                pooledProjectiles.Dispose();
            }
            
            // No pooled projectile available, create a new one
            return entityManager.Instantiate(projectilePrefab);
        }
        
        /// <summary>
        /// Return a projectile to the pool instead of destroying it
        /// </summary>
        public static void ReturnToPool(EntityCommandBuffer ecb, Entity projectile)
        {
            // Reset projectile data and disable it
            ecb.AddComponent<Disabled>(projectile);
            
            // Reset transform to origin
            ecb.SetComponent(projectile, new SimulationTransform
            {
                position = dfloat3.zero,
                rotation = dquaternion.identity,
                scale = dfloat.One
            });
        }
        
        /// <summary>
        /// Calculate projectile trajectory for arc projectiles
        /// </summary>
        public static dfloat3 CalculateArcTrajectory(dfloat3 start, dfloat3 target, dfloat arcHeight, dfloat gravity, out dfloat launchAngle)
        {
            dfloat3 displacement = target - start;
            dfloat horizontalDistance = dmath.length(new dfloat2(displacement.x, displacement.z));
            dfloat verticalDistance = displacement.y;
            
            // Calculate launch angle for desired arc height
            dfloat g = dmath.abs(gravity);
            dfloat h = arcHeight;
            
            // Simplified trajectory calculation
            launchAngle = dmath.atan2(verticalDistance + h, horizontalDistance);
            
            dfloat speed = dmath.sqrt(g * horizontalDistance * horizontalDistance / 
                (horizontalDistance * dmath.sin((dfloat)2 * launchAngle) - (dfloat)2 * verticalDistance * dmath.cos(launchAngle) * dmath.cos(launchAngle)));
            
            dfloat3 direction = dmath.normalize(new dfloat3(displacement.x, dfloat.Zero, displacement.z));
            dfloat3 velocity = direction * speed * dmath.cos(launchAngle);
            velocity.y = speed * dmath.sin(launchAngle);
            
            return velocity;
        }
        
        /// <summary>
        /// Check if a projectile should bounce off a surface
        /// </summary>
        public static bool ShouldBounce(ProjectileData projectileData, dfloat3 hitNormal)
        {
            return projectileData.CanBounce() && dmath.dot(projectileData.velocity, hitNormal) < dfloat.Zero;
        }
        
        /// <summary>
        /// Calculate bounce velocity
        /// </summary>
        public static dfloat3 CalculateBounceVelocity(dfloat3 incomingVelocity, dfloat3 surfaceNormal, dfloat bounciness)
        {
            // Reflect velocity off the surface
            dfloat3 reflectedVelocity = incomingVelocity - (dfloat)2 * dmath.dot(incomingVelocity, surfaceNormal) * surfaceNormal;

            // Apply bounciness factor
            return reflectedVelocity * bounciness;
        }

        /// <summary>
        /// Calculate bounce velocity with default bounciness
        /// </summary>
        public static dfloat3 CalculateBounceVelocity(dfloat3 incomingVelocity, dfloat3 surfaceNormal)
        {
            return CalculateBounceVelocity(incomingVelocity, surfaceNormal, new dfloat(0.8d));
        }
    }
}
