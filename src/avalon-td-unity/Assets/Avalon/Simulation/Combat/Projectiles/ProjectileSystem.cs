using Avalon.Simulation;
using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Attacks;
using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Projectiles
{
    /// <summary>
    /// System that handles projectile movement, collision detection, and lifetime management
    /// Processes all active projectiles and applies their effects when they hit targets
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    [UpdateAfter(typeof(AttackExecutionSystem))]
    public partial struct ProjectileSystem : ISystem
    {
        private EntityQuery projectileQuery;
        private EntityQuery targetQuery;
        private EntityQuery fixedTimestepQuery;
        
        // Component lookups for efficient access
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<UnitStats> unitStatsLookup;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for active projectiles
            projectileQuery = SystemAPI.QueryBuilder()
                .WithAll<ProjectileData, SimulationTransform>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for potential collision targets
            targetQuery = SystemAPI.QueryBuilder()
                .WithAll<SimulationTransform, UnitStats>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for fixed timestep
            fixedTimestepQuery = SystemAPI.QueryBuilder()
                .WithAll<FixedTimestep>()
                .Build();
                
            // Initialize component lookups
            transformLookup = SystemAPI.GetComponentLookup<SimulationTransform>(true);
            unitStatsLookup = SystemAPI.GetComponentLookup<UnitStats>(true);

            // Only run when there are projectiles to process and fixed timestep is available
            state.RequireForUpdate(projectileQuery);
            state.RequireForUpdate(fixedTimestepQuery);
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Update component lookups
            transformLookup.Update(ref state);
            unitStatsLookup.Update(ref state);
            
            // Get target data for collision detection
            var targetEntities = targetQuery.ToEntityArray(Allocator.TempJob);
            var targetTransforms = targetQuery.ToComponentDataArray<SimulationTransform>(Allocator.TempJob);
            var targetStats = targetQuery.ToComponentDataArray<UnitStats>(Allocator.TempJob);
            
            // Schedule projectile movement job
            var movementJob = new ProjectileMovementJob
            {
                currentTime = fixedTimestep.currentTime,
                deltaTime = fixedTimestep.tickDuration,
                transformLookup = transformLookup,
                unitStatsLookup = unitStatsLookup,
                targetEntities = targetEntities,
                targetTransforms = targetTransforms,
                targetStats = targetStats
            };
            
            state.Dependency = movementJob.ScheduleParallel(projectileQuery, state.Dependency);
            
            // Dispose arrays after job completion
            state.Dependency = targetEntities.Dispose(state.Dependency);
            state.Dependency = targetTransforms.Dispose(state.Dependency);
            state.Dependency = targetStats.Dispose(state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that handles projectile movement and collision detection
    /// </summary>
    [BurstCompile]
    public partial struct ProjectileMovementJob : IJobEntity
    {
        [ReadOnly] public dfloat currentTime;
        [ReadOnly] public dfloat deltaTime;
        [ReadOnly] public ComponentLookup<SimulationTransform> transformLookup;
        [ReadOnly] public ComponentLookup<UnitStats> unitStatsLookup;
        
        [ReadOnly] public NativeArray<Entity> targetEntities;
        [ReadOnly] public NativeArray<SimulationTransform> targetTransforms;
        [ReadOnly] public NativeArray<UnitStats> targetStats;
        
        public void Execute(Entity projectileEntity, ref ProjectileData projectileData, ref SimulationTransform transform)
        {
            // Check if projectile has expired
            if (projectileData.HasExpired(currentTime))
            {
                // Mark for destruction (would use EntityCommandBuffer in real implementation)
                return;
            }
            
            dfloat3 oldPosition = transform.position;
            dfloat3 newPosition = oldPosition;
            
            // Update projectile based on type
            switch (projectileData.projectileType)
            {
                case ProjectileType.Linear:
                    newPosition = UpdateLinearProjectile(ref projectileData, transform, deltaTime);
                    break;
                    
                case ProjectileType.Homing:
                    newPosition = UpdateHomingProjectile(ref projectileData, transform, deltaTime);
                    break;
                    
                case ProjectileType.Arc:
                    newPosition = UpdateArcProjectile(ref projectileData, transform, deltaTime);
                    break;
                    
                case ProjectileType.Bouncing:
                    newPosition = UpdateBouncingProjectile(ref projectileData, transform, deltaTime);
                    break;
                    
                case ProjectileType.Seeking:
                    newPosition = UpdateSeekingProjectile(ref projectileData, transform, deltaTime);
                    break;
                    
                default:
                    newPosition = oldPosition + projectileData.velocity * deltaTime;
                    break;
            }
            
            // Check for collisions along the movement path
            if (CheckCollisions(projectileEntity, ref projectileData, oldPosition, newPosition))
            {
                // Projectile hit something, mark for destruction
                return;
            }
            
            // Update transform position
            transform.position = newPosition;
            
            // Update rotation to face movement direction if moving
            if (dmath.lengthsq(projectileData.velocity) > dfloat.Zero)
            {
                dfloat3 forward = dmath.normalize(projectileData.velocity);
                transform.rotation = dquaternion.LookRotationSafe(forward, dmath.up());
            }
        }
        
        private dfloat3 UpdateLinearProjectile(ref ProjectileData projectileData, SimulationTransform transform, dfloat deltaTime)
        {
            // Simple linear movement
            return transform.position + projectileData.velocity * deltaTime;
        }
        
        private dfloat3 UpdateHomingProjectile(ref ProjectileData projectileData, SimulationTransform transform, dfloat deltaTime)
        {
            dfloat3 currentPos = transform.position;
            
            // Check if we have a valid target
            if (projectileData.targetEntity != Entity.Null && transformLookup.HasComponent(projectileData.targetEntity))
            {
                var targetTransform = transformLookup[projectileData.targetEntity];
                dfloat3 targetPos = targetTransform.position;
                
                // Calculate homing direction
                dfloat3 homingDirection = projectileData.CalculateHomingDirection(currentPos, targetPos);
                
                // Update velocity towards target
                dfloat3 currentDirection = dmath.normalize(projectileData.velocity);
                dfloat3 newDirection = dmath.normalize(dmath.lerp(currentDirection, homingDirection, 
                    projectileData.homingStrength * deltaTime));
                
                projectileData.velocity = newDirection * projectileData.speed;
            }
            
            return currentPos + projectileData.velocity * deltaTime;
        }
        
        private dfloat3 UpdateArcProjectile(ref ProjectileData projectileData, SimulationTransform transform, dfloat deltaTime)
        {
            // Apply gravity to velocity
            projectileData.acceleration = new dfloat3(dfloat.Zero, -projectileData.gravity, dfloat.Zero);
            projectileData.velocity += projectileData.acceleration * deltaTime;
            
            return transform.position + projectileData.velocity * deltaTime;
        }
        
        private dfloat3 UpdateBouncingProjectile(ref ProjectileData projectileData, SimulationTransform transform, dfloat deltaTime)
        {
            // For now, just linear movement (bouncing would require collision detection with terrain)
            return transform.position + projectileData.velocity * deltaTime;
        }
        
        private dfloat3 UpdateSeekingProjectile(ref ProjectileData projectileData, SimulationTransform transform, dfloat deltaTime)
        {
            // Similar to homing but with limited turn rate
            dfloat3 currentPos = transform.position;
            
            if (projectileData.targetEntity != Entity.Null && transformLookup.HasComponent(projectileData.targetEntity))
            {
                var targetTransform = transformLookup[projectileData.targetEntity];
                dfloat3 targetPos = targetTransform.position;
                
                dfloat3 toTarget = dmath.normalize(targetPos - currentPos);
                dfloat3 currentDirection = dmath.normalize(projectileData.velocity);
                
                // Limit turn rate
                dfloat maxTurnThisFrame = projectileData.maxTurnRate * deltaTime;
                dfloat3 newDirection = dmath.normalize(dmath.lerp(currentDirection, toTarget,
                    dmath.min(maxTurnThisFrame, dfloat.One)));
                
                projectileData.velocity = newDirection * projectileData.speed;
            }
            
            return currentPos + projectileData.velocity * deltaTime;
        }
        
        private bool CheckCollisions(Entity projectileEntity, ref ProjectileData projectileData, dfloat3 oldPos, dfloat3 newPos)
        {
            // Check collision with all potential targets
            for (int i = 0; i < targetEntities.Length; i++)
            {
                Entity target = targetEntities[i];
                
                // Skip if this is the source entity
                if (target == projectileData.sourceEntity)
                    continue;
                    
                // Skip if target is dead
                if (targetStats[i].currentHealth <= dfloat.Zero)
                    continue;
                
                dfloat3 targetPos = targetTransforms[i].position;
                
                // Check if projectile path intersects with target
                if (CheckLineCircleIntersection(oldPos, newPos, targetPos, projectileData.collisionRadius))
                {
                    // Hit detected - apply damage and effects
                    ApplyProjectileHit(projectileEntity, ref projectileData, target, targetPos);
                    
                    // Check if projectile should be destroyed
                    if (!projectileData.CanPierce())
                    {
                        return true; // Projectile should be destroyed
                    }
                    else
                    {
                        projectileData.ConsumePierce();
                    }
                }
            }
            
            return false; // Projectile continues
        }
        
        private bool CheckLineCircleIntersection(dfloat3 lineStart, dfloat3 lineEnd, dfloat3 circleCenter, dfloat radius)
        {
            // Simplified 2D collision check (ignoring Y axis for now)
            dfloat2 start = new dfloat2(lineStart.x, lineStart.z);
            dfloat2 end = new dfloat2(lineEnd.x, lineEnd.z);
            dfloat2 center = new dfloat2(circleCenter.x, circleCenter.z);
            
            dfloat2 lineDir = end - start;
            dfloat2 toCenter = center - start;
            
            dfloat lineLength = dmath.length(lineDir);
            if (lineLength <= dfloat.Zero) return false;
            
            dfloat2 lineNorm = lineDir / lineLength;
            dfloat projLength = dmath.dot(toCenter, lineNorm);
            
            // Clamp projection to line segment
            projLength = dmath.clamp(projLength, dfloat.Zero, lineLength);
            
            dfloat2 closestPoint = start + lineNorm * projLength;
            dfloat distance = dmath.distance(closestPoint, center);
            
            return distance <= radius;
        }
        
        private void ApplyProjectileHit(Entity projectileEntity, ref ProjectileData projectileData, Entity target, dfloat3 hitPosition)
        {
            // Create damage data (in real implementation, this would use EntityCommandBuffer)
            // For now, just mark the hit in projectile data
            
            // Apply AOE damage if configured
            if (projectileData.aoeRadius > dfloat.Zero)
            {
                ApplyAOEDamage(projectileData, hitPosition);
            }
        }
        
        private void ApplyAOEDamage(ProjectileData projectileData, dfloat3 explosionCenter)
        {
            int hitCount = 0;
            
            for (int i = 0; i < targetEntities.Length && hitCount < projectileData.maxAoeTargets; i++)
            {
                dfloat distance = dmath.distance(targetTransforms[i].position, explosionCenter);
                
                if (distance <= projectileData.aoeRadius && targetStats[i].currentHealth > dfloat.Zero)
                {
                    // Calculate damage falloff
                    dfloat damageMultiplier = dfloat.One;
                    if (projectileData.aoeFalloff > dfloat.Zero)
                    {
                        dfloat falloff = distance / projectileData.aoeRadius;
                        damageMultiplier = dmath.lerp(dfloat.One, dfloat.One - projectileData.aoeFalloff, falloff);
                    }
                    
                    dfloat damage = projectileData.damage * damageMultiplier;
                    
                    // In real implementation, would create damage entity here
                    hitCount++;
                }
            }
        }
    }
}
