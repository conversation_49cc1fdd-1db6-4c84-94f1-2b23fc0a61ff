using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.Combat.Configuration
{
    /// <summary>
    /// ScriptableObject that defines an attack pattern configuration
    /// Allows designers to configure all aspects of an attack pattern including damage, range, effects, etc.
    /// </summary>
    [CreateAssetMenu(fileName = "New Attack Pattern", menuName = "FlowField/Combat/Attack Pattern Definition")]
    public class AttackPatternDefinition : ScriptableObject
    {
        [Header("Basic Attack Properties")]
        [SerializeField] private string attackName = "Basic Attack";
        [SerializeField] private string description = "A basic attack pattern";
        [SerializeField] private AttackPattern attackPattern = AttackPattern.SingleTarget;
        [SerializeField] private ProjectileType projectileType = ProjectileType.Instant;
        [SerializeField] private DamageType damageType = DamageType.Physical;
        
        [Header("Damage Configuration")]
        [SerializeField] private float baseDamage = 10f;
        [SerializeField] private float damageVariance = 0.1f; // ±10% damage variance
        [SerializeField] private float criticalChance = 0.05f; // 5% crit chance
        [SerializeField] private float criticalMultiplier = 2f; // 2x damage on crit
        [SerializeField] private float armorPenetration = 0f; // 0-1 range
        [SerializeField] private float lifeSteal = 0f; // 0-1 range
        
        [Header("Range and Targeting")]
        [SerializeField] private float attackRange = 5f;
        [SerializeField] private float attackCooldown = 1f;
        [SerializeField] private int maxTargets = 1;
        [SerializeField] private bool requiresLineOfSight = true;
        [SerializeField] private TargetingStrategy targetingStrategy = TargetingStrategy.Nearest;
        
        [Header("Area of Effect")]
        [SerializeField] private float aoeRadius = 0f;
        [SerializeField] private float aoeFalloff = 0.5f; // 0-1 range, damage reduction at edge
        [SerializeField] private int maxAoeTargets = 10;
        
        [Header("Cone Attack Properties")]
        [SerializeField] private float coneAngle = 45f; // Degrees
        [SerializeField] private float coneRange = 3f;
        
        [Header("Multi-Projectile Properties")]
        [SerializeField] private int projectileCount = 1;
        [SerializeField] private float spreadAngle = 30f; // Degrees
        
        [Header("Projectile Properties")]
        [SerializeField] private float projectileSpeed = 10f;
        [SerializeField] private float projectileLifetime = 5f;
        [SerializeField] private float collisionRadius = 0.5f;
        [SerializeField] private int pierceCount = 0;
        [SerializeField] private float pierceDamageDecay = 0.2f; // 20% damage reduction per pierce
        
        [Header("Homing Properties")]
        [SerializeField] private float homingStrength = 2f;
        [SerializeField] private float homingDelay = 0f;
        [SerializeField] private float maxTurnRate = 180f; // Degrees per second
        
        [Header("Arc Properties")]
        [SerializeField] private float arcHeight = 2f;
        [SerializeField] private float gravity = 9.81f;
        
        [Header("Bouncing Properties")]
        [SerializeField] private int maxBounces = 0;
        [SerializeField] private float bounceDecay = 0.2f; // Speed reduction per bounce
        [SerializeField] private float bounceRange = 3f; // Range to search for bounce targets
        
        [Header("Chain Properties")]
        [SerializeField] private int chainCount = 1;
        [SerializeField] private float chainRange = 3f;
        [SerializeField] private float chainDamageDecay = 0.2f; // Damage reduction per chain
        
        [Header("Status Effects")]
        [SerializeField] private float stunDuration = 0f;
        [SerializeField] private float slowAmount = 0f; // 0-1 range
        [SerializeField] private float slowDuration = 0f;
        [SerializeField] private float knockbackForce = 0f;
        
        [Header("Damage Over Time")]
        [SerializeField] private float dotDamage = 0f; // Damage per tick
        [SerializeField] private float dotDuration = 0f;
        [SerializeField] private float dotTickRate = 1f; // Ticks per second
        [SerializeField] private DamageType dotType = DamageType.Poison;
        
        [Header("Visual Effects")]
        [SerializeField] private GameObject projectilePrefab;
        [SerializeField] private GameObject hitEffectPrefab;
        [SerializeField] private GameObject trailEffectPrefab;
        [SerializeField] private GameObject muzzleFlashPrefab;
        
        [Header("Audio")]
        [SerializeField] private AudioClip attackSound;
        [SerializeField] private AudioClip hitSound;
        [SerializeField] private AudioClip criticalHitSound;
        
        // Properties for accessing the configuration values
        public string AttackName => attackName;
        public string Description => description;
        public AttackPattern AttackPattern => attackPattern;
        public ProjectileType ProjectileType => projectileType;
        public DamageType DamageType => damageType;
        
        public dfloat BaseDamage => (dfloat)baseDamage;
        public dfloat DamageVariance => (dfloat)damageVariance;
        public dfloat CriticalChance => (dfloat)criticalChance;
        public dfloat CriticalMultiplier => (dfloat)criticalMultiplier;
        public dfloat ArmorPenetration => (dfloat)armorPenetration;
        public dfloat LifeSteal => (dfloat)lifeSteal;
        
        public dfloat AttackRange => (dfloat)attackRange;
        public dfloat AttackCooldown => (dfloat)attackCooldown;
        public int MaxTargets => maxTargets;
        public bool RequiresLineOfSight => requiresLineOfSight;
        public TargetingStrategy TargetingStrategy => targetingStrategy;
        
        public dfloat AoeRadius => (dfloat)aoeRadius;
        public dfloat AoeFalloff => (dfloat)aoeFalloff;
        public int MaxAoeTargets => maxAoeTargets;
        
        public dfloat ConeAngle => (dfloat)(coneAngle * Mathf.Deg2Rad); // Convert to radians
        public dfloat ConeRange => (dfloat)coneRange;
        
        public int ProjectileCount => projectileCount;
        public dfloat SpreadAngle => (dfloat)(spreadAngle * Mathf.Deg2Rad); // Convert to radians
        
        public dfloat ProjectileSpeed => (dfloat)projectileSpeed;
        public dfloat ProjectileLifetime => (dfloat)projectileLifetime;
        public dfloat CollisionRadius => (dfloat)collisionRadius;
        public int PierceCount => pierceCount;
        public dfloat PierceDamageDecay => (dfloat)pierceDamageDecay;
        
        public dfloat HomingStrength => (dfloat)homingStrength;
        public dfloat HomingDelay => (dfloat)homingDelay;
        public dfloat MaxTurnRate => (dfloat)(maxTurnRate * Mathf.Deg2Rad); // Convert to radians
        
        public dfloat ArcHeight => (dfloat)arcHeight;
        public dfloat Gravity => (dfloat)gravity;
        
        public int MaxBounces => maxBounces;
        public dfloat BounceDecay => (dfloat)bounceDecay;
        public dfloat BounceRange => (dfloat)bounceRange;
        
        public int ChainCount => chainCount;
        public dfloat ChainRange => (dfloat)chainRange;
        public dfloat ChainDamageDecay => (dfloat)chainDamageDecay;
        
        public dfloat StunDuration => (dfloat)stunDuration;
        public dfloat SlowAmount => (dfloat)slowAmount;
        public dfloat SlowDuration => (dfloat)slowDuration;
        public dfloat KnockbackForce => (dfloat)knockbackForce;
        
        public dfloat DotDamage => (dfloat)dotDamage;
        public dfloat DotDuration => (dfloat)dotDuration;
        public dfloat DotTickRate => (dfloat)dotTickRate;
        public DamageType DotType => dotType;
        
        public GameObject ProjectilePrefab => projectilePrefab;
        public GameObject HitEffectPrefab => hitEffectPrefab;
        public GameObject TrailEffectPrefab => trailEffectPrefab;
        public GameObject MuzzleFlashPrefab => muzzleFlashPrefab;
        
        public AudioClip AttackSound => attackSound;
        public AudioClip HitSound => hitSound;
        public AudioClip CriticalHitSound => criticalHitSound;
        
        /// <summary>
        /// Create AttackData from this definition
        /// </summary>
        public AttackData CreateAttackData()
        {
            return new AttackData
            {
                baseDamage = BaseDamage,
                attackRange = AttackRange,
                attackCooldown = AttackCooldown,
                attackPattern = AttackPattern,
                projectileType = ProjectileType,
                damageType = DamageType,
                aoeRadius = AoeRadius,
                coneAngle = ConeAngle,
                coneRange = ConeRange,
                maxTargets = MaxTargets,
                pierceCount = PierceCount,
                chainCount = ChainCount,
                chainRange = ChainRange,
                projectileCount = ProjectileCount,
                spreadAngle = SpreadAngle,
                projectileSpeed = ProjectileSpeed,
                projectileLifetime = ProjectileLifetime,
                homingStrength = HomingStrength,
                arcHeight = ArcHeight,
                criticalChance = CriticalChance,
                criticalMultiplier = CriticalMultiplier,
                armorPenetration = ArmorPenetration,
                lifeSteal = LifeSteal,
                stunDuration = StunDuration,
                slowAmount = SlowAmount,
                slowDuration = SlowDuration,
                dotDamage = DotDamage,
                dotDuration = DotDuration,
                dotTickRate = DotTickRate,
                combatState = CombatState.None
            };
        }
        
        /// <summary>
        /// Validate the configuration for common issues
        /// </summary>
        public bool ValidateConfiguration(out string errorMessage)
        {
            errorMessage = "";
            
            if (baseDamage <= 0)
            {
                errorMessage = "Base damage must be greater than 0";
                return false;
            }
            
            if (attackRange <= 0)
            {
                errorMessage = "Attack range must be greater than 0";
                return false;
            }
            
            if (attackCooldown <= 0)
            {
                errorMessage = "Attack cooldown must be greater than 0";
                return false;
            }
            
            if (maxTargets <= 0)
            {
                errorMessage = "Max targets must be greater than 0";
                return false;
            }
            
            // Pattern-specific validations
            switch (attackPattern)
            {
                case AttackPattern.AOEPosition:
                case AttackPattern.AOESelf:
                    if (aoeRadius <= 0)
                    {
                        errorMessage = "AOE attacks require AOE radius greater than 0";
                        return false;
                    }
                    break;
                    
                case AttackPattern.Cone:
                    if (coneAngle <= 0 || coneRange <= 0)
                    {
                        errorMessage = "Cone attacks require cone angle and range greater than 0";
                        return false;
                    }
                    break;
                    
                case AttackPattern.MultiLinearWave:
                    if (projectileCount <= 1)
                    {
                        errorMessage = "Multi-linear wave attacks require projectile count greater than 1";
                        return false;
                    }
                    break;
                    
                case AttackPattern.Chain:
                    if (chainCount <= 1 || chainRange <= 0)
                    {
                        errorMessage = "Chain attacks require chain count > 1 and chain range > 0";
                        return false;
                    }
                    break;
            }
            
            return true;
        }
    }
}
