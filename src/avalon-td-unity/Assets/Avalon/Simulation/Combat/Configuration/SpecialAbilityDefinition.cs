using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.Combat.Configuration
{
    /// <summary>
    /// Types of special abilities
    /// </summary>
    public enum AbilityType : byte
    {
        ActiveTargeted = 0,     // Requires target selection
        ActiveArea = 1,         // Requires area selection
        ActiveSelf = 2,         // Self-cast ability
        Passive = 3,            // Always active
        Triggered = 4,          // Triggers on specific conditions
        Aura = 5               // Affects nearby units
    }
    
    /// <summary>
    /// Conditions that can trigger abilities
    /// </summary>
    public enum TriggerCondition : byte
    {
        None = 0,
        OnAttack = 1,
        OnHit = 2,
        OnKill = 3,
        OnDamaged = 4,
        OnLowHealth = 5,
        OnEnemyNear = 6,
        OnCooldownReady = 7,
        OnTargetAcquired = 8
    }
    
    /// <summary>
    /// ScriptableObject that defines a special ability configuration
    /// Allows designers to create complex abilities with various effects and triggers
    /// </summary>
    [CreateAssetMenu(fileName = "New Special Ability", menuName = "FlowField/Combat/Special Ability Definition")]
    public class SpecialAbilityDefinition : ScriptableObject
    {
        [Header("Ability Identity")]
        [SerializeField] private string abilityName = "Special Ability";
        [SerializeField] private string description = "A special ability";
        [SerializeField] private Sprite abilityIcon;
        [SerializeField] private AbilityType abilityType = AbilityType.ActiveTargeted;
        
        [Header("Activation")]
        [SerializeField] private float cooldown = 10f;
        [SerializeField] private float castTime = 0f;
        [SerializeField] private float channelTime = 0f;
        [SerializeField] private float range = 5f;
        [SerializeField] private bool interruptible = true;
        
        [Header("Trigger Conditions (for Triggered abilities)")]
        [SerializeField] private TriggerCondition triggerCondition = TriggerCondition.None;
        [SerializeField] private float triggerChance = 1f; // 0-1 range
        [SerializeField] private float triggerThreshold = 0.5f; // For health-based triggers
        
        [Header("Area Properties")]
        [SerializeField] private float areaRadius = 3f;
        [SerializeField] private float areaAngle = 360f; // For cone-shaped areas
        [SerializeField] private int maxTargets = 10;
        [SerializeField] private bool affectsAllies = false;
        [SerializeField] private bool affectsEnemies = true;
        [SerializeField] private bool affectsSelf = false;
        
        [Header("Damage Effects")]
        [SerializeField] private float damage = 0f;
        [SerializeField] private DamageType damageType = DamageType.Magical;
        [SerializeField] private float damageScaling = 1f; // Scales with unit's attack damage
        [SerializeField] private bool canCrit = false;
        [SerializeField] private float armorPenetration = 0f;
        
        [Header("Healing Effects")]
        [SerializeField] private float healing = 0f;
        [SerializeField] private float healingScaling = 0f; // Scales with unit's max health
        [SerializeField] private bool canOverheal = false;
        
        [Header("Status Effects")]
        [SerializeField] private float stunDuration = 0f;
        [SerializeField] private float slowAmount = 0f;
        [SerializeField] private float slowDuration = 0f;
        [SerializeField] private float knockbackForce = 0f;
        [SerializeField] private float shieldAmount = 0f;
        [SerializeField] private float shieldDuration = 0f;
        
        [Header("Buff/Debuff Effects")]
        [SerializeField] private float damageBuffAmount = 0f;
        [SerializeField] private float damageBuffDuration = 0f;
        [SerializeField] private float speedBuffAmount = 0f;
        [SerializeField] private float speedBuffDuration = 0f;
        [SerializeField] private float attackSpeedBuffAmount = 0f;
        [SerializeField] private float attackSpeedBuffDuration = 0f;
        
        [Header("DOT Effects")]
        [SerializeField] private float dotDamage = 0f;
        [SerializeField] private float dotDuration = 0f;
        [SerializeField] private float dotTickRate = 1f;
        [SerializeField] private DamageType dotType = DamageType.Poison;
        
        [Header("Special Mechanics")]
        [SerializeField] private bool teleportsToTarget = false;
        [SerializeField] private bool createsProjectile = false;
        [SerializeField] private AttackPatternDefinition projectilePattern;
        [SerializeField] private bool summonUnits = false;
        [SerializeField] private CombatUnitDefinition summonedUnit;
        [SerializeField] private int summonCount = 1;
        [SerializeField] private float summonDuration = 10f;
        
        [Header("Aura Properties (for Aura abilities)")]
        [SerializeField] private float auraRadius = 5f;
        [SerializeField] private bool auraAffectsAllies = true;
        [SerializeField] private bool auraAffectsEnemies = false;
        [SerializeField] private float auraDamageBonus = 0f;
        [SerializeField] private float auraSpeedBonus = 0f;
        [SerializeField] private float auraAttackSpeedBonus = 0f;
        
        [Header("Visual and Audio")]
        [SerializeField] private GameObject castEffectPrefab;
        [SerializeField] private GameObject impactEffectPrefab;
        [SerializeField] private GameObject auraEffectPrefab;
        [SerializeField] private AudioClip castSound;
        [SerializeField] private AudioClip impactSound;
        
        // Properties for accessing configuration values
        public string AbilityName => abilityName;
        public string Description => description;
        public Sprite AbilityIcon => abilityIcon;
        public AbilityType AbilityType => abilityType;
        
        public dfloat Cooldown => (dfloat)cooldown;
        public dfloat CastTime => (dfloat)castTime;
        public dfloat ChannelTime => (dfloat)channelTime;
        public dfloat Range => (dfloat)range;
        public bool Interruptible => interruptible;
        
        public TriggerCondition TriggerCondition => triggerCondition;
        public dfloat TriggerChance => (dfloat)triggerChance;
        public dfloat TriggerThreshold => (dfloat)triggerThreshold;
        
        public dfloat AreaRadius => (dfloat)areaRadius;
        public dfloat AreaAngle => (dfloat)(areaAngle * Mathf.Deg2Rad);
        public int MaxTargets => maxTargets;
        public bool AffectsAllies => affectsAllies;
        public bool AffectsEnemies => affectsEnemies;
        public bool AffectsSelf => affectsSelf;
        
        public dfloat Damage => (dfloat)damage;
        public DamageType DamageType => damageType;
        public dfloat DamageScaling => (dfloat)damageScaling;
        public bool CanCrit => canCrit;
        public dfloat ArmorPenetration => (dfloat)armorPenetration;
        
        public dfloat Healing => (dfloat)healing;
        public dfloat HealingScaling => (dfloat)healingScaling;
        public bool CanOverheal => canOverheal;
        
        public dfloat StunDuration => (dfloat)stunDuration;
        public dfloat SlowAmount => (dfloat)slowAmount;
        public dfloat SlowDuration => (dfloat)slowDuration;
        public dfloat KnockbackForce => (dfloat)knockbackForce;
        public dfloat ShieldAmount => (dfloat)shieldAmount;
        public dfloat ShieldDuration => (dfloat)shieldDuration;
        
        public dfloat DamageBuffAmount => (dfloat)damageBuffAmount;
        public dfloat DamageBuffDuration => (dfloat)damageBuffDuration;
        public dfloat SpeedBuffAmount => (dfloat)speedBuffAmount;
        public dfloat SpeedBuffDuration => (dfloat)speedBuffDuration;
        public dfloat AttackSpeedBuffAmount => (dfloat)attackSpeedBuffAmount;
        public dfloat AttackSpeedBuffDuration => (dfloat)attackSpeedBuffDuration;
        
        public dfloat DotDamage => (dfloat)dotDamage;
        public dfloat DotDuration => (dfloat)dotDuration;
        public dfloat DotTickRate => (dfloat)dotTickRate;
        public DamageType DotType => dotType;
        
        public bool TeleportsToTarget => teleportsToTarget;
        public bool CreatesProjectile => createsProjectile;
        public AttackPatternDefinition ProjectilePattern => projectilePattern;
        public bool SummonUnits => summonUnits;
        public CombatUnitDefinition SummonedUnit => summonedUnit;
        public int SummonCount => summonCount;
        public dfloat SummonDuration => (dfloat)summonDuration;
        
        public dfloat AuraRadius => (dfloat)auraRadius;
        public bool AuraAffectsAllies => auraAffectsAllies;
        public bool AuraAffectsEnemies => auraAffectsEnemies;
        public dfloat AuraDamageBonus => (dfloat)auraDamageBonus;
        public dfloat AuraSpeedBonus => (dfloat)auraSpeedBonus;
        public dfloat AuraAttackSpeedBonus => (dfloat)auraAttackSpeedBonus;
        
        public GameObject CastEffectPrefab => castEffectPrefab;
        public GameObject ImpactEffectPrefab => impactEffectPrefab;
        public GameObject AuraEffectPrefab => auraEffectPrefab;
        public AudioClip CastSound => castSound;
        public AudioClip ImpactSound => impactSound;
        
        /// <summary>
        /// Check if this ability should trigger based on the condition
        /// </summary>
        public bool ShouldTrigger(TriggerCondition condition, dfloat currentHealth, dfloat maxHealth, uint randomSeed)
        {
            if (abilityType != AbilityType.Triggered)
                return false;
                
            if (triggerCondition != condition)
                return false;
                
            // Check trigger chance
            if (triggerChance < 1f)
            {
                var random = new Unity.Mathematics.Random(randomSeed);
                if (random.NextFloat() > triggerChance)
                    return false;
            }
            
            // Check health threshold for health-based triggers
            if (condition == TriggerCondition.OnLowHealth)
            {
                dfloat healthPercent = currentHealth / maxHealth;
                return healthPercent <= TriggerThreshold;
            }
            
            return true;
        }
        
        /// <summary>
        /// Calculate scaled damage based on unit stats
        /// </summary>
        public dfloat CalculateScaledDamage(dfloat unitAttackDamage)
        {
            return Damage + (unitAttackDamage * DamageScaling);
        }
        
        /// <summary>
        /// Calculate scaled healing based on unit stats
        /// </summary>
        public dfloat CalculateScaledHealing(dfloat unitMaxHealth)
        {
            return Healing + (unitMaxHealth * HealingScaling);
        }
        
        /// <summary>
        /// Validate the ability configuration
        /// </summary>
        public bool ValidateConfiguration(out string errorMessage)
        {
            errorMessage = "";
            
            if (string.IsNullOrEmpty(abilityName))
            {
                errorMessage = "Ability name cannot be empty";
                return false;
            }
            
            if (cooldown < 0)
            {
                errorMessage = "Cooldown cannot be negative";
                return false;
            }
            
            if (castTime < 0)
            {
                errorMessage = "Cast time cannot be negative";
                return false;
            }
            
            if (abilityType == AbilityType.ActiveTargeted && range <= 0)
            {
                errorMessage = "Targeted abilities must have range greater than 0";
                return false;
            }
            
            if (abilityType == AbilityType.ActiveArea && areaRadius <= 0)
            {
                errorMessage = "Area abilities must have area radius greater than 0";
                return false;
            }
            
            if (abilityType == AbilityType.Triggered && triggerCondition == TriggerCondition.None)
            {
                errorMessage = "Triggered abilities must have a trigger condition";
                return false;
            }
            
            if (createsProjectile && projectilePattern == null)
            {
                errorMessage = "Abilities that create projectiles must have a projectile pattern";
                return false;
            }
            
            if (summonUnits && summonedUnit == null)
            {
                errorMessage = "Abilities that summon units must have a summoned unit definition";
                return false;
            }
            
            return true;
        }
    }
}
