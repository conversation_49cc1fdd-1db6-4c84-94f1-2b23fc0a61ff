using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.Combat.Configuration
{
    /// <summary>
    /// ScriptableObject that defines a combat unit's configuration
    /// Combines unit stats with attack patterns and special abilities
    /// </summary>
    [CreateAssetMenu(fileName = "New Combat Unit", menuName = "FlowField/Combat/Combat Unit Definition")]
    public class CombatUnitDefinition : ScriptableObject
    {
        [Header("Unit Identity")]
        [SerializeField] private string unitName = "Combat Unit";
        [SerializeField] private string description = "A combat-capable unit";
        [SerializeField] private Sprite unitIcon;
        [SerializeField] private GameObject unitPrefab;
        
        [Header("Basic Stats")]
        [SerializeField] private float maxHealth = 100f;
        [SerializeField] private float armor = 0f;
        [SerializeField] private float magicResistance = 0f;
        [SerializeField] private float movementSpeed = 3f;
        [SerializeField] private float unitRadius = 0.5f;
        
        [Header("Combat Configuration")]
        [SerializeField] private AttackPatternDefinition primaryAttack;
        [SerializeField] private AttackPatternDefinition[] secondaryAttacks;
        [SerializeField] private float attackRangeMultiplier = 1f;
        [SerializeField] private float attackDamageMultiplier = 1f;
        [SerializeField] private float attackSpeedMultiplier = 1f;
        
        [Header("Targeting Preferences")]
        [SerializeField] private TargetingStrategy preferredTargeting = TargetingStrategy.Nearest;
        [SerializeField] private bool canTargetAir = true;
        [SerializeField] private bool canTargetGround = true;
        [SerializeField] private bool canTargetStructures = false;
        [SerializeField] private uint targetLayerMask = 0xFFFFFFFF;
        
        [Header("Special Abilities")]
        [SerializeField] private SpecialAbilityDefinition[] specialAbilities;
        [SerializeField] private float abilityCooldownMultiplier = 1f;
        
        [Header("Resistances and Immunities")]
        [SerializeField] private DamageType[] resistantTo;
        [SerializeField] private DamageType[] immuneTo;
        [SerializeField] private DamageType[] weakTo;
        [SerializeField] private float resistanceAmount = 0.5f; // 50% damage reduction
        [SerializeField] private float weaknessAmount = 1.5f; // 50% extra damage
        
        [Header("Status Effect Resistances")]
        [SerializeField] private bool immuneToStun = false;
        [SerializeField] private bool immuneToSlow = false;
        [SerializeField] private bool immuneToDOT = false;
        [SerializeField] private float statusEffectResistance = 0f; // 0-1 range
        
        [Header("Economic Properties")]
        [SerializeField] private int buildCost = 100;
        [SerializeField] private int upgradeCost = 50;
        [SerializeField] private int sellValue = 75;
        [SerializeField] private float buildTime = 2f;
        
        // Properties for accessing configuration values
        public string UnitName => unitName;
        public string Description => description;
        public Sprite UnitIcon => unitIcon;
        public GameObject UnitPrefab => unitPrefab;
        
        public dfloat MaxHealth => (dfloat)maxHealth;
        public dfloat Armor => (dfloat)armor;
        public dfloat MagicResistance => (dfloat)magicResistance;
        public dfloat MovementSpeed => (dfloat)movementSpeed;
        public dfloat UnitRadius => (dfloat)unitRadius;
        
        public AttackPatternDefinition PrimaryAttack => primaryAttack;
        public AttackPatternDefinition[] SecondaryAttacks => secondaryAttacks;
        public dfloat AttackRangeMultiplier => (dfloat)attackRangeMultiplier;
        public dfloat AttackDamageMultiplier => (dfloat)attackDamageMultiplier;
        public dfloat AttackSpeedMultiplier => (dfloat)attackSpeedMultiplier;
        
        public TargetingStrategy PreferredTargeting => preferredTargeting;
        public bool CanTargetAir => canTargetAir;
        public bool CanTargetGround => canTargetGround;
        public bool CanTargetStructures => canTargetStructures;
        public uint TargetLayerMask => targetLayerMask;
        
        public SpecialAbilityDefinition[] SpecialAbilities => specialAbilities;
        public dfloat AbilityCooldownMultiplier => (dfloat)abilityCooldownMultiplier;
        
        public DamageType[] ResistantTo => resistantTo;
        public DamageType[] ImmuneTo => immuneTo;
        public DamageType[] WeakTo => weakTo;
        public dfloat ResistanceAmount => (dfloat)resistanceAmount;
        public dfloat WeaknessAmount => (dfloat)weaknessAmount;
        
        public bool ImmuneToStun => immuneToStun;
        public bool ImmuneToSlow => immuneToSlow;
        public bool ImmuneToDOT => immuneToDOT;
        public dfloat StatusEffectResistance => (dfloat)statusEffectResistance;
        
        public int BuildCost => buildCost;
        public int UpgradeCost => upgradeCost;
        public int SellValue => sellValue;
        public dfloat BuildTime => (dfloat)buildTime;
        
        /// <summary>
        /// Create UnitStats from this definition
        /// </summary>
        public UnitStats CreateUnitStats()
        {
            var stats = new UnitStats
            {
                maxHealth = MaxHealth,
                currentHealth = MaxHealth,
                maxSpeed = MovementSpeed,
                radius = UnitRadius,
                acceleration = (dfloat)10, // Default acceleration
                deceleration = (dfloat)10, // Default deceleration
                rotationSpeed = (dfloat)180 // Default rotation speed
            };
            
            // Set combat stats from primary attack if available
            if (primaryAttack != null)
            {
                stats.attackDamage = primaryAttack.BaseDamage * AttackDamageMultiplier;
                stats.attackRange = primaryAttack.AttackRange * AttackRangeMultiplier;
                stats.attackCooldown = primaryAttack.AttackCooldown / AttackSpeedMultiplier;
            }
            
            return stats;
        }
        
        /// <summary>
        /// Create AttackData from primary attack pattern
        /// </summary>
        public AttackData CreateAttackData()
        {
            if (primaryAttack == null)
                return AttackData.CreateDefault();
                
            var attackData = primaryAttack.CreateAttackData();
            
            // Apply unit-specific multipliers
            attackData.baseDamage *= AttackDamageMultiplier;
            attackData.attackRange *= AttackRangeMultiplier;
            attackData.attackCooldown /= AttackSpeedMultiplier;
            
            return attackData;
        }
        
        /// <summary>
        /// Create TargetingData from this definition
        /// </summary>
        public TargetingData CreateTargetingData()
        {
            var targetingData = TargetingData.CreateDefault();
            
            targetingData.strategy = PreferredTargeting;
            targetingData.canTargetAir = CanTargetAir;
            targetingData.canTargetGround = CanTargetGround;
            targetingData.canTargetStructures = CanTargetStructures;
            targetingData.targetLayerMask = TargetLayerMask;
            
            if (primaryAttack != null)
            {
                targetingData.targetingRange = primaryAttack.AttackRange * AttackRangeMultiplier;
                targetingData.targetLossRange = targetingData.targetingRange * (dfloat)1.2f;
                targetingData.requireLineOfSight = primaryAttack.RequiresLineOfSight;
            }
            
            return targetingData;
        }
        
        /// <summary>
        /// Check if this unit is resistant to a damage type
        /// </summary>
        public bool IsResistantTo(DamageType damageType)
        {
            return System.Array.Exists(resistantTo, type => type == damageType);
        }
        
        /// <summary>
        /// Check if this unit is immune to a damage type
        /// </summary>
        public bool IsImmuneTo(DamageType damageType)
        {
            return System.Array.Exists(immuneTo, type => type == damageType);
        }
        
        /// <summary>
        /// Check if this unit is weak to a damage type
        /// </summary>
        public bool IsWeakTo(DamageType damageType)
        {
            return System.Array.Exists(weakTo, type => type == damageType);
        }
        
        /// <summary>
        /// Get damage multiplier for a specific damage type
        /// </summary>
        public dfloat GetDamageMultiplier(DamageType damageType)
        {
            if (IsImmuneTo(damageType))
                return dfloat.Zero;
            else if (IsResistantTo(damageType))
                return dfloat.One - ResistanceAmount;
            else if (IsWeakTo(damageType))
                return WeaknessAmount;
            else
                return dfloat.One;
        }
        
        /// <summary>
        /// Check if this unit can use a specific attack pattern
        /// </summary>
        public bool HasAttackPattern(AttackPattern pattern)
        {
            if (primaryAttack != null && primaryAttack.AttackPattern == pattern)
                return true;
                
            if (secondaryAttacks != null)
            {
                foreach (var attack in secondaryAttacks)
                {
                    if (attack != null && attack.AttackPattern == pattern)
                        return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// Get attack pattern definition by type
        /// </summary>
        public AttackPatternDefinition GetAttackPattern(AttackPattern pattern)
        {
            if (primaryAttack != null && primaryAttack.AttackPattern == pattern)
                return primaryAttack;
                
            if (secondaryAttacks != null)
            {
                foreach (var attack in secondaryAttacks)
                {
                    if (attack != null && attack.AttackPattern == pattern)
                        return attack;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Validate the unit configuration
        /// </summary>
        public bool ValidateConfiguration(out string errorMessage)
        {
            errorMessage = "";
            
            if (maxHealth <= 0)
            {
                errorMessage = "Max health must be greater than 0";
                return false;
            }
            
            if (primaryAttack == null)
            {
                errorMessage = "Primary attack pattern is required";
                return false;
            }
            
            if (!primaryAttack.ValidateConfiguration(out string attackError))
            {
                errorMessage = $"Primary attack validation failed: {attackError}";
                return false;
            }
            
            // Validate secondary attacks
            if (secondaryAttacks != null)
            {
                for (int i = 0; i < secondaryAttacks.Length; i++)
                {
                    if (secondaryAttacks[i] != null && !secondaryAttacks[i].ValidateConfiguration(out string secondaryError))
                    {
                        errorMessage = $"Secondary attack {i} validation failed: {secondaryError}";
                        return false;
                    }
                }
            }
            
            return true;
        }
    }
}
