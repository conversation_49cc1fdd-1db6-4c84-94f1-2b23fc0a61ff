﻿namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// Different types of projectiles
    /// </summary>
    public enum ProjectileType : byte
    {
        /// <summary>Instant hit, no travel time</summary>
        Instant = 0,
        
        /// <summary>Linear projectile with constant velocity</summary>
        Linear = 1,
        
        /// <summary>Homing projectile that tracks target</summary>
        Homing = 2,
        
        /// <summary>Arcing projectile affected by gravity</summary>
        Arc = 3,
        
        /// <summary>Bouncing projectile that ricochets</summary>
        Bouncing = 4,
        
        /// <summary>Seeking projectile with limited turn rate</summary>
        Seeking = 5
    }
}