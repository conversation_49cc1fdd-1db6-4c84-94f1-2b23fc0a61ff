using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Collections;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Targeting
{
    /// <summary>
    /// Utility functions for target selection and validation
    /// Provides helper methods for line-of-sight, range checking, and target filtering
    /// </summary>
    public static class TargetingUtils
    {
        /// <summary>
        /// Check if there's a clear line of sight between two positions
        /// </summary>
        public static bool HasLineOfSight(dfloat3 from, dfloat3 to, dfloat? maxDistance = null)
        {
            maxDistance ??= dfloat.Zero;
            dfloat distance = dmath.distance(from, to);
            
            // Check max distance if specified
            if (maxDistance > dfloat.Zero && distance > maxDistance)
                return false;
            
            // For now, assume clear line of sight
            // In a full implementation, this would raycast against obstacles
            return true;
        }
        
        /// <summary>
        /// Check if a target is within the specified range
        /// </summary>
        public static bool IsInRange(dfloat3 attackerPos, dfloat3 targetPos, dfloat range)
        {
            dfloat distance = dmath.distance(attackerPos, targetPos);
            return distance <= range;
        }
        
        /// <summary>
        /// Check if a target is within a cone area
        /// </summary>
        public static bool IsInCone(dfloat3 attackerPos, dfloat3 attackerForward, dfloat3 targetPos, 
            dfloat coneAngle, dfloat coneRange)
        {
            dfloat3 toTarget = targetPos - attackerPos;
            dfloat distance = dmath.length(toTarget);
            
            // Check range first
            if (distance > coneRange || distance <= dfloat.Zero)
                return false;
            
            // Check angle
            dfloat3 dirToTarget = toTarget / distance;
            dfloat angle = dmath.acos(dmath.dot(attackerForward, dirToTarget));
            
            return angle <= coneAngle * (dfloat)0.5f;
        }
        
        /// <summary>
        /// Get all targets within a circular area
        /// </summary>
        public static void GetTargetsInCircle(dfloat3 center, dfloat radius, 
            NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms,
            NativeList<Entity> results, int maxTargets = int.MaxValue)
        {
            results.Clear();
            int count = 0;
            
            for (int i = 0; i < entities.Length && count < maxTargets; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, center);
                if (distance <= radius)
                {
                    results.Add(entities[i]);
                    count++;
                }
            }
        }
        
        /// <summary>
        /// Get all targets within a cone area
        /// </summary>
        public static void GetTargetsInCone(dfloat3 attackerPos, dfloat3 attackerForward, dfloat coneAngle, dfloat coneRange,
            NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms,
            NativeList<Entity> results, int maxTargets = int.MaxValue)
        {
            results.Clear();
            int count = 0;
            
            for (int i = 0; i < entities.Length && count < maxTargets; i++)
            {
                if (IsInCone(attackerPos, attackerForward, transforms[i].position, coneAngle, coneRange))
                {
                    results.Add(entities[i]);
                    count++;
                }
            }
        }
        
        /// <summary>
        /// Find the nearest target to a position
        /// </summary>
        public static Entity FindNearestTarget(dfloat3 position, dfloat maxRange,
            NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms)
        {
            Entity nearest = Entity.Null;
            dfloat nearestDistance = dfloat.MAX_VALUE;
            
            for (int i = 0; i < entities.Length; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, position);
                if (distance <= maxRange && distance < nearestDistance)
                {
                    nearest = entities[i];
                    nearestDistance = distance;
                }
            }
            
            return nearest;
        }
        
        /// <summary>
        /// Find the target with the highest health
        /// </summary>
        public static Entity FindHighestHealthTarget(dfloat3 position, dfloat maxRange,
            NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms, 
            NativeArray<UnitStats> stats)
        {
            Entity best = Entity.Null;
            dfloat highestHealth = dfloat.Zero;
            
            for (int i = 0; i < entities.Length; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, position);
                if (distance <= maxRange && stats[i].currentHealth > highestHealth)
                {
                    best = entities[i];
                    highestHealth = stats[i].currentHealth;
                }
            }
            
            return best;
        }
        
        /// <summary>
        /// Find the target with the lowest health
        /// </summary>
        public static Entity FindLowestHealthTarget(dfloat3 position, dfloat maxRange,
            NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms, 
            NativeArray<UnitStats> stats)
        {
            Entity best = Entity.Null;
            dfloat lowestHealth = dfloat.MAX_VALUE;
            
            for (int i = 0; i < entities.Length; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, position);
                if (distance <= maxRange && stats[i].currentHealth > dfloat.Zero && stats[i].currentHealth < lowestHealth)
                {
                    best = entities[i];
                    lowestHealth = stats[i].currentHealth;
                }
            }
            
            return best;
        }
        
        /// <summary>
        /// Calculate target priority based on multiple factors
        /// </summary>
        public static dfloat CalculateTargetPriority(dfloat3 attackerPos, dfloat3 targetPos, UnitStats targetStats,
            TargetingStrategy strategy, dfloat? distanceWeightParam = null, dfloat? healthWeightParam = null)
        {
            var distanceWeight = distanceWeightParam ?? dfloat.One;
            var healthWeight = healthWeightParam ?? dfloat.One;
            
            dfloat distance = dmath.distance(attackerPos, targetPos);
            dfloat health = targetStats.currentHealth;
            dfloat maxHealth = targetStats.maxHealth;
            dfloat damage = targetStats.attackDamage;
            
            dfloat priority = dfloat.Zero;
            
            switch (strategy)
            {
                case TargetingStrategy.Nearest:
                    priority = dfloat.One / dmath.max(distance, (dfloat)0.1f); // Inverse distance
                    break;
                    
                case TargetingStrategy.LowestHealth:
                    priority = (maxHealth - health) * healthWeight + (dfloat.One / dmath.max(distance, (dfloat)0.1f)) * distanceWeight;
                    break;
                    
                case TargetingStrategy.HighestHealth:
                    priority = health * healthWeight - distance * distanceWeight;
                    break;
                    
                case TargetingStrategy.Strongest:
                    dfloat strength = maxHealth + damage;
                    priority = strength * healthWeight - distance * distanceWeight;
                    break;
                    
                case TargetingStrategy.Weakest:
                    dfloat weakness = dfloat.One / dmath.max(maxHealth + damage, (dfloat)1);
                    priority = weakness * healthWeight + (dfloat.One / dmath.max(distance, (dfloat)0.1f)) * distanceWeight;
                    break;
                    
                default:
                    priority = dfloat.One / dmath.max(distance, (dfloat)0.1f);
                    break;
            }
            
            return priority;
        }
        
        /// <summary>
        /// Predict where a moving target will be
        /// </summary>
        public static dfloat3 PredictTargetPosition(dfloat3 currentPos, dfloat3 velocity, dfloat predictionTime)
        {
            return currentPos + velocity * predictionTime;
        }
        
        /// <summary>
        /// Calculate intercept position for a projectile to hit a moving target
        /// </summary>
        public static dfloat3 CalculateInterceptPosition(dfloat3 shooterPos, dfloat3 targetPos, dfloat3 targetVelocity, 
            dfloat projectileSpeed)
        {
            dfloat3 toTarget = targetPos - shooterPos;
            dfloat distance = dmath.length(toTarget);
            
            if (distance <= dfloat.Zero || projectileSpeed <= dfloat.Zero)
                return targetPos;
            
            // Simple linear prediction
            dfloat timeToHit = distance / projectileSpeed;
            return targetPos + targetVelocity * timeToHit;
        }
        
        /// <summary>
        /// Check if a target matches the specified filters
        /// </summary>
        public static bool MatchesFilters(UnitStats targetStats, TargetingData targetingData)
        {
            // Health range check
            if (targetingData.minTargetHealth > dfloat.Zero && targetStats.currentHealth < targetingData.minTargetHealth)
                return false;
                
            if (targetingData.maxTargetHealth > dfloat.Zero && targetStats.currentHealth > targetingData.maxTargetHealth)
                return false;
            
            // Target must be alive
            if (targetStats.currentHealth <= dfloat.Zero)
                return false;
            
            // Additional filter checks would go here
            // - Layer masks
            // - Tag exclusions
            // - Special unit types
            
            return true;
        }
        
        /// <summary>
        /// Sort targets by priority for the given strategy
        /// </summary>
        public static void SortTargetsByPriority(NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms,
            NativeArray<UnitStats> stats, dfloat3 attackerPos, TargetingStrategy strategy)
        {
            // Simple bubble sort for small arrays (would use more efficient sorting for larger arrays)
            for (int i = 0; i < entities.Length - 1; i++)
            {
                for (int j = 0; j < entities.Length - i - 1; j++)
                {
                    dfloat priority1 = CalculateTargetPriority(attackerPos, transforms[j].position, stats[j], strategy);
                    dfloat priority2 = CalculateTargetPriority(attackerPos, transforms[j + 1].position, stats[j + 1], strategy);
                    
                    if (priority1 < priority2)
                    {
                        // Swap entities
                        (entities[j], entities[j + 1]) = (entities[j + 1], entities[j]);

                        // Swap transforms
                        (transforms[j], transforms[j + 1]) = (transforms[j + 1], transforms[j]);

                        // Swap stats
                        (stats[j], stats[j + 1]) = (stats[j + 1], stats[j]);
                    }
                }
            }
        }
    }
}
