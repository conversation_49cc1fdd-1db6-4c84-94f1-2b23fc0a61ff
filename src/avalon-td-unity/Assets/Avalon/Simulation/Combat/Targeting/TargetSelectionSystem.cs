using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Targeting
{
    /// <summary>
    /// System that handles target selection and acquisition for all combat units
    /// Processes different targeting strategies and maintains target tracking
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    public partial struct TargetSelectionSystem : ISystem
    {
        private EntityQuery attackerQuery;
        private EntityQuery potentialTargetQuery;
        private EntityQuery fixedTimestepQuery;
        
        // Component lookups for efficient access
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<UnitStats> unitStatsLookup;
        private ComponentLookup<FlowFieldFollower> followerLookup;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for entities that need targets
            attackerQuery = SystemAPI.QueryBuilder()
                .WithAll<AttackData, TargetingData, SimulationTransform>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for potential targets
            potentialTargetQuery = SystemAPI.QueryBuilder()
                .WithAll<SimulationTransform, UnitStats>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for fixed timestep
            fixedTimestepQuery = SystemAPI.QueryBuilder()
                .WithAll<FixedTimestep>()
                .Build();
                
            // Initialize component lookups
            transformLookup = SystemAPI.GetComponentLookup<SimulationTransform>(true);
            unitStatsLookup = SystemAPI.GetComponentLookup<UnitStats>(true);
            followerLookup = SystemAPI.GetComponentLookup<FlowFieldFollower>(true);
            
            state.RequireForUpdate(attackerQuery);
            state.RequireForUpdate(fixedTimestepQuery);
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Update component lookups
            transformLookup.Update(ref state);
            unitStatsLookup.Update(ref state);
            followerLookup.Update(ref state);
            
            // Get arrays of potential targets for efficient processing
            var targetEntities = potentialTargetQuery.ToEntityArray(Allocator.TempJob);
            var targetTransforms = potentialTargetQuery.ToComponentDataArray<SimulationTransform>(Allocator.TempJob);
            var targetStats = potentialTargetQuery.ToComponentDataArray<UnitStats>(Allocator.TempJob);
            
            // Schedule target selection job
            var targetSelectionJob = new TargetSelectionJob
            {
                currentTime = fixedTimestep.currentTime,
                transformLookup = transformLookup,
                unitStatsLookup = unitStatsLookup,
                followerLookup = followerLookup,
                targetEntities = targetEntities,
                targetTransforms = targetTransforms,
                targetStats = targetStats
            };
            
            state.Dependency = targetSelectionJob.ScheduleParallel(attackerQuery, state.Dependency);
            
            // Dispose arrays after job completion
            state.Dependency = targetEntities.Dispose(state.Dependency);
            state.Dependency = targetTransforms.Dispose(state.Dependency);
            state.Dependency = targetStats.Dispose(state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that processes target selection for all attacking entities
    /// </summary>
    [BurstCompile]
    public partial struct TargetSelectionJob : IJobEntity
    {
        [ReadOnly] public dfloat currentTime;
        [ReadOnly] public ComponentLookup<SimulationTransform> transformLookup;
        [ReadOnly] public ComponentLookup<UnitStats> unitStatsLookup;
        [ReadOnly] public ComponentLookup<FlowFieldFollower> followerLookup;
        
        [ReadOnly] public NativeArray<Entity> targetEntities;
        [ReadOnly] public NativeArray<SimulationTransform> targetTransforms;
        [ReadOnly] public NativeArray<UnitStats> targetStats;
        
        public void Execute(Entity entity, ref AttackData attackData, ref TargetingData targetingData,
            in SimulationTransform transform)
        {
            dfloat3 attackerPos = transform.position;
            
            // Check if current target is still valid
            if (targetingData.HasValidTarget())
            {
                if (ValidateCurrentTarget(ref targetingData, attackerPos))
                {
                    // Current target is still valid, update its position
                    if (transformLookup.HasComponent(targetingData.currentTarget))
                    {
                        var targetTransform = transformLookup[targetingData.currentTarget];
                        targetingData.lastTargetPosition = targetTransform.position;
                        
                        // Update prediction if enabled
                        if (targetingData.usePrediction && followerLookup.HasComponent(targetingData.currentTarget))
                        {
                            var follower = followerLookup[targetingData.currentTarget];
                            dfloat3 velocity = new dfloat3(dfloat.Zero); // Would need velocity component
                            targetingData.UpdatePrediction(targetTransform.position, velocity);
                        }
                        
                        attackData.combatState |= CombatState.HasTarget;
                        return; // Keep current target
                    }
                }
                
                // Current target is no longer valid
                targetingData.ClearTarget(currentTime);
                attackData.combatState &= ~CombatState.HasTarget;
            }
            
            // Check if we can acquire a new target
            if (!targetingData.CanAcquireNewTarget(currentTime))
            {
                return;
            }
            
            // Find new target based on strategy
            Entity newTarget = FindBestTarget(targetingData, attackerPos);
            
            if (newTarget != Entity.Null)
            {
                // Find the target's position
                dfloat3 targetPos = dfloat3.zero;
                for (int i = 0; i < targetEntities.Length; i++)
                {
                    if (targetEntities[i] == newTarget)
                    {
                        targetPos = targetTransforms[i].position;
                        break;
                    }
                }
                
                targetingData.SetTarget(newTarget, targetPos, currentTime);
                attackData.combatState |= CombatState.HasTarget;
            }
        }
        
        private bool ValidateCurrentTarget(ref TargetingData targetingData, dfloat3 attackerPos)
        {
            if (!transformLookup.HasComponent(targetingData.currentTarget))
                return false;
                
            var targetTransform = transformLookup[targetingData.currentTarget];
            
            // Check if target is still in range
            if (targetingData.ShouldLoseTarget(attackerPos, targetTransform.position))
                return false;
                
            // Check if target is still alive
            if (unitStatsLookup.HasComponent(targetingData.currentTarget))
            {
                var targetStats = unitStatsLookup[targetingData.currentTarget];
                if (targetStats.currentHealth <= dfloat.Zero)
                    return false;
            }
            
            return true;
        }
        
        private Entity FindBestTarget(TargetingData targetingData, dfloat3 attackerPos)
        {
            Entity bestTarget = Entity.Null;
            dfloat bestValue = dfloat.Zero;
            bool firstTarget = true;
            
            for (int i = 0; i < targetEntities.Length; i++)
            {
                Entity candidate = targetEntities[i];
                dfloat3 candidatePos = targetTransforms[i].position;
                UnitStats candidateStats = targetStats[i];
                
                // Check if candidate is in range
                if (!targetingData.IsTargetInRange(attackerPos, candidatePos))
                    continue;
                    
                // Check if candidate is alive
                if (candidateStats.currentHealth <= dfloat.Zero)
                    continue;
                    
                // Check layer mask and other filters
                if (!PassesFilters(targetingData, candidateStats))
                    continue;
                    
                // Calculate target value based on strategy
                dfloat targetValue = CalculateTargetValue(targetingData.strategy, attackerPos, candidatePos, candidateStats);
                
                // Check if this is the best target so far
                if (firstTarget || IsBetterTarget(targetingData.strategy, targetValue, bestValue))
                {
                    bestTarget = candidate;
                    bestValue = targetValue;
                    firstTarget = false;
                }
            }
            
            return bestTarget;
        }
        
        private bool PassesFilters(TargetingData targetingData, UnitStats candidateStats)
        {
            // Check health thresholds
            if (targetingData.minTargetHealth > dfloat.Zero && candidateStats.currentHealth < targetingData.minTargetHealth)
                return false;
                
            if (targetingData.maxTargetHealth > dfloat.Zero && candidateStats.currentHealth > targetingData.maxTargetHealth)
                return false;
                
            // Additional filters would go here (layer masks, tags, etc.)
            
            return true;
        }
        
        private dfloat CalculateTargetValue(TargetingStrategy strategy, dfloat3 attackerPos, dfloat3 targetPos, UnitStats targetStats)
        {
            switch (strategy)
            {
                case TargetingStrategy.Nearest:
                    return -dmath.distance(attackerPos, targetPos); // Negative for closer = better
                    
                case TargetingStrategy.Farthest:
                    return dmath.distance(attackerPos, targetPos);
                    
                case TargetingStrategy.HighestHealth:
                    return targetStats.currentHealth;
                    
                case TargetingStrategy.LowestHealth:
                    return -targetStats.currentHealth; // Negative for lower = better
                    
                case TargetingStrategy.Strongest:
                    return targetStats.maxHealth + targetStats.attackDamage; // Simple strength calculation
                    
                case TargetingStrategy.Weakest:
                    return -(targetStats.maxHealth + targetStats.attackDamage); // Negative for weaker = better
                    
                case TargetingStrategy.First:
                    // Would need path progress information
                    return dfloat.Zero;
                    
                case TargetingStrategy.Last:
                    // Would need path progress information
                    return dfloat.Zero;
                    
                case TargetingStrategy.Random:
                    // Use position as pseudo-random seed
                    var hash = (uint)(targetPos.x * 1000 + targetPos.z * 1000);
                    var random = new Unity.Mathematics.Random(hash);
                    return (dfloat)random.NextFloat();
                    
                default:
                    return dfloat.Zero;
            }
        }
        
        private bool IsBetterTarget(TargetingStrategy strategy, dfloat newValue, dfloat currentBestValue)
        {
            // For most strategies, higher value is better
            // For strategies with negative values (nearest, lowest health, etc.), this still works correctly
            return newValue > currentBestValue;
        }
    }
}
