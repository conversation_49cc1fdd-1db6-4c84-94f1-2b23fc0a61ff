using Avalon.Simulation.Combat.Configuration;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Combat.Damage;
using Avalon.Simulation.Combat.Effects;
using Avalon.Simulation.Effects;
using FlowField;
using Unity.Entities;
using Unity.Deterministic.Mathematics;

namespace Avalon.Simulation.Combat.Integration
{
    /// <summary>
    /// Utility class for integrating the combat system with existing FlowField systems
    /// Provides methods for setting up combat-enabled units and managing system interactions
    /// </summary>
    public static class CombatSystemIntegration
    {
        /// <summary>
        /// Setup a complete combat unit with all necessary components
        /// Integrates with UnitStats, Effects, and Movement systems
        /// </summary>
        public static void SetupCombatUnit(EntityManager entityManager, Entity unit, CombatUnitDefinition unitDefinition)
        {
            // Create and set UnitStats
            var unitStats = unitDefinition.CreateUnitStats();
            entityManager.SetComponentData(unit, unitStats);
            
            // Add combat components
            var attackData = unitDefinition.CreateAttackData();
            var targetingData = unitDefinition.CreateTargetingData();
            
            entityManager.AddComponentData(unit, attackData);
            entityManager.AddComponentData(unit, targetingData);
            
            // Add effect components using existing system
            EffectComponentUtils.AddEffectComponents(entityManager, unit, unitStats);
            
            // Add combat-specific effect buffer if not already present
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(unit))
            {
                entityManager.AddBuffer<GameEffect>(unit);
            }
            
            // Add shield component if unit has shield abilities
            if (HasShieldAbilities(unitDefinition))
            {
                entityManager.AddComponentData(unit, new ShieldData
                {
                    currentShield = dfloat.Zero,
                    maxShield = dfloat.Zero,
                    regenRate = dfloat.Zero,
                    regenDelay = (dfloat)3.0f
                });
            }
            
            // Setup special abilities
            SetupSpecialAbilities(entityManager, unit, unitDefinition);
            
            // Apply initial resistances and immunities
            ApplyResistances(entityManager, unit, unitDefinition);
        }
        
        /// <summary>
        /// Update an existing unit to add combat capabilities
        /// </summary>
        public static void AddCombatToExistingUnit(EntityManager entityManager, Entity unit, CombatUnitDefinition unitDefinition)
        {
            // Get existing UnitStats and update with combat values
            if (entityManager.HasComponent<UnitStats>(unit))
            {
                var existingStats = entityManager.GetComponentData<UnitStats>(unit);
                var combatStats = unitDefinition.CreateUnitStats();
                
                // Merge stats, keeping existing movement values but updating combat values
                existingStats.attackDamage = combatStats.attackDamage;
                existingStats.attackRange = combatStats.attackRange;
                existingStats.attackCooldown = combatStats.attackCooldown;
                existingStats.maxHealth = combatStats.maxHealth;
                existingStats.currentHealth = combatStats.currentHealth;
                
                entityManager.SetComponentData(unit, existingStats);
            }
            
            // Add combat components
            CombatComponentUtils.AddCombatComponents(entityManager, unit, unitDefinition.CreateUnitStats());
        }
        
        /// <summary>
        /// Synchronize combat stats with UnitStats changes
        /// Call this when UnitStats are modified to keep combat system in sync
        /// </summary>
        public static void SynchronizeCombatStats(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasComponent<UnitStats>(unit) || !entityManager.HasComponent<AttackData>(unit))
                return;
                
            var unitStats = entityManager.GetComponentData<UnitStats>(unit);
            var attackData = entityManager.GetComponentData<AttackData>(unit);
            var targetingData = entityManager.GetComponentData<TargetingData>(unit);
            
            // Update attack data from unit stats
            attackData.baseDamage = unitStats.attackDamage;
            attackData.attackRange = unitStats.attackRange;
            attackData.attackCooldown = unitStats.attackCooldown;
            
            // Update targeting data
            targetingData.targetingRange = unitStats.attackRange;
            targetingData.targetLossRange = unitStats.attackRange * (dfloat)1.2f;
            
            entityManager.SetComponentData(unit, attackData);
            entityManager.SetComponentData(unit, targetingData);
        }
        
        /// <summary>
        /// Apply combat effects modifiers to UnitStats
        /// Integrates with the existing CombatEffects system
        /// </summary>
        public static void ApplyCombatEffectsToStats(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasComponent<UnitStats>(unit) || !entityManager.HasComponent<CombatEffects>(unit))
                return;
                
            var unitStats = entityManager.GetComponentData<UnitStats>(unit);
            var combatEffects = entityManager.GetComponentData<CombatEffects>(unit);
            
            // Apply combat effects to attack stats
            unitStats.attackDamage = combatEffects.GetEffectiveAttackDamage();
            unitStats.attackRange = combatEffects.GetEffectiveAttackRange();
            unitStats.attackCooldown = combatEffects.GetEffectiveAttackCooldown();
            
            entityManager.SetComponentData(unit, unitStats);
        }
        
        /// <summary>
        /// Handle unit death and cleanup
        /// Integrates with existing systems for proper cleanup
        /// </summary>
        public static void HandleUnitDeath(EntityManager entityManager, Entity unit, Entity killer, dfloat currentTime)
        {
            // Clear targeting references to this unit
            ClearTargetingReferences(entityManager, unit);
            
            // Remove from any ongoing combat interactions
            CleanupCombatInteractions(entityManager, unit);
            
            // Add death component for delayed cleanup
            entityManager.AddComponentData(unit, new DeathData
            {
                deathTime = currentTime,
                killer = killer
            });
            
            // Disable the unit but don't destroy immediately (for death animations, etc.)
            entityManager.AddComponent<Disabled>(unit);
        }
        
        /// <summary>
        /// Update combat system integration with movement changes
        /// </summary>
        public static void OnMovementChanged(EntityManager entityManager, Entity unit, dfloat3 newPosition, dfloat3 newVelocity)
        {
            // Update targeting predictions
            if (entityManager.HasComponent<TargetingData>(unit))
            {
                var targetingData = entityManager.GetComponentData<TargetingData>(unit);
                if (targetingData.usePrediction)
                {
                    targetingData.UpdatePrediction(newPosition, newVelocity);
                    entityManager.SetComponentData(unit, targetingData);
                }
            }
            
            // Update any area-based abilities or auras
            UpdateAreaEffects(entityManager, unit, newPosition);
        }
        
        /// <summary>
        /// Check if a unit definition has shield-related abilities
        /// </summary>
        private static bool HasShieldAbilities(CombatUnitDefinition unitDefinition)
        {
            if (unitDefinition.SpecialAbilities == null)
                return false;
                
            foreach (var ability in unitDefinition.SpecialAbilities)
            {
                if (ability != null && ability.ShieldAmount > dfloat.Zero)
                    return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Setup special abilities for a unit
        /// </summary>
        private static void SetupSpecialAbilities(EntityManager entityManager, Entity unit, CombatUnitDefinition unitDefinition)
        {
            if (unitDefinition.SpecialAbilities == null || unitDefinition.SpecialAbilities.Length == 0)
                return;
                
            // Add ability components (would need to create ability system components)
            // For now, just add a marker component
            entityManager.AddComponentData(unit, new HasSpecialAbilities
            {
                abilityCount = unitDefinition.SpecialAbilities.Length
            });
        }
        
        /// <summary>
        /// Apply resistances and immunities from unit definition
        /// </summary>
        private static void ApplyResistances(EntityManager entityManager, Entity unit, CombatUnitDefinition unitDefinition)
        {
            if (unitDefinition.ResistantTo.Length > 0 || unitDefinition.ImmuneTo.Length > 0 || unitDefinition.WeakTo.Length > 0)
            {
                entityManager.AddComponentData(unit, new DamageResistances
                {
                    resistanceAmount = unitDefinition.ResistanceAmount,
                    weaknessAmount = unitDefinition.WeaknessAmount,
                    statusEffectResistance = unitDefinition.StatusEffectResistance,
                    immuneToStun = unitDefinition.ImmuneToStun,
                    immuneToSlow = unitDefinition.ImmuneToSlow,
                    immuneToDOT = unitDefinition.ImmuneToDOT
                });
            }
        }
        
        /// <summary>
        /// Clear all targeting references to a specific unit
        /// </summary>
        private static void ClearTargetingReferences(EntityManager entityManager, Entity deadUnit)
        {
            // Find all units targeting the dead unit and clear their targets
            var query = entityManager.CreateEntityQuery(ComponentType.ReadWrite<TargetingData>());
            var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
            var targetingDataArray = query.ToComponentDataArray<TargetingData>(Unity.Collections.Allocator.Temp);
            
            for (int i = 0; i < entities.Length; i++)
            {
                if (targetingDataArray[i].currentTarget == deadUnit)
                {
                    var targetingData = targetingDataArray[i];
                    targetingData.ClearTarget((dfloat)UnityEngine.Time.time);
                    entityManager.SetComponentData(entities[i], targetingData);
                }
            }
            
            entities.Dispose();
            targetingDataArray.Dispose();
        }
        
        /// <summary>
        /// Clean up any ongoing combat interactions for a unit
        /// </summary>
        private static void CleanupCombatInteractions(EntityManager entityManager, Entity unit)
        {
            // Remove any projectiles targeting this unit
            var projectileQuery = entityManager.CreateEntityQuery(ComponentType.ReadWrite<ProjectileData>());
            var projectiles = projectileQuery.ToEntityArray(Unity.Collections.Allocator.Temp);
            var projectileDataArray = projectileQuery.ToComponentDataArray<ProjectileData>(Unity.Collections.Allocator.Temp);
            
            for (int i = 0; i < projectiles.Length; i++)
            {
                if (projectileDataArray[i].targetEntity == unit)
                {
                    // Clear the target so projectile continues in straight line
                    var projectileData = projectileDataArray[i];
                    projectileData.targetEntity = Entity.Null;
                    entityManager.SetComponentData(projectiles[i], projectileData);
                }
            }
            
            projectiles.Dispose();
            projectileDataArray.Dispose();
        }
        
        /// <summary>
        /// Update area-based effects when a unit moves
        /// </summary>
        private static void UpdateAreaEffects(EntityManager entityManager, Entity unit, dfloat3 newPosition)
        {
            // Update any aura effects or area-based abilities
            // This would be expanded based on specific area effect implementations
        }
        
        /// <summary>
        /// Get effective combat stats including all modifiers
        /// </summary>
        public static CombatStats GetEffectiveCombatStats(EntityManager entityManager, Entity unit)
        {
            var stats = new CombatStats();
            
            if (entityManager.HasComponent<UnitStats>(unit))
            {
                var unitStats = entityManager.GetComponentData<UnitStats>(unit);
                stats.health = unitStats.currentHealth;
                stats.maxHealth = unitStats.maxHealth;
                stats.baseDamage = unitStats.attackDamage;
                stats.baseRange = unitStats.attackRange;
                stats.baseCooldown = unitStats.attackCooldown;
            }
            
            if (entityManager.HasComponent<CombatEffects>(unit))
            {
                var combatEffects = entityManager.GetComponentData<CombatEffects>(unit);
                stats.effectiveDamage = combatEffects.GetEffectiveAttackDamage();
                stats.effectiveRange = combatEffects.GetEffectiveAttackRange();
                stats.effectiveCooldown = combatEffects.GetEffectiveAttackCooldown();
                stats.criticalChance = combatEffects.GetEffectiveCriticalChance();
                stats.criticalDamage = combatEffects.GetEffectiveCriticalDamage();
            }
            
            return stats;
        }
    }
    
    /// <summary>
    /// Component to mark units with special abilities
    /// </summary>
    public struct HasSpecialAbilities : IComponentData
    {
        public int abilityCount;
    }
    
    /// <summary>
    /// Component for damage resistances and immunities
    /// </summary>
    public struct DamageResistances : IComponentData
    {
        public dfloat resistanceAmount;
        public dfloat weaknessAmount;
        public dfloat statusEffectResistance;
        public bool immuneToStun;
        public bool immuneToSlow;
        public bool immuneToDOT;
    }
    
    /// <summary>
    /// Struct containing effective combat statistics
    /// </summary>
    public struct CombatStats
    {
        public dfloat health;
        public dfloat maxHealth;
        public dfloat baseDamage;
        public dfloat effectiveDamage;
        public dfloat baseRange;
        public dfloat effectiveRange;
        public dfloat baseCooldown;
        public dfloat effectiveCooldown;
        public dfloat criticalChance;
        public dfloat criticalDamage;
    }
}
