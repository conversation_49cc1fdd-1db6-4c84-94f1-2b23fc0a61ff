using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Integration
{
    /// <summary>
    /// Component to mark entities as disabled/inactive
    /// Used to exclude entities from processing in various systems
    /// </summary>
    public struct Disabled : IComponentData
    {
    }
    /// <summary>
    /// System that handles integration between combat system and other FlowField systems
    /// Ensures combat stats stay synchronized with UnitStats and effect modifications
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(GameEffectSystem))]
    [UpdateBefore(typeof(FlowFieldCombatSystemGroup))]
    public partial class CombatIntegrationSystem : SystemBase
    {
        private EntityQuery combatUnitsQuery;
        private EntityQuery newCombatUnitsQuery;
        private EntityQuery fixedTimestepQuery;
        
        protected override void OnCreate()
        {
            // Query for units with combat components
            combatUnitsQuery = GetEntityQuery(
                ComponentType.ReadWrite<AttackData>(),
                ComponentType.ReadWrite<TargetingData>(),
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.Exclude<Disabled>()
            );

            // Query for units that need combat integration setup
            newCombatUnitsQuery = GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadOnly<NeedsCombatSetup>(),
                ComponentType.Exclude<AttackData>(),
                ComponentType.Exclude<Disabled>()
            );

            // Query for fixed timestep
            fixedTimestepQuery = GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());

            // Only require fixed timestep if it exists, otherwise system won't run
            RequireForUpdate(fixedTimestepQuery);

            // Start disabled to prevent crashes until properly configured
            Enabled = false;
        }
        
        protected override void OnUpdate()
        {
            // Safety check: Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty)
            {
                UnityEngine.Debug.LogWarning("CombatIntegrationSystem: FixedTimestep singleton not found");
                return;
            }

            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Setup combat for new units
            SetupNewCombatUnits();

            // Synchronize combat stats with UnitStats changes
            SynchronizeCombatStats(fixedTimestep.currentTime);

            // Apply combat effects to stats
            ApplyCombatEffectsModifiers();

            // Update targeting based on movement changes
            UpdateTargetingFromMovement();
        }
        
        private void SetupNewCombatUnits()
        {
            // Skip if no units need setup
            if (newCombatUnitsQuery.IsEmpty) return;

            // Use ECB.ParallelWriter to avoid structural changes that can cause crashes
            var ecb = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>().CreateCommandBuffer();
            var parallelWriter = ecb.AsParallelWriter();

            var jobHandle = Entities
                .WithAll<NeedsCombatSetup>()
                .WithNone<AttackData>()
                .ForEach((Entity entity, int entityInQueryIndex, in UnitStats unitStats) =>
                {
                    // Create default combat components
                    var attackData = CombatComponentUtils.CreateAttackDataFromStats(unitStats);
                    var targetingData = CombatComponentUtils.CreateTargetingDataFromStats(unitStats);

                    parallelWriter.AddComponent(entityInQueryIndex, entity, attackData);
                    parallelWriter.AddComponent(entityInQueryIndex, entity, targetingData);

                    // Remove setup marker
                    parallelWriter.RemoveComponent<NeedsCombatSetup>(entityInQueryIndex, entity);
                }).ScheduleParallel(Dependency);

            // Complete the job handle to ensure ECB operations are properly synchronized
            jobHandle.Complete();

            // Update system dependency to ensure proper job chaining
            Dependency = jobHandle;
        }
        
        private void SynchronizeCombatStats(dfloat currentTime)
        {
            // Skip if no combat units exist
            if (combatUnitsQuery.IsEmpty) return;

            var jobHandle = Entities
                .WithAll<AttackData, TargetingData, UnitStats>()
                .ForEach((Entity entity, ref AttackData attackData, ref TargetingData targetingData,
                    in UnitStats unitStats) =>
                {
                    // Check if UnitStats have changed and update combat components accordingly
                    bool statsChanged = false;

                    // Check for changes in attack stats
                    if (dmath.abs(attackData.baseDamage - unitStats.attackDamage) > (dfloat)0.001f)
                    {
                        attackData.baseDamage = unitStats.attackDamage;
                        statsChanged = true;
                    }

                    if (dmath.abs(attackData.attackRange - unitStats.attackRange) > (dfloat)0.001f)
                    {
                        attackData.attackRange = unitStats.attackRange;
                        targetingData.targetingRange = unitStats.attackRange;
                        targetingData.targetLossRange = unitStats.attackRange * (dfloat)1.2f;
                        statsChanged = true;
                    }

                    if (dmath.abs(attackData.attackCooldown - unitStats.attackCooldown) > (dfloat)0.001f)
                    {
                        attackData.attackCooldown = unitStats.attackCooldown;
                        statsChanged = true;
                    }

                    // If unit died, handle death
                    if (unitStats.currentHealth <= dfloat.Zero && (attackData.combatState & CombatState.HasTarget) != 0)
                    {
                        // Clear target and mark as dead
                        targetingData.ClearTarget(currentTime);
                        attackData.combatState &= ~CombatState.HasTarget;
                        attackData.combatState |= CombatState.Stunned; // Prevent further attacks
                    }
                }).ScheduleParallel(Dependency);

            // Update system dependency to ensure proper job chaining
            Dependency = jobHandle;
        }
        
        private void ApplyCombatEffectsModifiers()
        {
            var jobHandle = Entities
                .WithAll<AttackData, CombatEffects>()
                .ForEach((Entity entity, ref AttackData attackData, in CombatEffects combatEffects) =>
                {
                    // Apply combat effects modifiers to attack data
                    // This ensures the combat system uses the modified values from the effects system

                    // Note: We don't modify baseDamage directly, but the combat system should use
                    // combatEffects.GetEffectiveAttackDamage() when calculating actual damage

                    // Update combat state based on effects
                    if (!combatEffects.CanAttack())
                    {
                        attackData.combatState |= CombatState.Disarmed;
                    }
                    else
                    {
                        attackData.combatState &= ~CombatState.Disarmed;
                    }
                }).ScheduleParallel(Dependency);

            // Update system dependency to ensure proper job chaining
            Dependency = jobHandle;
        }
        
        private void UpdateTargetingFromMovement()
        {
            var jobHandle = Entities
                .WithAll<TargetingData, SimulationTransform, FlowFieldFollower>()
                .ForEach((Entity entity, ref TargetingData targetingData, in SimulationTransform transform,
                    in FlowFieldFollower follower) =>
                {
                    // Update targeting predictions based on movement
                    if (targetingData.usePrediction && targetingData.HasValidTarget())
                    {
                        // Calculate velocity from FlowFieldFollower (simplified)
                        dfloat3 velocity = new dfloat3(dfloat.Zero); // Would need actual velocity calculation
                        targetingData.UpdatePrediction(transform.position, velocity);
                    }
                }).ScheduleParallel(Dependency);

            // Update system dependency to ensure proper job chaining
            Dependency = jobHandle;
        }
    }
    
    /// <summary>
    /// Component to mark units that need combat setup
    /// </summary>
    public struct NeedsCombatSetup : IComponentData
    {
        public bool useDefaultAttack;
        public AttackPattern preferredPattern;
        public TargetingStrategy preferredTargeting;
    }
    
    /// <summary>
    /// System that handles combat unit initialization
    /// Runs early in the frame to set up combat components for new units
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(UnitStatsSystem))]
    [UpdateBefore(typeof(CombatIntegrationSystem))]
    public partial class CombatInitializationSystem : SystemBase
    {
        private EntityQuery newUnitsQuery;
        
        protected override void OnCreate()
        {
            // Query for units that have UnitStats but no combat components yet
            newUnitsQuery = GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadOnly<UnitStatsInitialized>(),
                ComponentType.Exclude<AttackData>(),
                ComponentType.Exclude<NeedsCombatSetup>(),
                ComponentType.Exclude<Disabled>()
            );

            // Only run when there are units that need combat setup
            RequireForUpdate(newUnitsQuery);
        }
        
        protected override void OnUpdate()
        {
            // Use ECB.ParallelWriter to avoid structural changes that can cause crashes
            var ecb = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>().CreateCommandBuffer();
            var parallelWriter = ecb.AsParallelWriter();

            // Mark new units for combat setup
            var jobHandle = Entities
                .WithAll<UnitStats, UnitStatsInitialized>()
                .WithNone<AttackData, NeedsCombatSetup>()
                .ForEach((Entity entity, int entityInQueryIndex, in UnitStats unitStats) =>
                {
                    // Only add combat to units that have attack capabilities
                    if (unitStats.attackDamage > dfloat.Zero || unitStats.attackRange > dfloat.Zero)
                    {
                        parallelWriter.AddComponent(entityInQueryIndex, entity, new NeedsCombatSetup
                        {
                            useDefaultAttack = true,
                            preferredPattern = AttackPattern.SingleTarget,
                            preferredTargeting = TargetingStrategy.Nearest
                        });
                    }

                }).ScheduleParallel(Dependency);

            // Complete the job handle to ensure ECB operations are properly synchronized
            jobHandle.Complete();

            // Update system dependency to ensure proper job chaining
            Dependency = jobHandle;
        }
    }
    
    /// <summary>
    /// Utility system for handling combat system events and integration
    /// </summary>
    public static class CombatIntegrationUtils
    {
        /// <summary>
        /// Safely disable the CombatIntegrationSystem
        /// </summary>
        public static void DisableCombatIntegration(World world)
        {
            var system = world.GetExistingSystemManaged<CombatIntegrationSystem>();
            if (system != null)
            {
                system.Enabled = false;
                UnityEngine.Debug.Log("CombatIntegrationSystem disabled");
            }
        }
        /// <summary>
        /// Add combat capabilities to an existing unit
        /// </summary>
        public static void AddCombatToUnit(EntityManager entityManager, Entity unit, 
            AttackPattern attackPattern = AttackPattern.SingleTarget, 
            TargetingStrategy targetingStrategy = TargetingStrategy.Nearest)
        {
            if (entityManager.HasComponent<AttackData>(unit))
                return; // Already has combat
                
            entityManager.AddComponentData(unit, new NeedsCombatSetup
            {
                useDefaultAttack = true,
                preferredPattern = attackPattern,
                preferredTargeting = targetingStrategy
            });
        }
        
        /// <summary>
        /// Remove combat capabilities from a unit
        /// </summary>
        public static void RemoveCombatFromUnit(EntityManager entityManager, Entity unit)
        {
            if (entityManager.HasComponent<AttackData>(unit))
                entityManager.RemoveComponent<AttackData>(unit);
                
            if (entityManager.HasComponent<TargetingData>(unit))
                entityManager.RemoveComponent<TargetingData>(unit);
                
            if (entityManager.HasComponent<NeedsCombatSetup>(unit))
                entityManager.RemoveComponent<NeedsCombatSetup>(unit);
        }
        
        /// <summary>
        /// Check if a unit has combat capabilities
        /// </summary>
        public static bool HasCombatCapabilities(EntityManager entityManager, Entity unit)
        {
            return entityManager.HasComponent<AttackData>(unit) && 
                   entityManager.HasComponent<TargetingData>(unit);
        }
        
        /// <summary>
        /// Get combat readiness status of a unit
        /// </summary>
        public static CombatReadiness GetCombatReadiness(EntityManager entityManager, Entity unit)
        {
            if (!HasCombatCapabilities(entityManager, unit))
                return CombatReadiness.NoCombat;
                
            var attackData = entityManager.GetComponentData<AttackData>(unit);
            var targetingData = entityManager.GetComponentData<TargetingData>(unit);
            
            if ((attackData.combatState & CombatState.Stunned) != 0)
                return CombatReadiness.Stunned;
                
            if ((attackData.combatState & CombatState.Disarmed) != 0)
                return CombatReadiness.Disarmed;
                
            if ((attackData.combatState & CombatState.OnCooldown) != 0)
                return CombatReadiness.OnCooldown;
                
            if (!targetingData.HasValidTarget())
                return CombatReadiness.NoTarget;
                
            return CombatReadiness.Ready;
        }
    }
    
    /// <summary>
    /// Combat readiness states
    /// </summary>
    public enum CombatReadiness : byte
    {
        Ready = 0,
        NoCombat = 1,
        NoTarget = 2,
        OnCooldown = 3,
        Stunned = 4,
        Disarmed = 5,
        Channeling = 6
    }
}
