// using Unity.Entities;
// using UnityEngine;
// using FlowField.Combat;
//
// namespace FlowField.Combat
// {
//     /// <summary>
//     /// Test script to safely enable and test the CombatIntegrationSystem
//     /// </summary>
//     public class CombatIntegrationSystemTest : MonoBehaviour
//     {
//         [Header("Combat Integration Test")]
//         [SerializeField] private bool enableOnStart = false;
//         [SerializeField] private bool showDebugInfo = true;
//         
//         private World world;
//         private CombatIntegrationSystem combatSystem;
//         
//         void Start()
//         {
//             world = World.DefaultGameObjectInjectionWorld;
//             if (world != null)
//             {
//                 combatSystem = world.GetExistingSystemManaged<CombatIntegrationSystem>();
//                 
//                 if (enableOnStart)
//                 {
//                     EnableCombatIntegration();
//                 }
//                 
//                 if (showDebugInfo)
//                 {
//                     LogSystemStatus();
//                 }
//             }
//             else
//             {
//                 Debug.LogError("Default world not found");
//             }
//         }
//         
//         [ContextMenu("Enable Combat Integration")]
//         public void EnableCombatIntegration()
//         {
//             if (world != null)
//             {
//                 CombatIntegrationUtils.EnableCombatIntegration(world);
//                 LogSystemStatus();
//             }
//         }
//         
//         [ContextMenu("Disable Combat Integration")]
//         public void DisableCombatIntegration()
//         {
//             if (world != null)
//             {
//                 CombatIntegrationUtils.DisableCombatIntegration(world);
//                 LogSystemStatus();
//             }
//         }
//         
//         [ContextMenu("Log System Status")]
//         public void LogSystemStatus()
//         {
//             if (combatSystem != null)
//             {
//                 Debug.Log($"CombatIntegrationSystem Status:");
//                 Debug.Log($"  - Enabled: {combatSystem.Enabled}");
//                 Debug.Log($"  - World: {world.Name}");
//                 
//                 // Check dependencies
//                 var fixedTimestepQuery = combatSystem.GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());
//                 Debug.Log($"  - FixedTimestep available: {!fixedTimestepQuery.IsEmpty}");
//                 
//                 // Check for entities that need combat setup
//                 var needsSetupQuery = combatSystem.GetEntityQuery(
//                     ComponentType.ReadOnly<UnitStats>(),
//                     ComponentType.ReadOnly<NeedsCombatSetup>(),
//                     ComponentType.Exclude<AttackData>(),
//                     ComponentType.Exclude<Disabled>()
//                 );
//                 Debug.Log($"  - Units needing combat setup: {needsSetupQuery.CalculateEntityCount()}");
//                 
//                 // Check for existing combat units
//                 var combatUnitsQuery = combatSystem.GetEntityQuery(
//                     ComponentType.ReadOnly<AttackData>(),
//                     ComponentType.ReadOnly<TargetingData>(),
//                     ComponentType.ReadOnly<UnitStats>(),
//                     ComponentType.Exclude<Disabled>()
//                 );
//                 Debug.Log($"  - Existing combat units: {combatUnitsQuery.CalculateEntityCount()}");
//             }
//             else
//             {
//                 Debug.LogWarning("CombatIntegrationSystem not found in world");
//             }
//         }
//         
//         void OnGUI()
//         {
//             if (!showDebugInfo) return;
//             
//             GUILayout.BeginArea(new Rect(10, 10, 300, 200));
//             GUILayout.Label("Combat Integration System Test", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
//             
//             if (combatSystem != null)
//             {
//                 GUILayout.Label($"System Enabled: {combatSystem.Enabled}");
//                 
//                 if (GUILayout.Button("Enable Combat Integration"))
//                 {
//                     EnableCombatIntegration();
//                 }
//                 
//                 if (GUILayout.Button("Disable Combat Integration"))
//                 {
//                     DisableCombatIntegration();
//                 }
//                 
//                 if (GUILayout.Button("Log Status"))
//                 {
//                     LogSystemStatus();
//                 }
//             }
//             else
//             {
//                 GUILayout.Label("System not found");
//             }
//             
//             GUILayout.EndArea();
//         }
//     }
// }
