using Avalon.Simulation.Combat.Attacks;
using Avalon.Simulation.Combat.Damage;
using Avalon.Simulation.Combat.Effects;
using Avalon.Simulation.Combat.Projectiles;
using Avalon.Simulation.Combat.Targeting;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Entities;

namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// System group that manages all combat-related systems in FlowField
    /// Handles attack processing, target selection, projectiles, and damage application
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(GameEffectSystem))]
    [UpdateBefore(typeof(MovementSystemGroup))]
    [WorldSystemFilter(WorldSystemFilterFlags.Default | WorldSystemFilterFlags.EntitySceneOptimizations)]
    public partial class FlowFieldCombatSystemGroup : ComponentSystemGroup
    {
        protected override void OnCreate()
        {
            base.OnCreate();

            // Add combat systems in proper execution order
            // 1. Target selection and acquisition
            var targetSelectionSystem = World.GetOrCreateSystem<TargetSelectionSystem>();
            
            // 2. Attack processing and cooldown management
            var attackProcessingSystem = World.GetOrCreateSystem<AttackProcessingSystem>();
            
            // 3. Attack execution (creates projectiles, applies damage)
            var attackExecutionSystem = World.GetOrCreateSystem<AttackExecutionSystem>();
            
            // 4. Projectile spawning and movement
            var projectileSystem = World.GetOrCreateSystem<ProjectileSystem>();
            
            // 6. Projectile cleanup and destruction
            var projectileCleanupSystem = World.GetOrCreateSystem<ProjectileCleanupSystem>();
            
            // 7. Damage application and effects
            var damageApplicationSystem = World.GetOrCreateSystem<DamageApplicationSystem>();
            
            // 8. Combat effects processing (DOT, stuns, etc.)
            var combatEffectsSystem = World.GetOrCreateSystem<CombatEffectsProcessingSystem>();

            // Add systems to update list in order
            AddSystemToUpdateList(targetSelectionSystem);
            AddSystemToUpdateList(attackProcessingSystem);
            AddSystemToUpdateList(attackExecutionSystem);
            AddSystemToUpdateList(projectileSystem);
            AddSystemToUpdateList(projectileCleanupSystem);
            AddSystemToUpdateList(damageApplicationSystem);
            AddSystemToUpdateList(combatEffectsSystem);
        }
    }
}
