# FlowField Combat System - Complete Guide

## Table of Contents
1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Core Concepts](#core-concepts)
4. [System Architecture](#system-architecture)
5. [Components Reference](#components-reference)
6. [Configuration](#configuration)
7. [Integration](#integration)
8. [Performance](#performance)
9. [Debugging](#debugging)
10. [Examples](#examples)

## Overview

The FlowField Combat System is a comprehensive, deterministic combat framework designed for tower defense games. It provides:

- **Multiple Attack Patterns**: Single target, linear, AOE, homing, cone, chain, and more
- **Flexible Targeting**: Various targeting strategies with range and line-of-sight support
- **Projectile System**: Support for instant, linear, homing, and arcing projectiles
- **Status Effects**: DOT, stuns, slows, shields, and custom effects
- **Deterministic Behavior**: All calculations use deterministic math for consistent results
- **ECS Optimization**: Built on Unity DOTS for high performance
- **Designer-Friendly**: ScriptableObject-based configuration system

## Quick Start

### 1. Basic Setup

```csharp
// Add combat capabilities to an existing unit
var unitDefinition = ScriptableObject.CreateInstance<CombatUnitDefinition>();
unitDefinition.PrimaryAttack = myAttackPattern;

// Setup the unit
CombatSystemIntegration.SetupCombatUnit(entityManager, unitEntity, unitDefinition);
```

### 2. Create an Attack Pattern

```csharp
// Create attack pattern asset
var attackPattern = ScriptableObject.CreateInstance<AttackPatternDefinition>();
attackPattern.AttackPattern = AttackPattern.SingleTarget;
attackPattern.BaseDamage = 50f;
attackPattern.AttackRange = 5f;
attackPattern.AttackCooldown = 1f;
```

### 3. Apply Damage

```csharp
// Apply instant damage
CombatComponentUtils.ApplyDamage(entityManager, attacker, target, 
    damage, position, DamageType.Physical);

// Apply damage over time
CombatEffectUtils.ApplyDamageOverTime(entityManager, target, 
    damagePerTick, duration, tickRate, DamageType.Poison);
```

## Core Concepts

### Attack Patterns

The system supports various attack patterns:

- **SingleTarget**: Direct attack on one enemy
- **Linear**: Straight-line projectile with optional piercing
- **MultiLinearWave**: Multiple projectiles in a spread
- **Homing**: Projectiles that track targets
- **AOEPosition**: Area damage at target location
- **AOESelf**: Area damage around attacker
- **Cone**: Directional cone attack
- **Circle**: Circular attack around attacker
- **FullMap**: Global attack hitting all enemies
- **Chain**: Attack that jumps between targets
- **Beam**: Continuous damage along a line

### Targeting Strategies

- **Nearest/Farthest**: Distance-based targeting
- **HighestHealth/LowestHealth**: Health-based targeting
- **First/Last**: Path progress-based targeting
- **Strongest/Weakest**: Overall stats-based targeting
- **Random**: Random selection within range

### Damage Types

- **Physical**: Affected by armor
- **Magical**: Affected by magic resistance
- **True**: Ignores all defenses
- **Elemental**: Fire, ice, lightning with special effects
- **Poison**: Damage over time effects

## System Architecture

### System Update Order

```
FlowFieldSystemGroup
├── UnitStatsSystem
├── EffectInitializationSystem
├── GameEffectSystem
├── CombatInitializationSystem
├── CombatIntegrationSystem
└── FlowFieldCombatSystemGroup
    ├── TargetSelectionSystem
    ├── AttackProcessingSystem
    ├── AttackExecutionSystem
    ├── ProjectileSystem
    ├── ProjectileCleanupSystem
    ├── DamageApplicationSystem
    └── CombatEffectsProcessingSystem
```

### Directory Structure

```
FlowField/Combat/
├── Core/                    # Core components
├── Targeting/              # Target selection
├── Attacks/                # Attack patterns
├── Projectiles/            # Projectile systems
├── Damage/                 # Damage application
├── Effects/                # Combat effects
├── Configuration/          # ScriptableObjects
├── Integration/            # System integration
├── Debugging/              # Debug tools
├── Tests/                  # Unit tests
└── Documentation/          # This guide
```

## Components Reference

### AttackData
Core component containing attack parameters:
```csharp
public struct AttackData : IComponentData
{
    public dfloat baseDamage;
    public dfloat attackRange;
    public dfloat attackCooldown;
    public AttackPattern attackPattern;
    public ProjectileType projectileType;
    public DamageType damageType;
    // ... additional parameters
}
```

### TargetingData
Handles target selection and tracking:
```csharp
public struct TargetingData : IComponentData
{
    public Entity currentTarget;
    public TargetingStrategy strategy;
    public dfloat targetingRange;
    public bool requireLineOfSight;
    // ... additional parameters
}
```

### ProjectileData
Manages projectile behavior:
```csharp
public struct ProjectileData : IComponentData
{
    public Entity sourceEntity;
    public Entity targetEntity;
    public dfloat3 velocity;
    public ProjectileType projectileType;
    public dfloat damage;
    // ... additional parameters
}
```

### DamageData
Represents damage events:
```csharp
public struct DamageData : IComponentData
{
    public Entity sourceEntity;
    public Entity targetEntity;
    public dfloat baseDamage;
    public DamageType damageType;
    public bool isCritical;
    // ... additional parameters
}
```

## Configuration

### Attack Pattern Definition

Create attack patterns using ScriptableObjects:

```csharp
[CreateAssetMenu(fileName = "New Attack Pattern", 
    menuName = "FlowField/Combat/Attack Pattern Definition")]
public class AttackPatternDefinition : ScriptableObject
{
    [Header("Basic Properties")]
    public AttackPattern attackPattern;
    public float baseDamage;
    public float attackRange;
    public float attackCooldown;
    
    [Header("Area of Effect")]
    public float aoeRadius;
    public int maxAoeTargets;
    
    // ... additional configuration
}
```

### Combat Unit Definition

Define complete combat units:

```csharp
[CreateAssetMenu(fileName = "New Combat Unit", 
    menuName = "FlowField/Combat/Combat Unit Definition")]
public class CombatUnitDefinition : ScriptableObject
{
    public AttackPatternDefinition primaryAttack;
    public AttackPatternDefinition[] secondaryAttacks;
    public SpecialAbilityDefinition[] specialAbilities;
    
    // Unit stats
    public float maxHealth;
    public float armor;
    public float movementSpeed;
    
    // Resistances
    public DamageType[] resistantTo;
    public DamageType[] immuneTo;
    public DamageType[] weakTo;
}
```

## Integration

### With Existing Systems

The combat system integrates seamlessly with existing FlowField systems:

#### UnitStats Integration
```csharp
// Combat stats are synchronized with UnitStats
CombatSystemIntegration.SynchronizeCombatStats(entityManager, unit);
```

#### Effects System Integration
```csharp
// Combat effects use the existing GameEffect system
CombatEffectUtils.ApplyStun(entityManager, target, duration);
CombatEffectUtils.ApplySlow(entityManager, target, slowAmount, duration);
```

#### Movement System Integration
```csharp
// Combat system responds to movement changes
CombatSystemIntegration.OnMovementChanged(entityManager, unit, newPosition, newVelocity);
```

### Adding Combat to Existing Units

```csharp
// Method 1: Using unit definition
CombatSystemIntegration.SetupCombatUnit(entityManager, unit, unitDefinition);

// Method 2: Adding to existing unit
CombatSystemIntegration.AddCombatToExistingUnit(entityManager, unit, unitDefinition);

// Method 3: Manual setup
CombatComponentUtils.AddCombatComponents(entityManager, unit, 
    AttackPattern.SingleTarget, TargetingStrategy.Nearest, damage, range, cooldown);
```

## Performance

### Optimization Features

- **Burst Compilation**: All systems are burst-compiled for maximum performance
- **Efficient Queries**: Optimized entity queries with proper filtering
- **Spatial Partitioning**: Efficient target selection using spatial queries
- **Object Pooling**: Projectiles are pooled to reduce allocations
- **Batched Operations**: Damage application is batched for efficiency

### Performance Monitoring

```csharp
// Add performance profiler to scene
var profiler = gameObject.AddComponent<CombatPerformanceProfiler>();
profiler.enableProfiling = true;
profiler.showOnScreenStats = true;
```

### Performance Guidelines

- Keep combat unit counts reasonable (< 1000 active units)
- Limit simultaneous projectiles (< 500 active projectiles)
- Use appropriate targeting ranges to reduce query overhead
- Consider LOD systems for distant combat units

## Debugging

### Visual Debugging

```csharp
// Add combat debugger to scene
var debugger = gameObject.AddComponent<CombatDebugger>();
debugger.enableDebugging = true;
debugger.showAttackRanges = true;
debugger.showTargetingLines = true;
debugger.showProjectilePaths = true;
```

### Debug Features

- **Attack Range Visualization**: Shows attack and targeting ranges
- **Targeting Lines**: Displays current target relationships
- **Projectile Paths**: Shows projectile trajectories
- **Combat State Display**: Real-time combat state information
- **Performance Stats**: System performance monitoring
- **Test Controls**: Runtime testing and manipulation tools

### Common Issues

1. **Units not attacking**: Check targeting range, line of sight, and combat state
2. **Projectiles missing**: Verify collision radius and target prediction
3. **Performance issues**: Monitor entity counts and system load
4. **Determinism problems**: Ensure all calculations use dfloat types

## Examples

### Example 1: Basic Tower

```csharp
// Create a basic tower with single-target attacks
var towerDefinition = ScriptableObject.CreateInstance<CombatUnitDefinition>();

var basicAttack = ScriptableObject.CreateInstance<AttackPatternDefinition>();
basicAttack.attackPattern = AttackPattern.SingleTarget;
basicAttack.baseDamage = 25f;
basicAttack.attackRange = 5f;
basicAttack.attackCooldown = 1f;
basicAttack.projectileType = ProjectileType.Linear;
basicAttack.projectileSpeed = 10f;

towerDefinition.primaryAttack = basicAttack;
towerDefinition.maxHealth = 200f;
towerDefinition.preferredTargeting = TargetingStrategy.Nearest;

// Setup the tower
CombatSystemIntegration.SetupCombatUnit(entityManager, towerEntity, towerDefinition);
```

### Example 2: AOE Mage Tower

```csharp
// Create a mage tower with AOE attacks
var mageDefinition = ScriptableObject.CreateInstance<CombatUnitDefinition>();

var aoeAttack = ScriptableObject.CreateInstance<AttackPatternDefinition>();
aoeAttack.attackPattern = AttackPattern.AOEPosition;
aoeAttack.baseDamage = 40f;
aoeAttack.attackRange = 6f;
aoeAttack.attackCooldown = 2f;
aoeAttack.aoeRadius = 2f;
aoeAttack.maxAoeTargets = 8;
aoeAttack.damageType = DamageType.Magical;

mageDefinition.primaryAttack = aoeAttack;
mageDefinition.maxHealth = 150f;
mageDefinition.preferredTargeting = TargetingStrategy.HighestHealth;

CombatSystemIntegration.SetupCombatUnit(entityManager, mageEntity, mageDefinition);
```

### Example 3: Poison DOT Tower

```csharp
// Create a poison tower with damage over time
var poisonDefinition = ScriptableObject.CreateInstance<CombatUnitDefinition>();

var poisonAttack = ScriptableObject.CreateInstance<AttackPatternDefinition>();
poisonAttack.attackPattern = AttackPattern.SingleTarget;
poisonAttack.baseDamage = 15f;
poisonAttack.attackRange = 4f;
poisonAttack.attackCooldown = 0.8f;
poisonAttack.dotDamage = 5f;
poisonAttack.dotDuration = 8f;
poisonAttack.dotTickRate = 1f;
poisonAttack.dotType = DamageType.Poison;

poisonDefinition.primaryAttack = poisonAttack;
poisonDefinition.maxHealth = 120f;

CombatSystemIntegration.SetupCombatUnit(entityManager, poisonEntity, poisonDefinition);
```

### Example 4: Multi-Shot Tower

```csharp
// Create a multi-shot tower
var multiShotDefinition = ScriptableObject.CreateInstance<CombatUnitDefinition>();

var multiShotAttack = ScriptableObject.CreateInstance<AttackPatternDefinition>();
multiShotAttack.attackPattern = AttackPattern.MultiLinearWave;
multiShotAttack.baseDamage = 20f;
multiShotAttack.attackRange = 5f;
multiShotAttack.attackCooldown = 1.5f;
multiShotAttack.projectileCount = 3;
multiShotAttack.spreadAngle = 30f; // degrees
multiShotAttack.pierceCount = 2;

multiShotDefinition.primaryAttack = multiShotAttack;
multiShotDefinition.maxHealth = 180f;

CombatSystemIntegration.SetupCombatUnit(entityManager, multiShotEntity, multiShotDefinition);
```

---

For more detailed information, see the individual system documentation files and code comments throughout the FlowField Combat System.
