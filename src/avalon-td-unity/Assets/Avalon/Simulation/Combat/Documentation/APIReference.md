# FlowField Combat System - API Reference

## Core Components

### AttackData
Primary component for attack configuration and state.

```csharp
public struct AttackData : IComponentData
```

#### Key Properties
- `dfloat baseDamage` - Base damage before modifiers
- `dfloat attackRange` - Maximum attack range
- `dfloat attackCooldown` - Time between attacks
- `AttackPattern attackPattern` - Type of attack pattern
- `ProjectileType projectileType` - Type of projectile
- `DamageType damageType` - Type of damage dealt
- `CombatState combatState` - Current combat state flags

#### Key Methods
- `bool CanAttack(dfloat currentTime)` - Check if unit can attack
- `void StartAttack(dfloat currentTime)` - Begin attack sequence
- `dfloat GetEffectiveDamage(bool isCritical)` - Get damage with modifiers
- `bool RollCritical(uint randomSeed)` - Determine critical hit

### TargetingData
Component for target selection and tracking.

```csharp
public struct TargetingData : IComponentData
```

#### Key Properties
- `Entity currentTarget` - Currently targeted entity
- `TargetingStrategy strategy` - Target selection strategy
- `dfloat targetingRange` - Range for target acquisition
- `dfloat targetLossRange` - Range at which to lose target
- `bool requireLineOfSight` - Whether LOS is required

#### Key Methods
- `bool HasValidTarget()` - Check if current target is valid
- `bool IsTargetInRange(dfloat3 attackerPos, dfloat3 targetPos)` - Range check
- `void SetTarget(Entity target, dfloat3 position, dfloat time)` - Set new target
- `void ClearTarget(dfloat time)` - Clear current target

### ProjectileData
Component for projectile entities.

```csharp
public struct ProjectileData : IComponentData
```

#### Key Properties
- `Entity sourceEntity` - Entity that fired projectile
- `Entity targetEntity` - Target entity (for homing)
- `dfloat3 velocity` - Current velocity vector
- `ProjectileType projectileType` - Projectile behavior type
- `dfloat damage` - Damage to deal on hit
- `dfloat lifetime` - Maximum existence time

#### Key Methods
- `bool HasExpired(dfloat currentTime)` - Check if expired
- `bool CanPierce()` - Check if can pierce targets
- `void ConsumePierce()` - Use up one pierce
- `dfloat3 CalculateHomingDirection(dfloat3 pos, dfloat3 target)` - Homing calc

### DamageData
Component representing damage events.

```csharp
public struct DamageData : IComponentData
```

#### Key Properties
- `Entity sourceEntity` - Damage source
- `Entity targetEntity` - Damage target
- `dfloat baseDamage` - Base damage amount
- `DamageType damageType` - Type of damage
- `bool isCritical` - Whether this is a critical hit
- `dfloat armorPenetration` - Armor penetration amount

#### Key Methods
- `dfloat CalculateFinalDamage(dfloat armor, dfloat resistance)` - Final damage calc
- `bool HasStatusEffects()` - Check if applies status effects
- `dfloat CalculateLifeSteal()` - Calculate life steal amount

## Utility Classes

### CombatComponentUtils
Static utility class for combat component management.

#### Key Methods
```csharp
// Add combat components to entity
static void AddCombatComponents(EntityManager em, Entity unit, UnitStats stats)

// Create AttackData from UnitStats
static AttackData CreateAttackDataFromStats(UnitStats stats)

// Create projectile entity
static Entity CreateProjectile(EntityManager em, Entity source, 
    dfloat3 start, dfloat3 target, dfloat damage, ProjectileType type)

// Apply damage to target
static void ApplyDamage(EntityManager em, Entity source, Entity target, 
    dfloat damage, dfloat3 position, DamageType type)

// Check if entity has combat components
static bool HasCombatComponents(EntityManager em, Entity entity)

// Get effective attack range including modifiers
static dfloat GetEffectiveAttackRange(EntityManager em, Entity entity)
```

### AttackPatternUtils
Static utility class for executing attack patterns.

#### Key Methods
```csharp
// Execute single target attack
static void ExecuteSingleTargetAttack(EntityManager em, Entity attacker, 
    Entity target, AttackData attackData, dfloat3 attackerPos, dfloat3 targetPos, dfloat time)

// Execute linear attack
static void ExecuteLinearAttack(EntityManager em, Entity attacker, 
    dfloat3 attackerPos, dfloat3 targetPos, AttackData attackData, dfloat time)

// Execute AOE attack at position
static void ExecuteAOEPositionAttack(EntityManager em, Entity attacker, 
    dfloat3 attackerPos, dfloat3 targetPos, AttackData attackData, dfloat time, EntityQuery enemies)

// Execute homing attack
static void ExecuteHomingAttack(EntityManager em, Entity attacker, Entity target,
    dfloat3 attackerPos, AttackData attackData, dfloat time)
```

### TargetingUtils
Static utility class for targeting calculations.

#### Key Methods
```csharp
// Check line of sight between positions
static bool HasLineOfSight(dfloat3 from, dfloat3 to, dfloat maxDistance)

// Check if target is in range
static bool IsInRange(dfloat3 attackerPos, dfloat3 targetPos, dfloat range)

// Check if target is in cone
static bool IsInCone(dfloat3 attackerPos, dfloat3 forward, dfloat3 targetPos, 
    dfloat angle, dfloat range)

// Find nearest target
static Entity FindNearestTarget(dfloat3 position, dfloat maxRange,
    NativeArray<Entity> entities, NativeArray<SimulationTransform> transforms)

// Calculate target priority
static dfloat CalculateTargetPriority(dfloat3 attackerPos, dfloat3 targetPos, 
    UnitStats targetStats, TargetingStrategy strategy)

// Predict target position
static dfloat3 PredictTargetPosition(dfloat3 currentPos, dfloat3 velocity, dfloat time)
```

### DamageUtils
Static utility class for damage calculations.

#### Key Methods
```csharp
// Calculate armor damage reduction
static dfloat CalculateArmorReduction(dfloat armor, dfloat penetration)

// Calculate final damage with all modifiers
static dfloat CalculateFinalDamage(dfloat baseDamage, dfloat armor, dfloat resistance,
    dfloat penetration, dfloat critMultiplier, bool isCrit, bool ignoreArmor, bool isTrue)

// Roll for critical hit
static bool RollCriticalHit(dfloat baseCritChance, dfloat bonus, uint seed)

// Calculate DOT tick damage
static dfloat CalculateDOTTickDamage(dfloat totalDamage, dfloat duration, dfloat tickRate)

// Calculate AOE damage with falloff
static dfloat CalculateAOEDamage(dfloat baseDamage, dfloat distance, 
    dfloat maxRadius, dfloat falloff)

// Calculate life steal healing
static dfloat CalculateLifeSteal(dfloat damageDealt, dfloat percent, dfloat flat)
```

### CombatEffectUtils
Static utility class for applying combat effects.

#### Key Methods
```csharp
// Apply damage over time
static void ApplyDamageOverTime(EntityManager em, Entity target, dfloat damagePerTick,
    dfloat duration, dfloat tickRate, DamageType type, int sourceId)

// Apply healing over time
static void ApplyHealingOverTime(EntityManager em, Entity target, dfloat healingPerTick,
    dfloat duration, dfloat tickRate, int sourceId)

// Apply shield effect
static void ApplyShield(EntityManager em, Entity target, dfloat amount, 
    dfloat duration, int sourceId)

// Apply stun effect
static void ApplyStun(EntityManager em, Entity target, dfloat duration, int sourceId)

// Apply slow effect
static void ApplySlow(EntityManager em, Entity target, dfloat amount, 
    dfloat duration, int sourceId)

// Apply damage buff
static void ApplyDamageBuff(EntityManager em, Entity target, dfloat multiplier,
    dfloat duration, int sourceId)

// Remove effects by type
static void RemoveEffectType(EntityManager em, Entity target, GameEffect.EffectType type)

// Check if target has effect
static bool HasEffectType(EntityManager em, Entity target, GameEffect.EffectType type)
```

## Integration Classes

### CombatSystemIntegration
Main integration utility for combat system setup.

#### Key Methods
```csharp
// Setup complete combat unit
static void SetupCombatUnit(EntityManager em, Entity unit, CombatUnitDefinition def)

// Add combat to existing unit
static void AddCombatToExistingUnit(EntityManager em, Entity unit, CombatUnitDefinition def)

// Synchronize combat stats with UnitStats
static void SynchronizeCombatStats(EntityManager em, Entity unit)

// Handle unit death
static void HandleUnitDeath(EntityManager em, Entity unit, Entity killer, dfloat time)

// Get effective combat stats
static CombatStats GetEffectiveCombatStats(EntityManager em, Entity unit)
```

### CombatIntegrationUtils
Additional integration utilities.

#### Key Methods
```csharp
// Add combat to unit
static void AddCombatToUnit(EntityManager em, Entity unit, 
    AttackPattern pattern, TargetingStrategy strategy)

// Remove combat from unit
static void RemoveCombatFromUnit(EntityManager em, Entity unit)

// Check combat capabilities
static bool HasCombatCapabilities(EntityManager em, Entity unit)

// Get combat readiness status
static CombatReadiness GetCombatReadiness(EntityManager em, Entity unit)
```

## Configuration Classes

### AttackPatternDefinition
ScriptableObject for defining attack patterns.

#### Key Properties
- `AttackPattern AttackPattern` - Type of attack
- `dfloat BaseDamage` - Base damage amount
- `dfloat AttackRange` - Attack range
- `dfloat AttackCooldown` - Cooldown duration
- `dfloat AoeRadius` - AOE radius
- `int MaxTargets` - Maximum targets
- `dfloat ProjectileSpeed` - Projectile speed

#### Key Methods
```csharp
// Create AttackData from definition
AttackData CreateAttackData()

// Validate configuration
bool ValidateConfiguration(out string errorMessage)
```

### CombatUnitDefinition
ScriptableObject for defining combat units.

#### Key Properties
- `AttackPatternDefinition PrimaryAttack` - Primary attack
- `AttackPatternDefinition[] SecondaryAttacks` - Secondary attacks
- `SpecialAbilityDefinition[] SpecialAbilities` - Special abilities
- `dfloat MaxHealth` - Maximum health
- `DamageType[] ResistantTo` - Damage resistances

#### Key Methods
```csharp
// Create UnitStats from definition
UnitStats CreateUnitStats()

// Create AttackData from definition
AttackData CreateAttackData()

// Create TargetingData from definition
TargetingData CreateTargetingData()

// Check resistance to damage type
bool IsResistantTo(DamageType type)

// Get damage multiplier for type
dfloat GetDamageMultiplier(DamageType type)
```

## Enums

### AttackPattern
```csharp
public enum AttackPattern : byte
{
    SingleTarget, Linear, LinearPiercing, MultiLinearWave,
    Homing, AOEPosition, AOESelf, Cone, Circle, FullMap, Chain, Beam
}
```

### TargetingStrategy
```csharp
public enum TargetingStrategy : byte
{
    Nearest, Farthest, HighestHealth, LowestHealth,
    First, Last, Strongest, Weakest, Random
}
```

### ProjectileType
```csharp
public enum ProjectileType : byte
{
    Instant, Linear, Homing, Arc, Bouncing, Seeking
}
```

### DamageType
```csharp
public enum DamageType : byte
{
    Physical, Magical, True, Elemental, Poison, Fire, Ice, Lightning
}
```

### CombatState
```csharp
[Flags]
public enum CombatState : byte
{
    None, Attacking, OnCooldown, HasTarget, Stunned, Silenced, Disarmed, Channeling
}
```

## Systems

### TargetSelectionSystem
Handles target acquisition and tracking.
- Updates targeting data for all combat units
- Implements various targeting strategies
- Manages target validation and loss

### AttackProcessingSystem
Processes attack timing and triggers.
- Manages attack cooldowns
- Determines when units can attack
- Triggers attack execution

### AttackExecutionSystem
Executes actual attack patterns.
- Creates projectiles
- Applies instant damage
- Handles different attack patterns

### ProjectileSystem
Manages projectile movement and collision.
- Updates projectile positions
- Handles collision detection
- Manages projectile lifetime

### DamageApplicationSystem
Applies damage to targets.
- Calculates final damage
- Applies status effects
- Handles life steal and shields

### CombatEffectsProcessingSystem
Processes combat-specific effects.
- Manages DOT effects
- Handles combat state changes
- Integrates with GameEffect system
