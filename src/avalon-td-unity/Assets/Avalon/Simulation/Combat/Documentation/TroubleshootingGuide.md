# FlowField Combat System - Troubleshooting Guide

## Common Issues and Solutions

### Units Not Attacking

#### Symptoms
- Units have targets but don't attack
- Attack animations don't play
- No projectiles are created

#### Possible Causes & Solutions

1. **Missing Combat Components**
   ```csharp
   // Check if unit has required components
   if (!CombatComponentUtils.HasCombatComponents(entityManager, unit))
   {
       // Add combat components
       CombatSystemIntegration.AddCombatToUnit(entityManager, unit);
   }
   ```

2. **Unit is Stunned or Disarmed**
   ```csharp
   var attackData = entityManager.GetComponentData<AttackData>(unit);
   if ((attackData.combatState & CombatState.Stunned) != 0)
   {
       // Unit is stunned - remove stun effect
       CombatEffectUtils.RemoveEffectType(entityManager, unit, GameEffect.EffectType.Stun);
   }
   ```

3. **Attack on Cooldown**
   ```csharp
   var attackData = entityManager.GetComponentData<AttackData>(unit);
   dfloat currentTime = (dfloat)Time.time;
   if (!attackData.CanAttack(currentTime))
   {
       // Reset cooldown for testing
       attackData.lastAttackTime = dfloat.Zero;
       entityManager.SetComponentData(unit, attackData);
   }
   ```

4. **Target Out of Range**
   ```csharp
   var targetingData = entityManager.GetComponentData<TargetingData>(unit);
   var unitTransform = entityManager.GetComponentData<SimulationTransform>(unit);
   var targetTransform = entityManager.GetComponentData<SimulationTransform>(targetingData.currentTarget);
   
   if (!targetingData.IsTargetInRange(unitTransform.position, targetTransform.position))
   {
       // Increase targeting range or move unit closer
       targetingData.targetingRange = (dfloat)10; // Increase range
       entityManager.SetComponentData(unit, targetingData);
   }
   ```

### Projectiles Not Hitting Targets

#### Symptoms
- Projectiles are created but miss targets
- Projectiles pass through enemies
- Homing projectiles don't track properly

#### Possible Causes & Solutions

1. **Collision Radius Too Small**
   ```csharp
   var projectileData = entityManager.GetComponentData<ProjectileData>(projectile);
   projectileData.collisionRadius = (dfloat)1.0f; // Increase collision radius
   entityManager.SetComponentData(projectile, projectileData);
   ```

2. **Target Moving Too Fast**
   ```csharp
   // Enable target prediction
   var targetingData = entityManager.GetComponentData<TargetingData>(attacker);
   targetingData.usePrediction = true;
   targetingData.predictionTime = (dfloat)0.5f;
   entityManager.SetComponentData(attacker, targetingData);
   ```

3. **Homing Strength Too Low**
   ```csharp
   var projectileData = entityManager.GetComponentData<ProjectileData>(projectile);
   projectileData.homingStrength = (dfloat)5.0f; // Increase homing strength
   projectileData.maxTurnRate = (dfloat)6.28f; // 360 degrees per second
   entityManager.SetComponentData(projectile, projectileData);
   ```

### Damage Not Being Applied

#### Symptoms
- Projectiles hit but no damage is dealt
- Health bars don't decrease
- Units don't die when they should

#### Possible Causes & Solutions

1. **Damage Events Not Created**
   ```csharp
   // Manually create damage event for testing
   CombatComponentUtils.ApplyDamage(entityManager, attacker, target, 
       (dfloat)100, targetPosition, DamageType.Physical);
   ```

2. **Target Has High Armor/Resistance**
   ```csharp
   // Check effective damage calculation
   var damageData = DamageData.CreatePhysical(attacker, target, (dfloat)100, position);
   dfloat finalDamage = damageData.CalculateFinalDamage((dfloat)0, (dfloat)0); // No armor/resistance
   
   // Or use true damage to bypass defenses
   var trueDamage = DamageData.CreateTrue(attacker, target, (dfloat)100, position);
   ```

3. **Target is Immune**
   ```csharp
   // Check if target has damage immunities
   if (entityManager.HasComponent<DamageResistances>(target))
   {
       var resistances = entityManager.GetComponentData<DamageResistances>(target);
       // Check immunity flags and adjust damage type
   }
   ```

### Performance Issues

#### Symptoms
- Low frame rate during combat
- Stuttering when many units attack
- High memory usage

#### Possible Causes & Solutions

1. **Too Many Active Entities**
   ```csharp
   // Monitor entity counts
   var combatUnits = entityManager.CreateEntityQuery(typeof(AttackData)).CalculateEntityCount();
   var projectiles = entityManager.CreateEntityQuery(typeof(ProjectileData)).CalculateEntityCount();
   
   if (combatUnits > 1000 || projectiles > 500)
   {
       // Implement LOD system or reduce spawn rates
   }
   ```

2. **Inefficient Target Selection**
   ```csharp
   // Reduce targeting range to limit query scope
   var targetingData = entityManager.GetComponentData<TargetingData>(unit);
   targetingData.targetingRange = (dfloat)5; // Reduce from larger value
   entityManager.SetComponentData(unit, targetingData);
   ```

3. **Memory Leaks from Projectiles**
   ```csharp
   // Enable projectile pooling
   ProjectileUtils.CreateProjectilePool(entityManager, projectilePrefab, 100);
   
   // Use pooled projectiles instead of creating new ones
   var pooledProjectile = ProjectileUtils.GetPooledProjectile(entityManager, projectilePrefab);
   ```

### Effects Not Working

#### Symptoms
- DOT effects don't tick
- Stuns don't prevent attacks
- Buffs don't apply

#### Possible Causes & Solutions

1. **Missing Effect Buffer**
   ```csharp
   // Ensure unit has GameEffect buffer
   if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(unit))
   {
       entityManager.AddBuffer<GameEffect>(unit);
   }
   ```

2. **Effect Duration Expired**
   ```csharp
   // Check effect timings
   var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
   dfloat currentTime = (dfloat)Time.time;
   
   for (int i = 0; i < effectBuffer.Length; i++)
   {
       var effect = effectBuffer[i];
       if ((currentTime - effect.startTime) >= effect.duration)
       {
           // Effect has expired
           effectBuffer.RemoveAt(i);
       }
   }
   ```

3. **Effect Not Processing**
   ```csharp
   // Manually trigger effect processing
   var effectsSystem = World.DefaultGameObjectInjectionWorld
       .GetOrCreateSystemManaged<CombatEffectsProcessingSystem>();
   effectsSystem.Update();
   ```

### Integration Issues

#### Symptoms
- Combat stats don't match UnitStats
- Effects don't integrate with existing systems
- System update order problems

#### Possible Causes & Solutions

1. **Stats Not Synchronized**
   ```csharp
   // Force synchronization
   CombatSystemIntegration.SynchronizeCombatStats(entityManager, unit);
   ```

2. **Missing Integration Components**
   ```csharp
   // Add integration marker
   entityManager.AddComponentData(unit, new NeedsCombatSetup
   {
       useDefaultAttack = true,
       preferredPattern = AttackPattern.SingleTarget
   });
   ```

3. **System Update Order**
   ```csharp
   // Verify system group order in FlowFieldSystemGroup
   // Combat should update after Effects but before Movement
   ```

## Debugging Tools

### Visual Debugging
```csharp
// Add combat debugger to scene
var debugger = gameObject.AddComponent<CombatDebugger>();
debugger.enableDebugging = true;
debugger.showAttackRanges = true;
debugger.showTargetingLines = true;
debugger.showProjectilePaths = true;
debugger.showCombatStates = true;
```

### Performance Monitoring
```csharp
// Add performance profiler
var profiler = gameObject.AddComponent<CombatPerformanceProfiler>();
profiler.enableProfiling = true;
profiler.showOnScreenStats = true;
profiler.logToConsole = true;
```

### Manual Testing
```csharp
// Test combat functionality manually
public void TestCombatSystem()
{
    var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
    
    // Create test attacker
    var attacker = entityManager.CreateEntity();
    CombatComponentUtils.AddCombatComponents(entityManager, attacker, 
        AttackPattern.SingleTarget, TargetingStrategy.Nearest, 
        (dfloat)50, (dfloat)5, (dfloat)1);
    
    // Create test target
    var target = entityManager.CreateEntity();
    entityManager.AddComponentData(target, new UnitStats
    {
        maxHealth = (dfloat)100,
        currentHealth = (dfloat)100
    });
    
    // Force attack
    CombatComponentUtils.ApplyDamage(entityManager, attacker, target, 
        (dfloat)25, dfloat3.zero, DamageType.Physical);
    
    // Verify damage was applied
    var targetStats = entityManager.GetComponentData<UnitStats>(target);
    Debug.Log($"Target health after damage: {targetStats.currentHealth}");
}
```

## Validation Checklist

### Before Reporting Issues

1. **Check Entity Components**
   - [ ] Unit has AttackData component
   - [ ] Unit has TargetingData component
   - [ ] Unit has SimulationTransform component
   - [ ] Unit has UnitStats component

2. **Verify Configuration**
   - [ ] Attack pattern is properly configured
   - [ ] Damage values are greater than 0
   - [ ] Range values are appropriate
   - [ ] Cooldown values are reasonable

3. **Test System Integration**
   - [ ] Combat systems are in correct update order
   - [ ] Fixed timestep is configured
   - [ ] No conflicting systems

4. **Performance Check**
   - [ ] Entity counts are reasonable
   - [ ] No memory leaks
   - [ ] Frame rate is acceptable

### Getting Help

If issues persist after following this guide:

1. **Enable Debug Logging**
   ```csharp
   CombatDebugger.enableDebugging = true;
   CombatPerformanceProfiler.logToConsole = true;
   ```

2. **Collect System Information**
   - Unity version
   - DOTS packages versions
   - Entity counts
   - Performance metrics

3. **Provide Minimal Reproduction**
   - Simplest possible setup that shows the issue
   - Step-by-step reproduction instructions
   - Expected vs actual behavior

4. **Check Documentation**
   - Review API Reference
   - Check examples in Combat System Guide
   - Verify configuration against examples
