using Avalon.Simulation.Combat;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Core
{
    /// <summary>
    /// Component for projectile entities that handles movement, targeting, and collision
    /// Contains all data needed for projectile behavior including physics and damage
    /// </summary>
    public struct ProjectileData : IComponentData
    {
        // Basic projectile properties
        public Entity sourceEntity;         // Entity that fired this projectile
        public Entity targetEntity;         // Target entity (for homing projectiles)
        public dfloat3 targetPosition;      // Target position (for non-homing projectiles)
        public dfloat3 startPosition;       // Starting position of projectile
        public dfloat3 velocity;            // Current velocity vector
        public dfloat3 acceleration;        // Acceleration vector (for gravity, etc.)
        
        // Projectile parameters
        public ProjectileType projectileType; // Type of projectile behavior
        public dfloat speed;                // Base movement speed
        public dfloat lifetime;             // Maximum time projectile can exist
        public dfloat spawnTime;            // When projectile was created
        public dfloat collisionRadius;      // Radius for collision detection
        
        // Homing parameters
        public dfloat homingStrength;       // Turn rate for homing projectiles
        public dfloat homingDelay;          // Delay before homing starts
        public dfloat maxTurnRate;          // Maximum turn rate per second
        
        // Arc parameters
        public dfloat arcHeight;            // Maximum height for arcing projectiles
        public dfloat gravity;              // Gravity strength for arcing projectiles
        
        // Bouncing parameters
        public int bounceCount;             // Number of bounces remaining
        public int maxBounces;              // Maximum number of bounces
        public dfloat bounceDecay;          // Speed reduction per bounce (0-1)
        public dfloat bounceRange;          // Range to search for bounce targets
        
        // Piercing parameters
        public int pierceCount;             // Number of targets remaining to pierce
        public int maxPierces;              // Maximum number of targets to pierce
        public dfloat pierceDamageDecay;    // Damage reduction per pierce (0-1)
        
        // Damage and effects
        public dfloat damage;               // Damage to deal on hit
        public DamageType damageType;       // Type of damage
        public dfloat criticalChance;       // Chance for critical hit
        public dfloat criticalMultiplier;   // Critical damage multiplier
        public dfloat armorPenetration;     // Armor penetration amount
        
        // Area of effect
        public dfloat aoeRadius;            // Radius for AOE damage
        public dfloat aoeFalloff;           // Damage falloff with distance (0-1)
        public int maxAoeTargets;           // Maximum targets hit by AOE
        
        // Status effects to apply on hit
        public dfloat stunDuration;         // Stun duration to apply
        public dfloat slowAmount;           // Slow amount to apply (0-1)
        public dfloat slowDuration;         // Slow duration to apply
        public dfloat dotDamage;            // DOT damage per tick
        public dfloat dotDuration;          // DOT duration
        public dfloat dotTickRate;          // DOT ticks per second
        
        // Visual and audio
        public Entity visualPrefab;         // Visual representation entity
        public Entity hitEffectPrefab;      // Effect to spawn on hit
        public Entity trailEffectPrefab;    // Trail effect entity
        
        // Projectile state flags
        public byte projectileFlags;        // Additional behavior flags
        
        /// <summary>
        /// Check if projectile has expired
        /// </summary>
        public bool HasExpired(dfloat currentTime)
        {
            return (currentTime - spawnTime) >= lifetime;
        }
        
        /// <summary>
        /// Check if projectile can still pierce targets
        /// </summary>
        public bool CanPierce()
        {
            return pierceCount > 0;
        }
        
        /// <summary>
        /// Check if projectile can still bounce
        /// </summary>
        public bool CanBounce()
        {
            return bounceCount > 0;
        }
        
        /// <summary>
        /// Consume a pierce, reducing damage if applicable
        /// </summary>
        public void ConsumePierce()
        {
            if (pierceCount > 0)
            {
                pierceCount--;
                damage *= (dfloat.One - pierceDamageDecay);
            }
        }
        
        /// <summary>
        /// Consume a bounce, reducing speed if applicable
        /// </summary>
        public void ConsumeBounce()
        {
            if (bounceCount > 0)
            {
                bounceCount--;
                speed *= (dfloat.One - bounceDecay);
            }
        }
        
        /// <summary>
        /// Get current damage accounting for piercing decay
        /// </summary>
        public dfloat GetCurrentDamage()
        {
            return damage;
        }
        
        /// <summary>
        /// Calculate homing direction towards target
        /// </summary>
        public dfloat3 CalculateHomingDirection(dfloat3 currentPosition, dfloat3 targetPos)
        {
            dfloat3 toTarget = dmath.normalize(targetPos - currentPosition);
            dfloat3 currentDirection = dmath.normalize(velocity);
            
            // Interpolate between current direction and target direction
            return dmath.normalize(dmath.lerp(currentDirection, toTarget, homingStrength));
        }
        
        /// <summary>
        /// Create a basic projectile
        /// </summary>
        public static ProjectileData CreateLinear(Entity source, dfloat3 start, dfloat3 target, dfloat projectileSpeed, dfloat projectileDamage)
        {
            dfloat3 direction = dmath.normalize(target - start);
            
            return new ProjectileData
            {
                sourceEntity = source,
                targetPosition = target,
                startPosition = start,
                velocity = direction * projectileSpeed,
                projectileType = ProjectileType.Linear,
                speed = projectileSpeed,
                lifetime = (dfloat)10,
                collisionRadius = (dfloat)0.5f,
                damage = projectileDamage,
                damageType = DamageType.Physical,
                maxPierces = 0,
                pierceCount = 0,
                maxBounces = 0,
                bounceCount = 0
            };
        }
        
        /// <summary>
        /// Create a homing projectile
        /// </summary>
        public static ProjectileData CreateHoming(Entity source, Entity target, dfloat3 start, dfloat projectileSpeed, dfloat projectileDamage, dfloat homing)
        {
            return new ProjectileData
            {
                sourceEntity = source,
                targetEntity = target,
                startPosition = start,
                velocity = new dfloat3(dfloat.Zero, dfloat.Zero, projectileSpeed), // Initial forward velocity
                projectileType = ProjectileType.Homing,
                speed = projectileSpeed,
                lifetime = (dfloat)10,
                collisionRadius = (dfloat)0.5f,
                damage = projectileDamage,
                damageType = DamageType.Physical,
                homingStrength = homing,
                maxTurnRate = (dfloat)3.14159f, // 180 degrees per second
                maxPierces = 0,
                pierceCount = 0
            };
        }
    }
}
