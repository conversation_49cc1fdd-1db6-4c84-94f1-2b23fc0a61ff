using Unity.Entities;

namespace Avalon.Simulation.Combat.Core
{
    /// <summary>
    /// Component that defines team affiliation and hostility relationships for combat units
    /// Used to determine valid targets and friendly fire behavior
    /// </summary>
    public struct TeamData : IComponentData
    {
        /// <summary>
        /// Unique identifier for this entity's team
        /// </summary>
        public int teamId;
        
        /// <summary>
        /// Team ID that this entity considers hostile
        /// Entities with this team ID will be valid targets
        /// </summary>
        public int isHostileTo;
        
        /// <summary>
        /// Whether this entity can attack members of its own team
        /// </summary>
        public bool allowFriendlyFire;
        
        /// <summary>
        /// Whether this entity can be targeted by hostile teams
        /// </summary>
        public bool canBeTargeted;
        
        /// <summary>
        /// Check if this entity should consider another entity as hostile
        /// </summary>
        public bool IsHostile(TeamData otherTeam)
        {
            // If friendly fire is disabled and same team, not hostile
            if (!allowFriendlyFire && teamId == otherTeam.teamId)
                return false;
                
            // Check if the other team is marked as hostile
            return otherTeam.teamId == isHostileTo;
        }
        
        /// <summary>
        /// Check if this entity can target another entity
        /// </summary>
        public bool CanTarget(TeamData otherTeam)
        {
            return otherTeam.canBeTargeted && IsHostile(otherTeam);
        }
        
        /// <summary>
        /// Create team data for a friendly unit
        /// </summary>
        public static TeamData CreateFriendly(int teamId, int hostileTeamId)
        {
            return new TeamData
            {
                teamId = teamId,
                isHostileTo = hostileTeamId,
                allowFriendlyFire = false,
                canBeTargeted = true
            };
        }
        
        /// <summary>
        /// Create team data for an enemy unit
        /// </summary>
        public static TeamData CreateEnemy(int teamId, int hostileTeamId)
        {
            return new TeamData
            {
                teamId = teamId,
                isHostileTo = hostileTeamId,
                allowFriendlyFire = false,
                canBeTargeted = true
            };
        }
        
        /// <summary>
        /// Create team data for a neutral unit that can't be targeted
        /// </summary>
        public static TeamData CreateNeutral(int teamId)
        {
            return new TeamData
            {
                teamId = teamId,
                isHostileTo = -1, // No hostile team
                allowFriendlyFire = false,
                canBeTargeted = false
            };
        }
    }
}
