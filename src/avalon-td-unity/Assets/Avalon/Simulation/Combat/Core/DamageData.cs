using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Core
{
    /// <summary>
    /// Component that represents a damage event to be applied to an entity
    /// Used for queuing and processing damage in a deterministic way
    /// </summary>
    public struct DamageData : IComponentData
    {
        // Damage source and target
        public Entity sourceEntity;         // Entity that caused the damage
        public Entity targetEntity;         // Entity receiving the damage
        public dfloat3 damagePosition;      // Position where damage occurred
        public dfloat damageTime;           // When damage was applied
        
        // Damage values
        public dfloat baseDamage;           // Base damage amount
        public dfloat finalDamage;          // Final damage after all calculations
        public DamageType damageType;       // Type of damage being dealt
        
        // Damage modifiers
        public dfloat armorPenetration;     // Amount of armor to ignore (0-1)
        public dfloat criticalMultiplier;   // Critical hit multiplier (1.0 = no crit)
        public dfloat damageMultiplier;     // Additional damage multiplier
        public dfloat resistanceReduction;  // Resistance reduction amount
        
        // Damage flags
        public bool isCritical;             // Whether this is a critical hit
        public bool ignoresArmor;           // Whether damage ignores all armor
        public bool ignoresShields;         // Whether damage bypasses shields
        public bool cannotBeReduced;        // Whether damage cannot be reduced
        public bool cannotBeBlocked;        // Whether damage cannot be blocked
        public bool isReflected;            // Whether this is reflected damage
        public bool isTrueDamage;           // Whether this is true damage
        
        // Status effects to apply with damage
        public dfloat stunDuration;         // Stun duration to apply
        public dfloat slowAmount;           // Slow amount (0-1)
        public dfloat slowDuration;         // Slow duration
        public dfloat knockbackForce;       // Knockback force to apply
        public dfloat3 knockbackDirection;  // Direction of knockback
        
        // Damage over time effects
        public dfloat dotDamage;            // DOT damage per tick
        public dfloat dotDuration;          // DOT total duration
        public dfloat dotTickRate;          // DOT ticks per second
        public DamageType dotType;          // Type of DOT damage
        
        // Life steal and healing
        public dfloat lifeStealAmount;      // Amount of damage to heal source
        public dfloat lifeStealPercent;     // Percentage of damage to heal (0-1)
        
        // Visual and audio effects
        public Entity hitEffectPrefab;      // Visual effect to spawn
        public dfloat3 effectScale;         // Scale for visual effects
        public byte effectFlags;            // Additional effect flags
        
        /// <summary>
        /// Calculate final damage after applying all modifiers
        /// </summary>
        public dfloat CalculateFinalDamage(dfloat targetArmor, dfloat targetResistance)
        {
            dfloat damage = baseDamage;
            
            // Apply critical hit multiplier
            if (isCritical)
            {
                damage *= criticalMultiplier;
            }
            
            // Apply damage multiplier
            damage *= damageMultiplier;
            
            // Apply armor reduction if not ignored
            if (!ignoresArmor && !isTrueDamage)
            {
                dfloat effectiveArmor = targetArmor * (dfloat.One - armorPenetration);
                dfloat armorReduction = effectiveArmor / (effectiveArmor + (dfloat)100);
                damage *= (dfloat.One - armorReduction);
            }
            
            // Apply resistance reduction if not true damage
            if (!isTrueDamage)
            {
                dfloat effectiveResistance = dmath.max(dfloat.Zero, targetResistance - resistanceReduction);
                damage *= (dfloat.One - effectiveResistance);
            }
            
            // Ensure minimum damage
            damage = dmath.max(damage, dfloat.Zero);
            
            finalDamage = damage;
            return damage;
        }
        
        /// <summary>
        /// Check if this damage should apply status effects
        /// </summary>
        public bool HasStatusEffects()
        {
            return stunDuration > dfloat.Zero || 
                   slowDuration > dfloat.Zero || 
                   dotDuration > dfloat.Zero ||
                   knockbackForce > dfloat.Zero;
        }
        
        /// <summary>
        /// Check if this damage should heal the source
        /// </summary>
        public bool HasLifeSteal()
        {
            return lifeStealAmount > dfloat.Zero || lifeStealPercent > dfloat.Zero;
        }
        
        /// <summary>
        /// Calculate life steal healing amount
        /// </summary>
        public dfloat CalculateLifeSteal()
        {
            dfloat healing = lifeStealAmount;
            healing += finalDamage * lifeStealPercent;
            return healing;
        }
        
        /// <summary>
        /// Create basic physical damage
        /// </summary>
        public static DamageData CreatePhysical(Entity source, Entity target, dfloat damage, dfloat3 position)
        {
            return new DamageData
            {
                sourceEntity = source,
                targetEntity = target,
                damagePosition = position,
                baseDamage = damage,
                finalDamage = damage,
                damageType = DamageType.Physical,
                criticalMultiplier = dfloat.One,
                damageMultiplier = dfloat.One,
                isCritical = false,
                ignoresArmor = false,
                ignoresShields = false,
                cannotBeReduced = false,
                cannotBeBlocked = false,
                isReflected = false,
                isTrueDamage = false
            };
        }
        
        /// <summary>
        /// Create critical hit damage
        /// </summary>
        public static DamageData CreateCritical(Entity source, Entity target, dfloat damage, dfloat critMultiplier, dfloat3 position)
        {
            var damageData = CreatePhysical(source, target, damage, position);
            damageData.isCritical = true;
            damageData.criticalMultiplier = critMultiplier;
            return damageData;
        }
        
        /// <summary>
        /// Create true damage that ignores all defenses
        /// </summary>
        public static DamageData CreateTrue(Entity source, Entity target, dfloat damage, dfloat3 position)
        {
            var damageData = CreatePhysical(source, target, damage, position);
            damageData.damageType = DamageType.True;
            damageData.isTrueDamage = true;
            damageData.ignoresArmor = true;
            damageData.cannotBeReduced = true;
            return damageData;
        }
        
        /// <summary>
        /// Create damage with status effects
        /// </summary>
        public static DamageData CreateWithEffects(Entity source, Entity target, dfloat damage, dfloat3 position, 
            dfloat stun = default, dfloat slowAmt = default, dfloat slowDur = default)
        {
            var damageData = CreatePhysical(source, target, damage, position);
            damageData.stunDuration = stun;
            damageData.slowAmount = slowAmt;
            damageData.slowDuration = slowDur;
            return damageData;
        }
    }
}
