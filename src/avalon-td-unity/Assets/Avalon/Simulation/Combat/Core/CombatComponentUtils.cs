using Avalon.Simulation.Combat;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Entities;
using Unity.Deterministic.Mathematics;

namespace Avalon.Simulation.Combat.Core
{
    /// <summary>
    /// Utility class for adding and managing combat components on entities
    /// Provides convenience methods for setting up combat-enabled units
    /// </summary>
    public static class CombatComponentUtils
    {
        /// <summary>
        /// Add all necessary combat components to a unit entity
        /// </summary>
        public static void AddCombatComponents(EntityManager entityManager, Entity unit, UnitStats stats)
        {
            // Create combat components from UnitStats
            var attackData = CreateAttackDataFromStats(stats);
            var targetingData = CreateTargetingDataFromStats(stats);
            
            // Add components to entity
            entityManager.AddComponentData(unit, attackData);
            entityManager.AddComponentData(unit, targetingData);
        }
        
        /// <summary>
        /// Add combat components with custom configuration
        /// </summary>
        public static void AddCombatComponents(EntityManager entityManager, Entity unit, 
            AttackPattern attackPattern, TargetingStrategy targetingStrategy, 
            dfloat damage, dfloat range, dfloat cooldown)
        {
            var attackData = AttackData.CreateDefault();
            attackData.baseDamage = damage;
            attackData.attackRange = range;
            attackData.attackCooldown = cooldown;
            attackData.attackPattern = attackPattern;
            
            var targetingData = TargetingData.CreateDefault();
            targetingData.strategy = targetingStrategy;
            targetingData.targetingRange = range;
            targetingData.targetLossRange = range * (dfloat)1.2f; // 20% larger loss range
            
            entityManager.AddComponentData(unit, attackData);
            entityManager.AddComponentData(unit, targetingData);
        }
        
        /// <summary>
        /// Create AttackData from UnitStats
        /// </summary>
        public static AttackData CreateAttackDataFromStats(UnitStats stats)
        {
            var attackData = AttackData.CreateDefault();
            
            // Copy values from UnitStats
            attackData.baseDamage = stats.attackDamage;
            attackData.attackRange = stats.attackRange;
            attackData.attackCooldown = stats.attackCooldown;
            
            // Set reasonable defaults for other parameters
            attackData.attackPattern = AttackPattern.SingleTarget;
            attackData.projectileType = ProjectileType.Instant;
            attackData.damageType = DamageType.Physical;
            attackData.maxTargets = 1;
            attackData.projectileSpeed = (dfloat)10;
            attackData.criticalChance = (dfloat)0.05f;
            attackData.criticalMultiplier = (dfloat)2;
            
            return attackData;
        }
        
        /// <summary>
        /// Create TargetingData from UnitStats
        /// </summary>
        public static TargetingData CreateTargetingDataFromStats(UnitStats stats)
        {
            var targetingData = TargetingData.CreateDefault();
            
            // Use attack range for targeting
            targetingData.targetingRange = stats.attackRange;
            targetingData.targetLossRange = stats.attackRange * (dfloat)1.2f;
            
            // Set reasonable defaults
            targetingData.strategy = TargetingStrategy.Nearest;
            targetingData.retargetDelay = (dfloat)0.5f;
            targetingData.canTargetAir = true;
            targetingData.canTargetGround = true;
            
            return targetingData;
        }
        
        /// <summary>
        /// Create a projectile entity with the specified parameters
        /// </summary>
        public static Entity CreateProjectile(EntityManager entityManager, Entity source,
            dfloat3 startPos, dfloat3 targetPos, dfloat damage, ProjectileType type = ProjectileType.Linear)
        {
            var projectile = entityManager.CreateEntity();

            // Add transform components for deterministic simulation and smooth interpolation
            entityManager.AddComponentData(projectile, new SimulationTransform
            {
                position = startPos,
                rotation = dquaternion.identity,
                scale = dfloat.One
            });

            // Add previous transform for interpolation (DETERMINISTIC)
            entityManager.AddComponentData(projectile, new PreviousSimulationTransform
            {
                Position = startPos,
                Rotation = dquaternion.identity,
                Scale = dfloat.One
            });

            // Add local transform for presentation (will be interpolated)
            entityManager.AddComponentData(projectile, new Unity.Transforms.LocalTransform
            {
                Position = new Unity.Mathematics.float3((float)startPos.x, (float)startPos.y, (float)startPos.z),
                Rotation = Unity.Mathematics.quaternion.identity,
                Scale = 1.0f
            });

            // Add projectile data
            var projectileData = type == ProjectileType.Linear
                ? ProjectileData.CreateLinear(source, startPos, targetPos, (dfloat)10, damage)
                : ProjectileData.CreateHoming(source, Entity.Null, startPos, (dfloat)10, damage, (dfloat)2);

            entityManager.AddComponentData(projectile, projectileData);

            return projectile;
        }
        
        /// <summary>
        /// Create a homing projectile targeting a specific entity
        /// </summary>
        public static Entity CreateHomingProjectile(EntityManager entityManager, Entity source, Entity target,
            dfloat3 startPos, dfloat damage, dfloat speed, dfloat homingStrength)
        {
            var projectile = entityManager.CreateEntity();

            // Add transform components for deterministic simulation and smooth interpolation
            entityManager.AddComponentData(projectile, new SimulationTransform
            {
                position = startPos,
                rotation = dquaternion.identity,
                scale = dfloat.One
            });

            // Add previous transform for interpolation (DETERMINISTIC)
            entityManager.AddComponentData(projectile, new PreviousSimulationTransform
            {
                Position = startPos,
                Rotation = dquaternion.identity,
                Scale = dfloat.One
            });

            // Add local transform for presentation (will be interpolated)
            entityManager.AddComponentData(projectile, new Unity.Transforms.LocalTransform
            {
                Position = new Unity.Mathematics.float3((float)startPos.x, (float)startPos.y, (float)startPos.z),
                Rotation = Unity.Mathematics.quaternion.identity,
                Scale = 1.0f
            });

            var projectileData = ProjectileData.CreateHoming(source, target, startPos, speed, damage, homingStrength);
            entityManager.AddComponentData(projectile, projectileData);

            return projectile;
        }
        
        /// <summary>
        /// Apply damage to a target entity
        /// </summary>
        public static void ApplyDamage(EntityManager entityManager, Entity source, Entity target, 
            dfloat damage, dfloat3 position, DamageType damageType = DamageType.Physical)
        {
            var damageData = DamageData.CreatePhysical(source, target, damage, position);
            damageData.damageType = damageType;
            
            // Create a temporary entity to hold the damage data for processing
            var damageEntity = entityManager.CreateEntity();
            entityManager.AddComponentData(damageEntity, damageData);
        }
        
        /// <summary>
        /// Check if an entity has combat capabilities
        /// </summary>
        public static bool HasCombatComponents(EntityManager entityManager, Entity entity)
        {
            return entityManager.HasComponent<AttackData>(entity) && 
                   entityManager.HasComponent<TargetingData>(entity);
        }
        
        /// <summary>
        /// Enable or disable combat for an entity
        /// </summary>
        public static void SetCombatEnabled(EntityManager entityManager, Entity entity, bool enabled)
        {
            if (entityManager.HasComponent<AttackData>(entity))
            {
                var attackData = entityManager.GetComponentData<AttackData>(entity);
                if (enabled)
                {
                    attackData.combatState &= ~CombatState.Disarmed;
                }
                else
                {
                    attackData.combatState |= CombatState.Disarmed;
                }
                entityManager.SetComponentData(entity, attackData);
            }
        }
        
        /// <summary>
        /// Get the effective attack range for an entity
        /// </summary>
        public static dfloat GetEffectiveAttackRange(EntityManager entityManager, Entity entity)
        {
            if (entityManager.HasComponent<AttackData>(entity))
            {
                var attackData = entityManager.GetComponentData<AttackData>(entity);
                
                // Apply range modifiers from CombatEffects if present
                if (entityManager.HasComponent<CombatEffects>(entity))
                {
                    var combatEffects = entityManager.GetComponentData<CombatEffects>(entity);
                    return attackData.attackRange * combatEffects.rangeMultiplier;
                }
                
                return attackData.attackRange;
            }
            
            return dfloat.Zero;
        }
    }
}
