using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Core
{
    /// <summary>
    /// Component that handles target selection and tracking for combat units
    /// Contains targeting strategy, current target, and target validation parameters
    /// </summary>
    public struct TargetingData : IComponentData
    {
        // Current target information
        public Entity currentTarget;        // Currently targeted entity
        public dfloat3 lastTargetPosition;  // Last known position of target
        public dfloat targetAcquiredTime;   // When the current target was acquired
        public dfloat targetLostTime;       // When target was lost (for re-acquisition delay)
        
        // Targeting strategy and parameters
        public TargetingStrategy strategy;  // How to select targets
        public dfloat targetingRange;       // Range for target acquisition
        public dfloat targetLossRange;      // Range at which to lose target (usually > targetingRange)
        public dfloat retargetDelay;        // Minimum time before switching targets
        public dfloat targetLostDelay;      // Delay before acquiring new target after losing one
        
        // Target validation parameters
        public bool requireLineOfSight;     // Whether line of sight is required
        public bool canTargetAir;           // Can target flying units
        public bool canTargetGround;        // Can target ground units
        public bool canTargetStructures;    // Can target buildings/structures
        public bool preferNewTargets;       // Prefer targets not being attacked by others
        
        // Target filtering
        public uint targetLayerMask;        // Layer mask for valid targets
        public uint excludeTagMask;         // Tag mask for targets to exclude
        public dfloat minTargetHealth;      // Minimum health threshold for targeting
        public dfloat maxTargetHealth;      // Maximum health threshold for targeting
        
        // Multi-target parameters (for AOE and multi-hit attacks)
        public int maxSimultaneousTargets;  // Maximum targets to track simultaneously
        public dfloat targetSpreadRadius;   // Radius for spreading targets in AOE
        
        // Target prediction (for moving targets)
        public bool usePrediction;          // Whether to predict target movement
        public dfloat predictionTime;       // How far ahead to predict
        public dfloat3 predictedPosition;   // Predicted target position
        
        // Targeting state flags
        public byte targetingFlags;         // Additional targeting behavior flags
        
        /// <summary>
        /// Check if we have a valid target
        /// </summary>
        public bool HasValidTarget()
        {
            return currentTarget != Entity.Null;
        }
        
        /// <summary>
        /// Check if target is within range
        /// </summary>
        public bool IsTargetInRange(dfloat3 attackerPosition, dfloat3 targetPosition)
        {
            dfloat distance = dmath.distance(attackerPosition, targetPosition);
            return distance <= targetingRange;
        }
        
        /// <summary>
        /// Check if we should lose the current target
        /// </summary>
        public bool ShouldLoseTarget(dfloat3 attackerPosition, dfloat3 targetPosition)
        {
            dfloat distance = dmath.distance(attackerPosition, targetPosition);
            return distance > targetLossRange;
        }
        
        /// <summary>
        /// Check if enough time has passed to retarget
        /// </summary>
        public bool CanRetarget(dfloat currentTime)
        {
            return (currentTime - targetAcquiredTime) >= retargetDelay;
        }
        
        /// <summary>
        /// Check if enough time has passed since losing target to acquire new one
        /// </summary>
        public bool CanAcquireNewTarget(dfloat currentTime)
        {
            return (currentTime - targetLostTime) >= targetLostDelay;
        }
        
        /// <summary>
        /// Set a new target
        /// </summary>
        public void SetTarget(Entity newTarget, dfloat3 targetPosition, dfloat currentTime)
        {
            currentTarget = newTarget;
            lastTargetPosition = targetPosition;
            targetAcquiredTime = currentTime;
        }
        
        /// <summary>
        /// Clear the current target
        /// </summary>
        public void ClearTarget(dfloat currentTime)
        {
            currentTarget = Entity.Null;
            targetLostTime = currentTime;
        }
        
        /// <summary>
        /// Update predicted target position based on target velocity
        /// </summary>
        public void UpdatePrediction(dfloat3 targetPosition, dfloat3 targetVelocity)
        {
            if (usePrediction)
            {
                predictedPosition = targetPosition + targetVelocity * predictionTime;
            }
            else
            {
                predictedPosition = targetPosition;
            }
        }
        
        /// <summary>
        /// Get the position to aim at (current or predicted)
        /// </summary>
        public dfloat3 GetAimPosition()
        {
            return usePrediction ? predictedPosition : lastTargetPosition;
        }
        
        /// <summary>
        /// Create default targeting data
        /// </summary>
        public static TargetingData CreateDefault()
        {
            return new TargetingData
            {
                currentTarget = Entity.Null,
                strategy = TargetingStrategy.Nearest,
                targetingRange = (dfloat)5,
                targetLossRange = (dfloat)6,
                retargetDelay = (dfloat)0.5f,
                targetLostDelay = (dfloat)0.1f,
                canTargetAir = true,
                canTargetGround = true,
                canTargetStructures = false,
                maxSimultaneousTargets = 1,
                targetLayerMask = 0xFFFFFFFF, // All layers by default
                usePrediction = false,
                predictionTime = (dfloat)0.5f
            };
        }
    }
}
