using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Core
{
    public struct DamageAccumulator : IComponentData
    {
        public dfloat Value;
    }
    
    public struct AttackTarget : IComponentData
    {
        public Entity Target;
    }
    
    public struct AttackCooldown : IComponentData
    {
        public int StartTick;
        public int EndTick;
    }
    
    public struct AttackOrigin : IComponentData
    {
        public dfloat3 Position;
    }
    
    public struct Cone : IComponentData
    {
        public dfloat Angle;
        public dfloat Range;
    }
    
    public struct Pierce : IComponentData
    {
        public int Value;
    }
    
    public struct AreaOfEffect : IComponentData
    {
        public dfloat Radius;
    }
    
    /// <summary>
    /// Core component that defines attack parameters for a unit
    /// Contains all data needed to execute attacks including damage, range, cooldown, and pattern
    /// </summary>
    public struct AttackData : IComponentData
    {
        // Basic attack parameters
        public dfloat baseDamage;           // Base damage before modifiers
        public dfloat attackRange;          // Maximum attack range
        public dfloat attackCooldown;       // Time between attacks
        public dfloat lastAttackTime;       // When the last attack occurred
        
        // Attack pattern configuration
        public AttackPattern attackPattern; // Type of attack pattern to use
        public ProjectileType projectileType; // Type of projectile if applicable
        public DamageType damageType;       // Type of damage dealt
        
        // Pattern-specific parameters
        public dfloat aoeRadius;            // Radius for AOE attacks
        public dfloat coneAngle;            // Angle for cone attacks (in radians)
        public dfloat coneRange;            // Range for cone attacks
        public int maxTargets;              // Maximum number of targets to hit
        public int pierceCount;             // Number of targets projectile can pierce
        public int chainCount;              // Number of chain jumps
        public dfloat chainRange;           // Range for chain jumps
        
        // Multi-projectile parameters
        public int projectileCount;         // Number of projectiles for multi-shot
        public dfloat spreadAngle;          // Spread angle for multi-projectiles
        
        // Projectile parameters
        public dfloat projectileSpeed;      // Speed of projectiles
        public dfloat projectileLifetime;   // How long projectiles live
        public dfloat homingStrength;       // Homing turn rate for homing projectiles
        public dfloat arcHeight;            // Arc height for arcing projectiles
        
        // Special attack properties
        public dfloat criticalChance;       // Chance for critical hits (0-1)
        public dfloat criticalMultiplier;   // Damage multiplier for crits
        public dfloat armorPenetration;     // Amount of armor to ignore (0-1)
        public dfloat lifeSteal;            // Percentage of damage healed (0-1)
        
        // Status effect parameters
        public dfloat stunDuration;         // Duration of stun effect
        public dfloat slowAmount;           // Amount of slow effect (0-1)
        public dfloat slowDuration;         // Duration of slow effect
        public dfloat dotDamage;            // Damage per tick for DOT
        public dfloat dotDuration;          // Duration of DOT effect
        public dfloat dotTickRate;          // Ticks per second for DOT
        
        // Attack state flags
        public CombatState combatState;     // Current combat state
        public byte attackFlags;            // Additional attack behavior flags
        
        /// <summary>
        /// Check if the unit can attack right now
        /// </summary>
        public bool CanAttack(dfloat currentTime)
        {
            // Check if not on cooldown
            bool cooldownReady = (currentTime - lastAttackTime) >= attackCooldown;
            
            // Check combat state flags
            bool notStunned = (combatState & CombatState.Stunned) == 0;
            bool notDisarmed = (combatState & CombatState.Disarmed) == 0;
            bool notChanneling = (combatState & CombatState.Channeling) == 0;
            
            return cooldownReady && notStunned && notDisarmed && notChanneling;
        }
        
        /// <summary>
        /// Start an attack, updating the last attack time and state
        /// </summary>
        public void StartAttack(dfloat currentTime)
        {
            lastAttackTime = currentTime;
            combatState |= CombatState.Attacking | CombatState.OnCooldown;
        }
        
        /// <summary>
        /// Get the effective damage after applying modifiers
        /// </summary>
        public dfloat GetEffectiveDamage(bool isCritical = false)
        {
            dfloat damage = baseDamage;
            
            if (isCritical)
            {
                damage *= criticalMultiplier;
            }
            
            return damage;
        }
        
        /// <summary>
        /// Check if this attack should be a critical hit
        /// </summary>
        public bool RollCritical(uint randomSeed)
        {
            // Use deterministic random based on seed
            var random = new Unity.Mathematics.Random(randomSeed);
            return random.NextFloat() < (float)criticalChance;
        }
        
        /// <summary>
        /// Create default attack data for a unit
        /// </summary>
        public static AttackData CreateDefault()
        {
            return new AttackData
            {
                baseDamage = (dfloat)10,
                attackRange = (dfloat)3,
                attackCooldown = (dfloat)1,
                attackPattern = AttackPattern.SingleTarget,
                projectileType = ProjectileType.Instant,
                damageType = DamageType.Physical,
                maxTargets = 1,
                projectileSpeed = (dfloat)10,
                projectileLifetime = (dfloat)5,
                criticalChance = (dfloat)0.05f,
                criticalMultiplier = (dfloat)2,
                combatState = CombatState.None
            };
        }
    }
}
