using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using UnityEditor;
using UnityEngine;

namespace Avalon.Simulation.Combat.Debugging
{
    /// <summary>
    /// MonoBehaviour component for debugging and visualizing combat system
    /// Provides real-time monitoring and visualization of combat states, ranges, and interactions
    /// </summary>
    public class CombatDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] public bool enableDebugging = true;
        [SerializeField] private bool showAttackRanges = true;
        [SerializeField] private bool showTargetingLines = true;
        [SerializeField] private bool showProjectilePaths = true;
        [SerializeField] private bool showDamageNumbers = true;
        [SerializeField] private bool showCombatStates = true;
        [SerializeField] private bool showEffectTimers = true;
        
        [Header("Visualization Colors")]
        [SerializeField] private Color attackRangeColor = Color.red;
        [SerializeField] private Color targetingLineColor = Color.yellow;
        [SerializeField] private Color projectilePathColor = Color.cyan;
        [SerializeField] private Color criticalHitColor = Color.orange;
        [SerializeField] private Color healingColor = Color.green;
        
        [Header("Debug Filters")]
        [SerializeField] private bool debugSelectedUnitOnly = false;
        [SerializeField] private GameObject selectedUnit;
        [SerializeField] private LayerMask debugLayerMask = -1;
        
        [Header("Performance")]
        [SerializeField] private int maxDebugEntities = 100;
        [SerializeField] private float debugUpdateInterval = 0.1f;
        
        private EntityManager entityManager;
        private EntityQuery combatUnitsQuery;
        private EntityQuery projectileQuery;
        private float lastUpdateTime;
        
        // Debug data storage
        private NativeList<DebugCombatUnit> debugUnits;
        private NativeList<DebugProjectile> debugProjectiles;
        private bool isInitialized = false;
        
        private void Start()
        {
            InitializeDebugger();
        }
        
        private void Update()
        {
            if (!enableDebugging || !isInitialized)
                return;
                
            if (Time.time - lastUpdateTime >= debugUpdateInterval)
            {
                UpdateDebugData();
                lastUpdateTime = Time.time;
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!enableDebugging || !isInitialized)
                return;
                
            DrawCombatDebugInfo();
        }
        
        private void OnGUI()
        {
            if (!enableDebugging || !isInitialized)
                return;
                
            DrawDebugUI();
        }
        
        private void InitializeDebugger()
        {
            var world = World.DefaultGameObjectInjectionWorld;
            if (world == null)
                return;
                
            entityManager = world.EntityManager;
            
            // Create entity queries
            combatUnitsQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<AttackData>(),
                ComponentType.ReadOnly<TargetingData>(),
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.Exclude<Disabled>()
            );
            
            projectileQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<ProjectileData>(),
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.Exclude<Disabled>()
            );
            
            // Initialize debug data containers
            debugUnits = new NativeList<DebugCombatUnit>(maxDebugEntities, Allocator.Persistent);
            debugProjectiles = new NativeList<DebugProjectile>(maxDebugEntities, Allocator.Persistent);
            
            isInitialized = true;
        }
        
        private void UpdateDebugData()
        {
            if (!entityManager.IsQueryValid(combatUnitsQuery) || !entityManager.IsQueryValid(projectileQuery))
                return;
                
            // Clear previous debug data
            debugUnits.Clear();
            debugProjectiles.Clear();
            
            // Update combat units debug data
            UpdateCombatUnitsDebugData();
            
            // Update projectiles debug data
            UpdateProjectilesDebugData();
        }
        
        private void UpdateCombatUnitsDebugData()
        {
            var entities = combatUnitsQuery.ToEntityArray(Allocator.Temp);
            var attackDataArray = combatUnitsQuery.ToComponentDataArray<AttackData>(Allocator.Temp);
            var targetingDataArray = combatUnitsQuery.ToComponentDataArray<TargetingData>(Allocator.Temp);
            var transformArray = combatUnitsQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int count = Mathf.Min(entities.Length, maxDebugEntities);
            
            for (int i = 0; i < count; i++)
            {
                var debugUnit = new DebugCombatUnit
                {
                    entity = entities[i],
                    position = transformArray[i].position,
                    attackData = attackDataArray[i],
                    targetingData = targetingDataArray[i],
                    hasTarget = targetingDataArray[i].HasValidTarget(),
                    targetPosition = targetingDataArray[i].lastTargetPosition,
                    combatState = attackDataArray[i].combatState
                };
                
                // Get additional debug info
                if (entityManager.HasComponent<UnitStats>(entities[i]))
                {
                    var unitStats = entityManager.GetComponentData<UnitStats>(entities[i]);
                    debugUnit.currentHealth = unitStats.currentHealth;
                    debugUnit.maxHealth = unitStats.maxHealth;
                }
                
                debugUnits.Add(debugUnit);
            }
            
            entities.Dispose();
            attackDataArray.Dispose();
            targetingDataArray.Dispose();
            transformArray.Dispose();
        }
        
        private void UpdateProjectilesDebugData()
        {
            var entities = projectileQuery.ToEntityArray(Allocator.Temp);
            var projectileDataArray = projectileQuery.ToComponentDataArray<ProjectileData>(Allocator.Temp);
            var transformArray = projectileQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int count = Mathf.Min(entities.Length, maxDebugEntities);
            
            for (int i = 0; i < count; i++)
            {
                var debugProjectile = new DebugProjectile
                {
                    entity = entities[i],
                    position = transformArray[i].position,
                    projectileData = projectileDataArray[i],
                    velocity = projectileDataArray[i].velocity,
                    targetPosition = projectileDataArray[i].targetPosition,
                    hasTarget = projectileDataArray[i].targetEntity != Entity.Null
                };
                
                debugProjectiles.Add(debugProjectile);
            }
            
            entities.Dispose();
            projectileDataArray.Dispose();
            transformArray.Dispose();
        }
        
        private void DrawCombatDebugInfo()
        {
            // Draw attack ranges
            if (showAttackRanges)
            {
                DrawAttackRanges();
            }
            
            // Draw targeting lines
            if (showTargetingLines)
            {
                DrawTargetingLines();
            }
            
            // Draw projectile paths
            if (showProjectilePaths)
            {
                DrawProjectilePaths();
            }
        }
        
        private void DrawAttackRanges()
        {
            Gizmos.color = attackRangeColor;
            
            for (int i = 0; i < debugUnits.Length; i++)
            {
                var unit = debugUnits[i];
                Vector3 position = new Vector3((float)unit.position.x, (float)unit.position.y, (float)unit.position.z);
                float range = (float)unit.attackData.attackRange;
                
                // Draw attack range circle
                DrawWireCircle(position, range);
                
                // Draw cone for cone attacks
                if (unit.attackData.attackPattern == AttackPattern.Cone)
                {
                    DrawConeRange(position, unit);
                }
                
                // Draw AOE radius for AOE attacks
                if (unit.attackData.aoeRadius > dfloat.Zero)
                {
                    Gizmos.color = Color.Lerp(attackRangeColor, Color.white, 0.5f);
                    DrawWireCircle(position, (float)unit.attackData.aoeRadius);
                }
            }
        }
        
        private void DrawTargetingLines()
        {
            Gizmos.color = targetingLineColor;
            
            for (int i = 0; i < debugUnits.Length; i++)
            {
                var unit = debugUnits[i];
                if (unit.hasTarget)
                {
                    Vector3 from = new Vector3((float)unit.position.x, (float)unit.position.y, (float)unit.position.z);
                    Vector3 to = new Vector3((float)unit.targetPosition.x, (float)unit.targetPosition.y, (float)unit.targetPosition.z);
                    
                    Gizmos.DrawLine(from, to);
                    
                    // Draw target indicator
                    Gizmos.DrawWireSphere(to, 0.2f);
                }
            }
        }
        
        private void DrawProjectilePaths()
        {
            Gizmos.color = projectilePathColor;
            
            for (int i = 0; i < debugProjectiles.Length; i++)
            {
                var projectile = debugProjectiles[i];
                Vector3 position = new Vector3((float)projectile.position.x, (float)projectile.position.y, (float)projectile.position.z);
                Vector3 velocity = new Vector3((float)projectile.velocity.x, (float)projectile.velocity.y, (float)projectile.velocity.z);
                
                // Draw current position
                Gizmos.DrawWireSphere(position, 0.1f);
                
                // Draw velocity vector
                Gizmos.DrawRay(position, velocity.normalized * 0.5f);
                
                // Draw predicted path for homing projectiles
                if (projectile.projectileData.projectileType == ProjectileType.Homing && projectile.hasTarget)
                {
                    Vector3 targetPos = new Vector3((float)projectile.targetPosition.x, (float)projectile.targetPosition.y, (float)projectile.targetPosition.z);
                    Gizmos.color = Color.Lerp(projectilePathColor, Color.white, 0.5f);
                    Gizmos.DrawLine(position, targetPos);
                }
            }
        }
        
        private void DrawConeRange(Vector3 position, DebugCombatUnit unit)
        {
            float angle = (float)unit.attackData.coneAngle * Mathf.Rad2Deg;
            float range = (float)unit.attackData.coneRange;
            
            // Draw cone outline
            Vector3 forward = Vector3.forward; // Would need actual facing direction
            Vector3 left = Quaternion.Euler(0, -angle * 0.5f, 0) * forward * range;
            Vector3 right = Quaternion.Euler(0, angle * 0.5f, 0) * forward * range;
            
            Gizmos.DrawLine(position, position + left);
            Gizmos.DrawLine(position, position + right);
            Gizmos.DrawLine(position + left, position + right);
        }
        
        private void DrawWireCircle(Vector3 center, float radius)
        {
            int segments = 32;
            float angleStep = 360f / segments;
            
            Vector3 prevPoint = center + Vector3.forward * radius;
            for (int i = 1; i <= segments; i++)
            {
                float angle = i * angleStep * Mathf.Deg2Rad;
                Vector3 newPoint = center + new Vector3(Mathf.Sin(angle), 0, Mathf.Cos(angle)) * radius;
                Gizmos.DrawLine(prevPoint, newPoint);
                prevPoint = newPoint;
            }
        }
        
        private void DrawDebugUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Combat System Debug", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
            
            GUILayout.Label($"Combat Units: {debugUnits.Length}");
            GUILayout.Label($"Active Projectiles: {debugProjectiles.Length}");
            
            GUILayout.Space(10);
            
            // Combat state summary
            if (debugUnits.Length > 0)
            {
                int attacking = 0, onCooldown = 0, hasTarget = 0;
                
                for (int i = 0; i < debugUnits.Length; i++)
                {
                    var unit = debugUnits[i];
                    if ((unit.combatState & CombatState.Attacking) != 0) attacking++;
                    if ((unit.combatState & CombatState.OnCooldown) != 0) onCooldown++;
                    if (unit.hasTarget) hasTarget++;
                }
                
                GUILayout.Label($"Attacking: {attacking}");
                GUILayout.Label($"On Cooldown: {onCooldown}");
                GUILayout.Label($"Has Target: {hasTarget}");
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
        
        private void OnDestroy()
        {
            if (isInitialized)
            {
                if (debugUnits.IsCreated)
                    debugUnits.Dispose();
                if (debugProjectiles.IsCreated)
                    debugProjectiles.Dispose();
            }
        }
    }
    
    /// <summary>
    /// Debug data structure for combat units
    /// </summary>
    public struct DebugCombatUnit
    {
        public Entity entity;
        public dfloat3 position;
        public AttackData attackData;
        public TargetingData targetingData;
        public bool hasTarget;
        public dfloat3 targetPosition;
        public CombatState combatState;
        public dfloat currentHealth;
        public dfloat maxHealth;
    }
    
    /// <summary>
    /// Debug data structure for projectiles
    /// </summary>
    public struct DebugProjectile
    {
        public Entity entity;
        public dfloat3 position;
        public ProjectileData projectileData;
        public dfloat3 velocity;
        public dfloat3 targetPosition;
        public bool hasTarget;
    }
}
