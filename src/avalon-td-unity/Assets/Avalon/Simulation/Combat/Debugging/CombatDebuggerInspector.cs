using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Combat.Effects;
using Avalon.Simulation.Effects;
using FlowField;
using Unity.Entities;
using UnityEngine;
using UnityEditor;

namespace Avalon.Simulation.Combat.Debugging
{
#if UNITY_EDITOR
    /// <summary>
    /// Custom inspector for the CombatDebugger component
    /// Provides enhanced debugging interface and real-time combat system monitoring
    /// </summary>
    [CustomEditor(typeof(CombatDebugger))]
    public class CombatDebuggerInspector : Editor
    {
        private CombatDebugger debugger;
        private bool showTestControls = false;
        private bool showPerformanceStats = false;
        private bool showDetailedStats = false;
        
        // Test parameters
        private float testDamage = 50f;
        private AttackPattern testAttackPattern = AttackPattern.SingleTarget;
        private DamageType testDamageType = DamageType.Physical;
        
        private void OnEnable()
        {
            debugger = (CombatDebugger)target;
        }
        
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Combat System Debug Tools", EditorStyles.boldLabel);
            
            DrawDebugControls();
            DrawTestControls();
            DrawPerformanceStats();
            DrawDetailedStats();
            
            // Repaint frequently when debugging is enabled
            if (debugger.enableDebugging && Application.isPlaying)
            {
                EditorUtility.SetDirty(target);
                Repaint();
            }
        }
        
        private void DrawDebugControls()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Debug Controls", EditorStyles.boldLabel);
            
            if (GUILayout.Button("Clear All Targets"))
            {
                ClearAllTargets();
            }
            
            if (GUILayout.Button("Reset All Cooldowns"))
            {
                ResetAllCooldowns();
            }
            
            if (GUILayout.Button("Heal All Units"))
            {
                HealAllUnits();
            }
            
            if (GUILayout.Button("Stun All Units"))
            {
                StunAllUnits();
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawTestControls()
        {
            EditorGUILayout.BeginVertical("box");
            showTestControls = EditorGUILayout.Foldout(showTestControls, "Test Controls");
            
            if (showTestControls)
            {
                testDamage = EditorGUILayout.FloatField("Test Damage", testDamage);
                testAttackPattern = (AttackPattern)EditorGUILayout.EnumPopup("Test Attack Pattern", testAttackPattern);
                testDamageType = (DamageType)EditorGUILayout.EnumPopup("Test Damage Type", testDamageType);
                
                EditorGUILayout.Space();
                
                if (GUILayout.Button("Apply Test Damage to All"))
                {
                    ApplyTestDamageToAll();
                }
                
                if (GUILayout.Button("Create Test Projectile"))
                {
                    CreateTestProjectile();
                }
                
                if (GUILayout.Button("Trigger Test Effects"))
                {
                    TriggerTestEffects();
                }
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawPerformanceStats()
        {
            EditorGUILayout.BeginVertical("box");
            showPerformanceStats = EditorGUILayout.Foldout(showPerformanceStats, "Performance Stats");
            
            if (showPerformanceStats && Application.isPlaying)
            {
                var world = World.DefaultGameObjectInjectionWorld;
                if (world != null)
                {
                    var entityManager = world.EntityManager;
                    
                    // Count entities
                    var combatUnitsQuery = entityManager.CreateEntityQuery(typeof(AttackData), typeof(TargetingData));
                    var projectileQuery = entityManager.CreateEntityQuery(typeof(ProjectileData));
                    var effectQuery = entityManager.CreateEntityQuery(typeof(DynamicBuffer<GameEffect>));
                    
                    EditorGUILayout.LabelField($"Combat Units: {combatUnitsQuery.CalculateEntityCount()}");
                    EditorGUILayout.LabelField($"Active Projectiles: {projectileQuery.CalculateEntityCount()}");
                    EditorGUILayout.LabelField($"Units with Effects: {effectQuery.CalculateEntityCount()}");
                    
                    // Memory usage
                    EditorGUILayout.LabelField($"Frame Rate: {1f / Time.unscaledDeltaTime:F1} FPS");
                    EditorGUILayout.LabelField($"Delta Time: {Time.unscaledDeltaTime * 1000f:F2} ms");
                }
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawDetailedStats()
        {
            EditorGUILayout.BeginVertical("box");
            showDetailedStats = EditorGUILayout.Foldout(showDetailedStats, "Detailed Combat Stats");
            
            if (showDetailedStats && Application.isPlaying)
            {
                var world = World.DefaultGameObjectInjectionWorld;
                if (world != null)
                {
                    var entityManager = world.EntityManager;
                    DrawCombatStateBreakdown(entityManager);
                    DrawAttackPatternBreakdown(entityManager);
                    DrawDamageTypeBreakdown(entityManager);
                }
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawCombatStateBreakdown(EntityManager entityManager)
        {
            EditorGUILayout.LabelField("Combat States:", EditorStyles.boldLabel);
            
            var query = entityManager.CreateEntityQuery(typeof(AttackData));
            var attackDataArray = query.ToComponentDataArray<AttackData>(Unity.Collections.Allocator.Temp);
            
            int attacking = 0, onCooldown = 0, stunned = 0, disarmed = 0, hasTarget = 0;
            
            foreach (var attackData in attackDataArray)
            {
                if ((attackData.combatState & CombatState.Attacking) != 0) attacking++;
                if ((attackData.combatState & CombatState.OnCooldown) != 0) onCooldown++;
                if ((attackData.combatState & CombatState.Stunned) != 0) stunned++;
                if ((attackData.combatState & CombatState.Disarmed) != 0) disarmed++;
                if ((attackData.combatState & CombatState.HasTarget) != 0) hasTarget++;
            }
            
            EditorGUILayout.LabelField($"  Attacking: {attacking}");
            EditorGUILayout.LabelField($"  On Cooldown: {onCooldown}");
            EditorGUILayout.LabelField($"  Stunned: {stunned}");
            EditorGUILayout.LabelField($"  Disarmed: {disarmed}");
            EditorGUILayout.LabelField($"  Has Target: {hasTarget}");
            
            attackDataArray.Dispose();
        }
        
        private void DrawAttackPatternBreakdown(EntityManager entityManager)
        {
            EditorGUILayout.LabelField("Attack Patterns:", EditorStyles.boldLabel);
            
            var query = entityManager.CreateEntityQuery(typeof(AttackData));
            var attackDataArray = query.ToComponentDataArray<AttackData>(Unity.Collections.Allocator.Temp);
            
            var patternCounts = new System.Collections.Generic.Dictionary<AttackPattern, int>();
            
            foreach (var attackData in attackDataArray)
            {
                if (patternCounts.ContainsKey(attackData.attackPattern))
                    patternCounts[attackData.attackPattern]++;
                else
                    patternCounts[attackData.attackPattern] = 1;
            }
            
            foreach (var kvp in patternCounts)
            {
                EditorGUILayout.LabelField($"  {kvp.Key}: {kvp.Value}");
            }
            
            attackDataArray.Dispose();
        }
        
        private void DrawDamageTypeBreakdown(EntityManager entityManager)
        {
            EditorGUILayout.LabelField("Damage Types:", EditorStyles.boldLabel);
            
            var query = entityManager.CreateEntityQuery(typeof(AttackData));
            var attackDataArray = query.ToComponentDataArray<AttackData>(Unity.Collections.Allocator.Temp);
            
            var damageTypeCounts = new System.Collections.Generic.Dictionary<DamageType, int>();
            
            foreach (var attackData in attackDataArray)
            {
                if (damageTypeCounts.ContainsKey(attackData.damageType))
                    damageTypeCounts[attackData.damageType]++;
                else
                    damageTypeCounts[attackData.damageType] = 1;
            }
            
            foreach (var kvp in damageTypeCounts)
            {
                EditorGUILayout.LabelField($"  {kvp.Key}: {kvp.Value}");
            }
            
            attackDataArray.Dispose();
        }
        
        private void ClearAllTargets()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                var query = entityManager.CreateEntityQuery(typeof(TargetingData));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                
                foreach (var entity in entities)
                {
                    var targetingData = entityManager.GetComponentData<TargetingData>(entity);
                    targetingData.ClearTarget((Unity.Deterministic.Mathematics.dfloat)Time.time);
                    entityManager.SetComponentData(entity, targetingData);
                }
                
                entities.Dispose();
                Debug.Log("Cleared all targets");
            }
        }
        
        private void ResetAllCooldowns()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                var query = entityManager.CreateEntityQuery(typeof(AttackData));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                
                foreach (var entity in entities)
                {
                    var attackData = entityManager.GetComponentData<AttackData>(entity);
                    attackData.lastAttackTime = (Unity.Deterministic.Mathematics.dfloat)0;
                    attackData.combatState &= ~(CombatState.OnCooldown | CombatState.Attacking);
                    entityManager.SetComponentData(entity, attackData);
                }
                
                entities.Dispose();
                Debug.Log("Reset all cooldowns");
            }
        }
        
        private void HealAllUnits()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                var query = entityManager.CreateEntityQuery(typeof(UnitStats));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                
                foreach (var entity in entities)
                {
                    var unitStats = entityManager.GetComponentData<UnitStats>(entity);
                    unitStats.currentHealth = unitStats.maxHealth;
                    entityManager.SetComponentData(entity, unitStats);
                }
                
                entities.Dispose();
                Debug.Log("Healed all units");
            }
        }
        
        private void StunAllUnits()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                
                // Apply stun effect to all units with combat capabilities
                var query = entityManager.CreateEntityQuery(typeof(AttackData));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                
                foreach (var entity in entities)
                {
                    CombatEffectUtils.ApplyStun(entityManager, entity, (Unity.Deterministic.Mathematics.dfloat)2.0f, 999);
                }
                
                entities.Dispose();
                Debug.Log("Applied stun to all combat units");
            }
        }
        
        private void ApplyTestDamageToAll()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                var query = entityManager.CreateEntityQuery(typeof(UnitStats), typeof(SimulationTransform));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                var transforms = query.ToComponentDataArray<SimulationTransform>(Unity.Collections.Allocator.Temp);
                
                for (int i = 0; i < entities.Length; i++)
                {
                    CombatComponentUtils.ApplyDamage(entityManager, Entity.Null, entities[i], 
                        (Unity.Deterministic.Mathematics.dfloat)testDamage, transforms[i].position, testDamageType);
                }
                
                entities.Dispose();
                transforms.Dispose();
                Debug.Log($"Applied {testDamage} {testDamageType} damage to all units");
            }
        }
        
        private void CreateTestProjectile()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                
                // Create a test projectile at camera position
                var cameraPos = SceneView.lastActiveSceneView?.camera?.transform.position ?? Vector3.zero;
                var startPos = new Unity.Deterministic.Mathematics.dfloat3(
                    (Unity.Deterministic.Mathematics.dfloat)cameraPos.x,
                    (Unity.Deterministic.Mathematics.dfloat)cameraPos.y,
                    (Unity.Deterministic.Mathematics.dfloat)cameraPos.z
                );
                var targetPos = startPos + new Unity.Deterministic.Mathematics.dfloat3(
                    (Unity.Deterministic.Mathematics.dfloat)5, 
                    (Unity.Deterministic.Mathematics.dfloat)0, 
                    (Unity.Deterministic.Mathematics.dfloat)0
                );
                
                CombatComponentUtils.CreateProjectile(entityManager, Entity.Null, startPos, targetPos, 
                    (Unity.Deterministic.Mathematics.dfloat)testDamage, ProjectileType.Linear);
                
                Debug.Log("Created test projectile");
            }
        }
        
        private void TriggerTestEffects()
        {
            if (!Application.isPlaying) return;
            
            var world = World.DefaultGameObjectInjectionWorld;
            if (world != null)
            {
                var entityManager = world.EntityManager;
                var query = entityManager.CreateEntityQuery(typeof(AttackData));
                var entities = query.ToEntityArray(Unity.Collections.Allocator.Temp);
                
                foreach (var entity in entities)
                {
                    // Apply various test effects
                    CombatEffectUtils.ApplySlow(entityManager, entity, 
                        (Unity.Deterministic.Mathematics.dfloat)0.5f, 
                        (Unity.Deterministic.Mathematics.dfloat)3.0f, 999);
                    
                    CombatEffectUtils.ApplyDamageOverTime(entityManager, entity, 
                        (Unity.Deterministic.Mathematics.dfloat)5.0f, 
                        (Unity.Deterministic.Mathematics.dfloat)5.0f, 
                        (Unity.Deterministic.Mathematics.dfloat)1.0f, DamageType.Poison, 999);
                }
                
                entities.Dispose();
                Debug.Log("Applied test effects to all combat units");
            }
        }
    }
#endif
}
