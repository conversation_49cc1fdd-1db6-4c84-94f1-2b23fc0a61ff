using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Effects;
using Unity.Collections;
using Unity.Entities;
using Unity.Profiling;
using UnityEditor;
using UnityEngine;

namespace Avalon.Simulation.Combat.Debugging
{
    /// <summary>
    /// Performance profiler for the combat system
    /// Tracks system performance, entity counts, and memory usage
    /// </summary>
    public class CombatPerformanceProfiler : MonoBehaviour
    {
        [Header("Profiling Settings")]
        [SerializeField] private bool enableProfiling = true;
        [SerializeField] private float profilingInterval = 1f;
        [SerializeField] private bool logToConsole = false;
        [SerializeField] private bool showOnScreenStats = true;
        
        [Header("Performance Thresholds")]
        [SerializeField] private float warningFrameTime = 16.67f; // 60 FPS
        [SerializeField] private int warningEntityCount = 1000;
        [SerializeField] private float warningMemoryUsage = 100f; // MB
        
        // Profiler markers
        private static readonly ProfilerMarker s_TargetSelectionMarker = new ProfilerMarker("Combat.TargetSelection");
        private static readonly ProfilerMarker s_AttackProcessingMarker = new ProfilerMarker("Combat.AttackProcessing");
        private static readonly ProfilerMarker s_ProjectileSystemMarker = new ProfilerMarker("Combat.ProjectileSystem");
        private static readonly ProfilerMarker s_DamageApplicationMarker = new ProfilerMarker("Combat.DamageApplication");
        private static readonly ProfilerMarker s_EffectsProcessingMarker = new ProfilerMarker("Combat.EffectsProcessing");
        
        // Performance data
        private CombatPerformanceData currentData;
        private CombatPerformanceData averageData;
        private float lastProfilingTime;
        private int frameCount;
        
        // Entity queries
        private EntityManager entityManager;
        private EntityQuery combatUnitsQuery;
        private EntityQuery projectileQuery;
        private EntityQuery damageQuery;
        private EntityQuery effectQuery;
        
        private void Start()
        {
            InitializeProfiler();
        }
        
        private void Update()
        {
            if (!enableProfiling)
                return;
                
            UpdatePerformanceData();
            
            if (Time.time - lastProfilingTime >= profilingInterval)
            {
                ProcessPerformanceData();
                lastProfilingTime = Time.time;
            }
        }
        
        private void OnGUI()
        {
            if (!enableProfiling || !showOnScreenStats)
                return;
                
            DrawPerformanceStats();
        }
        
        private void InitializeProfiler()
        {
            var world = World.DefaultGameObjectInjectionWorld;
            if (world == null)
                return;
                
            entityManager = world.EntityManager;
            
            // Create entity queries
            combatUnitsQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<AttackData>(),
                ComponentType.ReadOnly<TargetingData>()
            );
            
            projectileQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<ProjectileData>()
            );
            
            damageQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<DamageData>()
            );
            
            effectQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<DynamicBuffer<GameEffect>>()
            );
            
            // Initialize performance data
            currentData = new CombatPerformanceData();
            averageData = new CombatPerformanceData();
        }
        
        private void UpdatePerformanceData()
        {
            if (combatUnitsQuery.IsEmpty)
                return;
                
            // Update entity counts
            currentData.combatUnitCount = combatUnitsQuery.CalculateEntityCount();
            currentData.projectileCount = projectileQuery.CalculateEntityCount();
            currentData.damageEventCount = damageQuery.CalculateEntityCount();
            currentData.unitsWithEffectsCount = effectQuery.CalculateEntityCount();
            
            // Update timing data
            currentData.frameTime = Time.unscaledDeltaTime * 1000f; // Convert to milliseconds
            currentData.fps = 1f / Time.unscaledDeltaTime;
            
            // Update memory usage (simplified)
            currentData.memoryUsage = UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong() / (1024f * 1024f); // MB
            
            // Calculate combat system load
            currentData.combatSystemLoad = CalculateCombatSystemLoad();
            
            frameCount++;
        }
        
        private void ProcessPerformanceData()
        {
            // Calculate averages
            if (frameCount > 0)
            {
                averageData.combatUnitCount = currentData.combatUnitCount;
                averageData.projectileCount = currentData.projectileCount;
                averageData.damageEventCount = currentData.damageEventCount;
                averageData.unitsWithEffectsCount = currentData.unitsWithEffectsCount;
                averageData.frameTime = currentData.frameTime;
                averageData.fps = currentData.fps;
                averageData.memoryUsage = currentData.memoryUsage;
                averageData.combatSystemLoad = currentData.combatSystemLoad;
            }
            
            // Check for performance warnings
            CheckPerformanceWarnings();
            
            // Log to console if enabled
            if (logToConsole)
            {
                LogPerformanceData();
            }
            
            frameCount = 0;
        }
        
        private float CalculateCombatSystemLoad()
        {
            // Simplified combat system load calculation
            float load = 0f;
            
            // Base load from entity counts
            load += currentData.combatUnitCount * 0.01f;
            load += currentData.projectileCount * 0.02f;
            load += currentData.damageEventCount * 0.05f;
            load += currentData.unitsWithEffectsCount * 0.015f;
            
            // Frame time impact
            if (currentData.frameTime > 16.67f) // 60 FPS threshold
            {
                load += (currentData.frameTime - 16.67f) * 0.1f;
            }
            
            return Mathf.Clamp01(load);
        }
        
        private void CheckPerformanceWarnings()
        {
            bool hasWarnings = false;
            
            if (currentData.frameTime > warningFrameTime)
            {
                Debug.LogWarning($"Combat System: High frame time detected: {currentData.frameTime:F2}ms");
                hasWarnings = true;
            }
            
            if (currentData.combatUnitCount + currentData.projectileCount > warningEntityCount)
            {
                Debug.LogWarning($"Combat System: High entity count: {currentData.combatUnitCount + currentData.projectileCount}");
                hasWarnings = true;
            }
            
            if (currentData.memoryUsage > warningMemoryUsage)
            {
                Debug.LogWarning($"Combat System: High memory usage: {currentData.memoryUsage:F1}MB");
                hasWarnings = true;
            }
            
            if (currentData.combatSystemLoad > 0.8f)
            {
                Debug.LogWarning($"Combat System: High system load: {currentData.combatSystemLoad:P1}");
                hasWarnings = true;
            }
        }
        
        private void LogPerformanceData()
        {
            Debug.Log($"Combat Performance - Units: {currentData.combatUnitCount}, " +
                     $"Projectiles: {currentData.projectileCount}, " +
                     $"Frame Time: {currentData.frameTime:F2}ms, " +
                     $"FPS: {currentData.fps:F1}, " +
                     $"Load: {currentData.combatSystemLoad:P1}");
        }
        
        private void DrawPerformanceStats()
        {
            GUILayout.BeginArea(new Rect(Screen.width - 320, 10, 310, 300));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Combat Performance", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
            
            // Entity counts
            GUILayout.Label($"Combat Units: {currentData.combatUnitCount}");
            GUILayout.Label($"Projectiles: {currentData.projectileCount}");
            GUILayout.Label($"Damage Events: {currentData.damageEventCount}");
            GUILayout.Label($"Units w/ Effects: {currentData.unitsWithEffectsCount}");
            
            GUILayout.Space(5);
            
            // Performance metrics
            Color originalColor = GUI.color;
            
            // Frame time with color coding
            GUI.color = currentData.frameTime > warningFrameTime ? Color.red : Color.white;
            GUILayout.Label($"Frame Time: {currentData.frameTime:F2}ms");
            
            GUI.color = currentData.fps < 60f ? Color.red : Color.white;
            GUILayout.Label($"FPS: {currentData.fps:F1}");
            
            GUI.color = originalColor;
            GUILayout.Label($"Memory: {currentData.memoryUsage:F1}MB");
            
            // System load bar
            GUILayout.Label("System Load:");
            Rect loadBarRect = GUILayoutUtility.GetRect(200, 20);
            GUI.color = Color.Lerp(Color.green, Color.red, currentData.combatSystemLoad);
            GUI.DrawTexture(new Rect(loadBarRect.x, loadBarRect.y, loadBarRect.width * currentData.combatSystemLoad, loadBarRect.height), Texture2D.whiteTexture);
            GUI.color = originalColor;
            GUI.Box(loadBarRect, "");
            GUILayout.Label($"{currentData.combatSystemLoad:P1}");
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
        
        // Static methods for profiling specific systems
        public static void BeginTargetSelectionProfiling()
        {
            s_TargetSelectionMarker.Begin();
        }
        
        public static void EndTargetSelectionProfiling()
        {
            s_TargetSelectionMarker.End();
        }
        
        public static void BeginAttackProcessingProfiling()
        {
            s_AttackProcessingMarker.Begin();
        }
        
        public static void EndAttackProcessingProfiling()
        {
            s_AttackProcessingMarker.End();
        }
        
        public static void BeginProjectileSystemProfiling()
        {
            s_ProjectileSystemMarker.Begin();
        }
        
        public static void EndProjectileSystemProfiling()
        {
            s_ProjectileSystemMarker.End();
        }
        
        public static void BeginDamageApplicationProfiling()
        {
            s_DamageApplicationMarker.Begin();
        }
        
        public static void EndDamageApplicationProfiling()
        {
            s_DamageApplicationMarker.End();
        }
        
        public static void BeginEffectsProcessingProfiling()
        {
            s_EffectsProcessingMarker.Begin();
        }
        
        public static void EndEffectsProcessingProfiling()
        {
            s_EffectsProcessingMarker.End();
        }
    }
    
    /// <summary>
    /// Structure containing combat system performance data
    /// </summary>
    [System.Serializable]
    public struct CombatPerformanceData
    {
        public int combatUnitCount;
        public int projectileCount;
        public int damageEventCount;
        public int unitsWithEffectsCount;
        public float frameTime;
        public float fps;
        public float memoryUsage;
        public float combatSystemLoad;
        
        public void Reset()
        {
            combatUnitCount = 0;
            projectileCount = 0;
            damageEventCount = 0;
            unitsWithEffectsCount = 0;
            frameTime = 0f;
            fps = 0f;
            memoryUsage = 0f;
            combatSystemLoad = 0f;
        }
    }
}
