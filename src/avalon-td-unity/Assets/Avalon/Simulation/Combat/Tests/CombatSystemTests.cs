// using NUnit.Framework;
// using Unity.Collections;
// using Unity.Deterministic.Mathematics;
// using Unity.Entities;
// using Unity.Entities.Tests;
// using FlowField.Effects;
//
// namespace FlowField.Combat.Tests
// {
//     /// <summary>
//     /// Comprehensive unit tests for the FlowField Combat System
//     /// Tests deterministic behavior, component interactions, and system integration
//     /// </summary>
//     [TestFixture]
//     public class CombatSystemTests : ECSTestsFixture
//     {
//         private EntityManager entityManager;
//         private Entity testAttacker;
//         private Entity testTarget;
//         private UnitStats attackerStats;
//         private UnitStats targetStats;
//         
//         [SetUp]
//         public override void Setup()
//         {
//             base.Setup();
//             entityManager = m_Manager;
//             
//             // Create test entities
//             SetupTestEntities();
//         }
//         
//         private void SetupTestEntities()
//         {
//             // Create attacker entity
//             testAttacker = entityManager.CreateEntity();
//             attackerStats = new UnitStats
//             {
//                 maxHealth = (dfloat)100,
//                 currentHealth = (dfloat)100,
//                 attackDamage = (dfloat)25,
//                 attackRange = (dfloat)5,
//                 attackCooldown = (dfloat)1,
//                 maxSpeed = (dfloat)3,
//                 radius = (dfloat)0.5f
//             };
//             
//             entityManager.AddComponentData(testAttacker, attackerStats);
//             entityManager.AddComponentData(testAttacker, new SimulationTransform
//             {
//                 position = new dfloat3(dfloat.Zero, dfloat.Zero, dfloat.Zero),
//                 rotation = dquaternion.identity,
//                 scale = dfloat.One
//             });
//             
//             // Create target entity
//             testTarget = entityManager.CreateEntity();
//             targetStats = new UnitStats
//             {
//                 maxHealth = (dfloat)80,
//                 currentHealth = (dfloat)80,
//                 attackDamage = (dfloat)15,
//                 attackRange = (dfloat)3,
//                 attackCooldown = (dfloat)1.5f,
//                 maxSpeed = (dfloat)2,
//                 radius = (dfloat)0.5f
//             };
//             
//             entityManager.AddComponentData(testTarget, targetStats);
//             entityManager.AddComponentData(testTarget, new SimulationTransform
//             {
//                 position = new dfloat3((dfloat)3, dfloat.Zero, dfloat.Zero),
//                 rotation = dquaternion.identity,
//                 scale = dfloat.One
//             });
//         }
//         
//         [Test]
//         public void AttackData_CreateDefault_HasValidValues()
//         {
//             // Arrange & Act
//             var attackData = AttackData.CreateDefault();
//             
//             // Assert
//             Assert.Greater((float)attackData.baseDamage, 0f, "Base damage should be greater than 0");
//             Assert.Greater((float)attackData.attackRange, 0f, "Attack range should be greater than 0");
//             Assert.Greater((float)attackData.attackCooldown, 0f, "Attack cooldown should be greater than 0");
//             Assert.AreEqual(AttackPattern.SingleTarget, attackData.attackPattern, "Default attack pattern should be SingleTarget");
//             Assert.AreEqual(ProjectileType.Instant, attackData.projectileType, "Default projectile type should be Instant");
//             Assert.AreEqual(CombatState.None, attackData.combatState, "Initial combat state should be None");
//         }
//         
//         [Test]
//         public void AttackData_CanAttack_ReturnsTrueWhenReady()
//         {
//             // Arrange
//             var attackData = AttackData.CreateDefault();
//             attackData.lastAttackTime = dfloat.Zero;
//             attackData.combatState = CombatState.None;
//             dfloat currentTime = (dfloat)2; // Well past cooldown
//             
//             // Act
//             bool canAttack = attackData.CanAttack(currentTime);
//             
//             // Assert
//             Assert.IsTrue(canAttack, "Should be able to attack when cooldown is ready and not stunned");
//         }
//         
//         [Test]
//         public void AttackData_CanAttack_ReturnsFalseWhenOnCooldown()
//         {
//             // Arrange
//             var attackData = AttackData.CreateDefault();
//             attackData.lastAttackTime = (dfloat)1;
//             attackData.attackCooldown = (dfloat)2;
//             dfloat currentTime = (dfloat)1.5f; // Still on cooldown
//             
//             // Act
//             bool canAttack = attackData.CanAttack(currentTime);
//             
//             // Assert
//             Assert.IsFalse(canAttack, "Should not be able to attack while on cooldown");
//         }
//         
//         [Test]
//         public void AttackData_CanAttack_ReturnsFalseWhenStunned()
//         {
//             // Arrange
//             var attackData = AttackData.CreateDefault();
//             attackData.lastAttackTime = dfloat.Zero;
//             attackData.combatState = CombatState.Stunned;
//             dfloat currentTime = (dfloat)2;
//             
//             // Act
//             bool canAttack = attackData.CanAttack(currentTime);
//             
//             // Assert
//             Assert.IsFalse(canAttack, "Should not be able to attack while stunned");
//         }
//         
//         [Test]
//         public void TargetingData_CreateDefault_HasValidValues()
//         {
//             // Arrange & Act
//             var targetingData = TargetingData.CreateDefault();
//             
//             // Assert
//             Assert.AreEqual(Entity.Null, targetingData.currentTarget, "Initial target should be null");
//             Assert.Greater((float)targetingData.targetingRange, 0f, "Targeting range should be greater than 0");
//             Assert.Greater((float)targetingData.targetLossRange, (float)targetingData.targetingRange, "Target loss range should be greater than targeting range");
//             Assert.AreEqual(TargetingStrategy.Nearest, targetingData.strategy, "Default targeting strategy should be Nearest");
//         }
//         
//         [Test]
//         public void TargetingData_IsTargetInRange_ReturnsTrueWhenInRange()
//         {
//             // Arrange
//             var targetingData = TargetingData.CreateDefault();
//             targetingData.targetingRange = (dfloat)5;
//             dfloat3 attackerPos = dfloat3.zero;
//             dfloat3 targetPos = new dfloat3((dfloat)3, dfloat.Zero, dfloat.Zero); // Distance = 3
//             
//             // Act
//             bool inRange = targetingData.IsTargetInRange(attackerPos, targetPos);
//             
//             // Assert
//             Assert.IsTrue(inRange, "Target should be in range when distance is less than targeting range");
//         }
//         
//         [Test]
//         public void TargetingData_IsTargetInRange_ReturnsFalseWhenOutOfRange()
//         {
//             // Arrange
//             var targetingData = TargetingData.CreateDefault();
//             targetingData.targetingRange = (dfloat)5;
//             dfloat3 attackerPos = dfloat3.zero;
//             dfloat3 targetPos = new dfloat3((dfloat)7, dfloat.Zero, dfloat.Zero); // Distance = 7
//             
//             // Act
//             bool inRange = targetingData.IsTargetInRange(attackerPos, targetPos);
//             
//             // Assert
//             Assert.IsFalse(inRange, "Target should not be in range when distance is greater than targeting range");
//         }
//         
//         [Test]
//         public void ProjectileData_CreateLinear_HasCorrectProperties()
//         {
//             // Arrange
//             Entity source = testAttacker;
//             dfloat3 start = dfloat3.zero;
//             dfloat3 target = new dfloat3((dfloat)5, dfloat.Zero, dfloat.Zero);
//             dfloat speed = (dfloat)10;
//             dfloat damage = (dfloat)25;
//             
//             // Act
//             var projectileData = ProjectileData.CreateLinear(source, start, target, speed, damage);
//             
//             // Assert
//             Assert.AreEqual(source, projectileData.sourceEntity, "Source entity should match");
//             Assert.AreEqual(target, projectileData.targetPosition, "Target position should match");
//             Assert.AreEqual(ProjectileType.Linear, projectileData.projectileType, "Projectile type should be Linear");
//             Assert.AreEqual(damage, projectileData.damage, "Damage should match");
//             Assert.Greater((float)dmath.length(projectileData.velocity), 0f, "Velocity should be non-zero");
//         }
//         
//         [Test]
//         public void ProjectileData_HasExpired_ReturnsTrueWhenExpired()
//         {
//             // Arrange
//             var projectileData = ProjectileData.CreateLinear(testAttacker, dfloat3.zero, 
//                 new dfloat3((dfloat)5, dfloat.Zero, dfloat.Zero), (dfloat)10, (dfloat)25);
//             projectileData.spawnTime = dfloat.Zero;
//             projectileData.lifetime = (dfloat)2;
//             dfloat currentTime = (dfloat)3; // Past lifetime
//             
//             // Act
//             bool hasExpired = projectileData.HasExpired(currentTime);
//             
//             // Assert
//             Assert.IsTrue(hasExpired, "Projectile should be expired when current time exceeds spawn time + lifetime");
//         }
//         
//         [Test]
//         public void DamageData_CreatePhysical_HasCorrectProperties()
//         {
//             // Arrange
//             Entity source = testAttacker;
//             Entity target = testTarget;
//             dfloat damage = (dfloat)50;
//             dfloat3 position = new dfloat3((dfloat)1, dfloat.Zero, dfloat.Zero);
//             
//             // Act
//             var damageData = DamageData.CreatePhysical(source, target, damage, position);
//             
//             // Assert
//             Assert.AreEqual(source, damageData.sourceEntity, "Source entity should match");
//             Assert.AreEqual(target, damageData.targetEntity, "Target entity should match");
//             Assert.AreEqual(damage, damageData.baseDamage, "Base damage should match");
//             Assert.AreEqual(DamageType.Physical, damageData.damageType, "Damage type should be Physical");
//             Assert.IsFalse(damageData.isCritical, "Should not be critical by default");
//             Assert.IsFalse(damageData.isTrueDamage, "Should not be true damage by default");
//         }
//         
//         [Test]
//         public void DamageData_CalculateFinalDamage_AppliesArmorCorrectly()
//         {
//             // Arrange
//             var damageData = DamageData.CreatePhysical(testAttacker, testTarget, (dfloat)100, dfloat3.zero);
//             dfloat armor = (dfloat)50; // Should reduce damage
//             dfloat resistance = dfloat.Zero;
//             
//             // Act
//             dfloat finalDamage = damageData.CalculateFinalDamage(armor, resistance);
//             
//             // Assert
//             Assert.Less((float)finalDamage, 100f, "Final damage should be less than base damage due to armor");
//             Assert.Greater((float)finalDamage, 0f, "Final damage should still be greater than 0");
//         }
//         
//         [Test]
//         public void DamageData_CalculateFinalDamage_IgnoresArmorForTrueDamage()
//         {
//             // Arrange
//             var damageData = DamageData.CreateTrue(testAttacker, testTarget, (dfloat)100, dfloat3.zero);
//             dfloat armor = (dfloat)1000; // Very high armor
//             dfloat resistance = (dfloat)0.9f; // High resistance
//             
//             // Act
//             dfloat finalDamage = damageData.CalculateFinalDamage(armor, resistance);
//             
//             // Assert
//             Assert.AreEqual(100f, (float)finalDamage, 0.01f, "True damage should ignore armor and resistance");
//         }
//         
//         [Test]
//         public void CombatComponentUtils_AddCombatComponents_AddsRequiredComponents()
//         {
//             // Arrange
//             Entity unit = entityManager.CreateEntity();
//             entityManager.AddComponentData(unit, attackerStats);
//             
//             // Act
//             CombatComponentUtils.AddCombatComponents(entityManager, unit, attackerStats);
//             
//             // Assert
//             Assert.IsTrue(entityManager.HasComponent<AttackData>(unit), "Should have AttackData component");
//             Assert.IsTrue(entityManager.HasComponent<TargetingData>(unit), "Should have TargetingData component");
//             
//             var attackData = entityManager.GetComponentData<AttackData>(unit);
//             Assert.AreEqual(attackerStats.attackDamage, attackData.baseDamage, "Attack damage should match UnitStats");
//             Assert.AreEqual(attackerStats.attackRange, attackData.attackRange, "Attack range should match UnitStats");
//         }
//         
//         [Test]
//         public void DamageUtils_CalculateArmorReduction_ReturnsCorrectReduction()
//         {
//             // Arrange
//             dfloat armor = (dfloat)100;
//             dfloat armorPenetration = (dfloat)0.5f; // 50% penetration
//             
//             // Act
//             dfloat reduction = DamageUtils.CalculateArmorReduction(armor, armorPenetration);
//             
//             // Assert
//             Assert.Greater((float)reduction, 0f, "Armor reduction should be greater than 0");
//             Assert.Less((float)reduction, 1f, "Armor reduction should be less than 100%");
//         }
//         
//         [Test]
//         public void DamageUtils_CalculateFinalDamage_IsDeterministic()
//         {
//             // Arrange
//             dfloat baseDamage = (dfloat)100;
//             dfloat armor = (dfloat)50;
//             dfloat resistance = (dfloat)0.2f;
//             dfloat armorPenetration = (dfloat)0.3f;
//             dfloat critMultiplier = (dfloat)2;
//             
//             // Act - Calculate multiple times
//             dfloat damage1 = DamageUtils.CalculateFinalDamage(baseDamage, armor, resistance, armorPenetration, critMultiplier, true, false, false);
//             dfloat damage2 = DamageUtils.CalculateFinalDamage(baseDamage, armor, resistance, armorPenetration, critMultiplier, true, false, false);
//             dfloat damage3 = DamageUtils.CalculateFinalDamage(baseDamage, armor, resistance, armorPenetration, critMultiplier, true, false, false);
//             
//             // Assert
//             Assert.AreEqual((float)damage1, (float)damage2, 0.001f, "Damage calculation should be deterministic");
//             Assert.AreEqual((float)damage2, (float)damage3, 0.001f, "Damage calculation should be deterministic");
//         }
//         
//         [Test]
//         public void TargetingUtils_IsInRange_WorksCorrectly()
//         {
//             // Arrange
//             dfloat3 attackerPos = dfloat3.zero;
//             dfloat3 targetPos = new dfloat3((dfloat)3, dfloat.Zero, dfloat.Zero);
//             dfloat range = (dfloat)5;
//             
//             // Act
//             bool inRange = TargetingUtils.IsInRange(attackerPos, targetPos, range);
//             
//             // Assert
//             Assert.IsTrue(inRange, "Target should be in range");
//         }
//         
//         [Test]
//         public void TargetingUtils_IsInCone_WorksCorrectly()
//         {
//             // Arrange
//             dfloat3 attackerPos = dfloat3.zero;
//             dfloat3 attackerForward = new dfloat3(dfloat.Zero, dfloat.Zero, dfloat.One);
//             dfloat3 targetPos = new dfloat3((dfloat)1, dfloat.Zero, (dfloat)2); // Slightly to the side
//             dfloat coneAngle = (dfloat)(45f * dmath.PI / 180f); // 45 degrees in radians
//             dfloat coneRange = (dfloat)5;
//             
//             // Act
//             bool inCone = TargetingUtils.IsInCone(attackerPos, attackerForward, targetPos, coneAngle, coneRange);
//             
//             // Assert
//             Assert.IsTrue(inCone, "Target should be within cone");
//         }
//         
//         [Test]
//         public void AttackPatternDefinition_ValidateConfiguration_DetectsInvalidConfig()
//         {
//             // This would require creating a ScriptableObject instance in tests
//             // For now, we'll test the validation logic conceptually
//             Assert.Pass("Attack pattern validation would be tested with ScriptableObject instances");
//         }
//         
//         [Test]
//         public void CombatSystemIntegration_SetupCombatUnit_WorksCorrectly()
//         {
//             // This would require a full CombatUnitDefinition setup
//             // For now, we'll test the basic integration
//             Assert.Pass("Combat system integration would be tested with full unit definitions");
//         }
//         
//         [TearDown]
//         public override void TearDown()
//         {
//             base.TearDown();
//         }
//     }
// }
