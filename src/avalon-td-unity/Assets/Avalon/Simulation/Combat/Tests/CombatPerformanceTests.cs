// using NUnit.Framework;
// using Unity.Collections;
// using Unity.Deterministic.Mathematics;
// using Unity.Entities;
// using System.Diagnostics;
// using Unity.PerformanceTesting;
// using Unity.Entities.Tests;
//
// namespace FlowField.Combat.Tests
// {
//     /// <summary>
//     /// Performance tests for the FlowField Combat System
//     /// Tests system performance under various load conditions and entity counts
//     /// </summary>
//     [TestFixture]
//     public class CombatPerformanceTests : ECSTestsFixture
//     {
//         private EntityManager entityManager;
//         private World testWorld;
//         
//         [SetUp]
//         public override void Setup()
//         {
//             base.Setup();
//             entityManager = m_Manager;
//             testWorld = m_Manager.World;
//         }
//         
//         [Test, Performance]
//         public void TargetSelection_Performance_SmallScale()
//         {
//             // Arrange
//             int unitCount = 100;
//             var units = CreateTestCombatUnits(unitCount);
//             var targetSelectionSystem = testWorld.GetOrCreateSystemManaged<TargetSelectionSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 targetSelectionSystem.Update();
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(units);
//         }
//         
//         [Test, Performance]
//         public void TargetSelection_Performance_MediumScale()
//         {
//             // Arrange
//             int unitCount = 500;
//             var units = CreateTestCombatUnits(unitCount);
//             var targetSelectionSystem = testWorld.GetOrCreateSystemManaged<TargetSelectionSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 targetSelectionSystem.Update();
//             })
//             .WarmupCount(3)
//             .MeasurementCount(15)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(units);
//         }
//         
//         [Test, Performance]
//         public void TargetSelection_Performance_LargeScale()
//         {
//             // Arrange
//             int unitCount = 1000;
//             var units = CreateTestCombatUnits(unitCount);
//             var targetSelectionSystem = testWorld.GetOrCreateSystemManaged<TargetSelectionSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 targetSelectionSystem.Update();
//             })
//             .WarmupCount(2)
//             .MeasurementCount(10)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(units);
//         }
//         
//         [Test, Performance]
//         public void ProjectileSystem_Performance_ManyProjectiles()
//         {
//             // Arrange
//             int projectileCount = 500;
//             var projectiles = CreateTestProjectiles(projectileCount);
//             var projectileSystem = testWorld.GetOrCreateSystemManaged<ProjectileSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 projectileSystem.Update();
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(projectiles);
//         }
//         
//         [Test, Performance]
//         public void DamageApplication_Performance_ManyDamageEvents()
//         {
//             // Arrange
//             int damageEventCount = 200;
//             var damageEvents = CreateTestDamageEvents(damageEventCount);
//             var damageSystem = testWorld.GetOrCreateSystemManaged<DamageApplicationSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 damageSystem.Update();
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(damageEvents);
//         }
//         
//         [Test, Performance]
//         public void CombatEffects_Performance_ManyEffects()
//         {
//             // Arrange
//             int unitCount = 300;
//             var units = CreateTestUnitsWithEffects(unitCount);
//             var effectsSystem = testWorld.GetOrCreateSystemManaged<CombatEffectsProcessingSystem>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 effectsSystem.Update();
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(units);
//         }
//         
//         [Test, Performance]
//         public void FullCombatSystem_Performance_IntegratedTest()
//         {
//             // Arrange
//             int unitCount = 200;
//             int projectileCount = 100;
//             
//             var units = CreateTestCombatUnits(unitCount);
//             var projectiles = CreateTestProjectiles(projectileCount);
//             
//             var combatSystemGroup = testWorld.GetOrCreateSystemManaged<FlowFieldCombatSystemGroup>();
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 combatSystemGroup.Update();
//             })
//             .WarmupCount(3)
//             .MeasurementCount(15)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//             
//             // Cleanup
//             CleanupTestEntities(units);
//             CleanupTestEntities(projectiles);
//         }
//         
//         [Test]
//         public void DamageCalculation_Performance_ManyCalculations()
//         {
//             // Arrange
//             int calculationCount = 10000;
//             dfloat baseDamage = (dfloat)100;
//             dfloat armor = (dfloat)50;
//             dfloat resistance = (dfloat)0.2f;
//             dfloat armorPenetration = (dfloat)0.3f;
//             dfloat critMultiplier = (dfloat)2;
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 for (int i = 0; i < calculationCount; i++)
//                 {
//                     DamageUtils.CalculateFinalDamage(baseDamage, armor, resistance, armorPenetration, 
//                         critMultiplier, i % 5 == 0, false, false);
//                 }
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .Run();
//         }
//         
//         [Test]
//         public void TargetingCalculation_Performance_ManyDistanceChecks()
//         {
//             // Arrange
//             int checkCount = 10000;
//             dfloat3 attackerPos = dfloat3.zero;
//             dfloat range = (dfloat)5;
//             
//             // Act & Measure
//             Measure.Method(() =>
//             {
//                 for (int i = 0; i < checkCount; i++)
//                 {
//                     dfloat3 targetPos = new dfloat3((dfloat)(i % 10), dfloat.Zero, (dfloat)(i % 10));
//                     TargetingUtils.IsInRange(attackerPos, targetPos, range);
//                 }
//             })
//             .WarmupCount(5)
//             .MeasurementCount(20)
//             .IterationsPerMeasurement(1)
//             .Run();
//         }
//         
//         [Test]
//         public void Memory_Allocation_CombatComponents()
//         {
//             // Test memory allocation patterns for combat components
//             Measure.Method(() =>
//             {
//                 var units = CreateTestCombatUnits(100);
//                 CleanupTestEntities(units);
//             })
//             .WarmupCount(2)
//             .MeasurementCount(10)
//             .IterationsPerMeasurement(1)
//             .GC()
//             .Run();
//         }
//         
//         private NativeArray<Entity> CreateTestCombatUnits(int count)
//         {
//             var units = new NativeArray<Entity>(count, Allocator.Temp);
//             
//             for (int i = 0; i < count; i++)
//             {
//                 var unit = entityManager.CreateEntity();
//                 
//                 // Add required components
//                 entityManager.AddComponentData(unit, new UnitStats
//                 {
//                     maxHealth = (dfloat)100,
//                     currentHealth = (dfloat)100,
//                     attackDamage = (dfloat)25,
//                     attackRange = (dfloat)5,
//                     attackCooldown = (dfloat)1,
//                     maxSpeed = (dfloat)3,
//                     radius = (dfloat)0.5f
//                 });
//                 
//                 entityManager.AddComponentData(unit, new SimulationTransform
//                 {
//                     position = new dfloat3((dfloat)(i % 20), dfloat.Zero, (dfloat)(i / 20)),
//                     rotation = dquaternion.identity,
//                     scale = dfloat.One
//                 });
//                 
//                 entityManager.AddComponentData(unit, AttackData.CreateDefault());
//                 entityManager.AddComponentData(unit, TargetingData.CreateDefault());
//                 
//                 units[i] = unit;
//             }
//             
//             return units;
//         }
//         
//         private NativeArray<Entity> CreateTestProjectiles(int count)
//         {
//             var projectiles = new NativeArray<Entity>(count, Allocator.Temp);
//             
//             for (int i = 0; i < count; i++)
//             {
//                 var projectile = entityManager.CreateEntity();
//                 
//                 entityManager.AddComponentData(projectile, new SimulationTransform
//                 {
//                     position = new dfloat3((dfloat)(i % 10), dfloat.Zero, (dfloat)(i / 10)),
//                     rotation = dquaternion.identity,
//                     scale = dfloat.One
//                 });
//                 
//                 var projectileData = ProjectileData.CreateLinear(Entity.Null, 
//                     new dfloat3((dfloat)(i % 10), dfloat.Zero, (dfloat)(i / 10)),
//                     new dfloat3((dfloat)(i % 10 + 5), dfloat.Zero, (dfloat)(i / 10)),
//                     (dfloat)10, (dfloat)25);
//                 
//                 entityManager.AddComponentData(projectile, projectileData);
//                 
//                 projectiles[i] = projectile;
//             }
//             
//             return projectiles;
//         }
//         
//         private NativeArray<Entity> CreateTestDamageEvents(int count)
//         {
//             var damageEvents = new NativeArray<Entity>(count, Allocator.Temp);
//             
//             for (int i = 0; i < count; i++)
//             {
//                 var damageEvent = entityManager.CreateEntity();
//                 
//                 var damageData = DamageData.CreatePhysical(Entity.Null, Entity.Null, 
//                     (dfloat)50, new dfloat3((dfloat)i, dfloat.Zero, dfloat.Zero));
//                 
//                 entityManager.AddComponentData(damageEvent, damageData);
//                 
//                 damageEvents[i] = damageEvent;
//             }
//             
//             return damageEvents;
//         }
//         
//         private NativeArray<Entity> CreateTestUnitsWithEffects(int count)
//         {
//             var units = new NativeArray<Entity>(count, Allocator.Temp);
//             
//             for (int i = 0; i < count; i++)
//             {
//                 var unit = entityManager.CreateEntity();
//                 
//                 entityManager.AddComponentData(unit, new UnitStats
//                 {
//                     maxHealth = (dfloat)100,
//                     currentHealth = (dfloat)80,
//                     attackDamage = (dfloat)25,
//                     attackRange = (dfloat)5,
//                     attackCooldown = (dfloat)1,
//                     maxSpeed = (dfloat)3,
//                     radius = (dfloat)0.5f
//                 });
//                 
//                 // Add effect buffer with some test effects
//                 var effectBuffer = entityManager.AddBuffer<FlowField.Effects.GameEffect>(unit);
//                 
//                 // Add a few test effects
//                 effectBuffer.Add(new FlowField.Effects.GameEffect
//                 {
//                     category = FlowField.Effects.GameEffect.EffectCategory.Health,
//                     effectType = FlowField.Effects.GameEffect.EffectType.DamageOverTime,
//                     primaryValue = (dfloat)5,
//                     duration = (dfloat)10,
//                     startTime = dfloat.Zero,
//                     tickInterval = dfloat.One
//                 });
//                 
//                 effectBuffer.Add(new FlowField.Effects.GameEffect
//                 {
//                     category = FlowField.Effects.GameEffect.EffectCategory.Movement,
//                     effectType = FlowField.Effects.GameEffect.EffectType.Slow,
//                     primaryValue = (dfloat)0.5f,
//                     duration = (dfloat)5,
//                     startTime = dfloat.Zero
//                 });
//                 
//                 units[i] = unit;
//             }
//             
//             return units;
//         }
//         
//         private void CleanupTestEntities(NativeArray<Entity> entities)
//         {
//             if (entities.IsCreated)
//             {
//                 entityManager.DestroyEntity(entities);
//                 entities.Dispose();
//             }
//         }
//         
//         [TearDown]
//         public override void TearDown()
//         {
//             base.TearDown();
//         }
//     }
// }
