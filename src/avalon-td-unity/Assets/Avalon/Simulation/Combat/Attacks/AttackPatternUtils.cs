using Avalon.Simulation.Combat;
using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Collections;

namespace Avalon.Simulation.Combat.Attacks
{
    /// <summary>
    /// Utility class for executing different attack patterns
    /// Contains static methods for each attack pattern type
    /// </summary>
    public static class AttackPatternUtils
    {
        /// <summary>
        /// Execute a single target attack
        /// </summary>
        public static void ExecuteSingleTargetAttack(EntityManager entityManager, Entity attacker, Entity target,
            AttackData attackData, dfloat3 attackerPos, dfloat3 targetPos, dfloat currentTime)
        {
            // Create projectile or apply instant damage
            if (attackData.projectileType == ProjectileType.Instant)
            {
                // Instant damage
                var damage = attackData.GetEffectiveDamage(attackData.RollCritical((uint)(currentTime * 1000)));
                CombatComponentUtils.ApplyDamage(entityManager, attacker, target, damage, targetPos, attackData.damageType);
            }
            else
            {
                // Create projectile
                CombatComponentUtils.CreateProjectile(entityManager, attacker, attackerPos, targetPos, 
                    attackData.baseDamage, attackData.projectileType);
            }
        }
        
        /// <summary>
        /// Execute a linear attack that hits the first target in line
        /// </summary>
        public static void ExecuteLinearAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos, 
            dfloat3 targetPos, AttackData attackData, dfloat currentTime)
        {
            // Calculate direction
            dfloat3 direction = dmath.normalize(targetPos - attackerPos);
            dfloat3 endPos = attackerPos + direction * attackData.attackRange;
            
            // Create linear projectile
            var projectile = CombatComponentUtils.CreateProjectile(entityManager, attacker, attackerPos, endPos, 
                attackData.baseDamage, ProjectileType.Linear);
                
            // Set piercing if configured
            if (entityManager.HasComponent<ProjectileData>(projectile))
            {
                var projData = entityManager.GetComponentData<ProjectileData>(projectile);
                projData.pierceCount = attackData.pierceCount;
                projData.maxPierces = attackData.pierceCount;
                entityManager.SetComponentData(projectile, projData);
            }
        }
        
        /// <summary>
        /// Execute a multi-linear wave attack with multiple projectiles
        /// </summary>
        public static void ExecuteMultiLinearWaveAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos,
            dfloat3 targetPos, AttackData attackData, dfloat currentTime)
        {
            dfloat3 baseDirection = dmath.normalize(targetPos - attackerPos);
            dfloat halfSpread = attackData.spreadAngle * (dfloat)0.5f;
            dfloat angleStep = attackData.spreadAngle / dmath.max((dfloat)1, (dfloat)(attackData.projectileCount - 1));
            
            for (int i = 0; i < attackData.projectileCount; i++)
            {
                // Calculate angle for this projectile
                dfloat angle = -halfSpread + angleStep * (dfloat)i;
                
                // Rotate direction by angle
                dfloat3 direction = RotateDirection(baseDirection, angle);
                dfloat3 endPos = attackerPos + direction * attackData.attackRange;
                
                // Create projectile
                CombatComponentUtils.CreateProjectile(entityManager, attacker, attackerPos, endPos, 
                    attackData.baseDamage, ProjectileType.Linear);
            }
        }
        
        /// <summary>
        /// Execute a homing attack that tracks a specific target
        /// </summary>
        public static void ExecuteHomingAttack(EntityManager entityManager, Entity attacker, Entity target,
            dfloat3 attackerPos, AttackData attackData, dfloat currentTime)
        {
            // Create homing projectile
            CombatComponentUtils.CreateHomingProjectile(entityManager, attacker, target, attackerPos,
                attackData.baseDamage, attackData.projectileSpeed, attackData.homingStrength);
        }
        
        /// <summary>
        /// Execute an AOE attack at a specific position
        /// </summary>
        public static void ExecuteAOEPositionAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos,
            dfloat3 targetPos, AttackData attackData, dfloat currentTime, EntityQuery enemyQuery)
        {
            // Get all entities within AOE radius
            var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
            var transforms = enemyQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int hitCount = 0;
            for (int i = 0; i < enemies.Length && hitCount < attackData.maxTargets; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, targetPos);
                if (distance <= attackData.aoeRadius)
                {
                    // Calculate damage falloff
                    dfloat damageMultiplier = dfloat.One;
                    if (attackData.aoeRadius > dfloat.Zero)
                    {
                        dfloat falloff = distance / attackData.aoeRadius;
                        damageMultiplier = dmath.lerp(dfloat.One, (dfloat)0.5f, falloff);
                    }
                    
                    dfloat damage = attackData.baseDamage * damageMultiplier;
                    CombatComponentUtils.ApplyDamage(entityManager, attacker, enemies[i], damage, 
                        transforms[i].position, attackData.damageType);
                    hitCount++;
                }
            }
            
            enemies.Dispose();
            transforms.Dispose();
        }
        
        /// <summary>
        /// Execute an AOE attack centered on the attacker
        /// </summary>
        public static void ExecuteAOESelfAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos,
            AttackData attackData, dfloat currentTime, EntityQuery enemyQuery)
        {
            ExecuteAOEPositionAttack(entityManager, attacker, attackerPos, attackerPos, attackData, currentTime, enemyQuery);
        }
        
        /// <summary>
        /// Execute a cone attack in front of the attacker
        /// </summary>
        public static void ExecuteConeAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos,
            dfloat3 targetPos, AttackData attackData, dfloat currentTime, EntityQuery enemyQuery)
        {
            dfloat3 coneDirection = dmath.normalize(targetPos - attackerPos);
            dfloat halfAngle = attackData.coneAngle * (dfloat)0.5f;
            
            var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
            var transforms = enemyQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int hitCount = 0;
            for (int i = 0; i < enemies.Length && hitCount < attackData.maxTargets; i++)
            {
                dfloat3 toEnemy = transforms[i].position - attackerPos;
                dfloat distance = dmath.length(toEnemy);
                
                if (distance <= attackData.coneRange && distance > dfloat.Zero)
                {
                    dfloat3 dirToEnemy = toEnemy / distance;
                    dfloat angle = dmath.acos(dmath.dot(coneDirection, dirToEnemy));
                    
                    if (angle <= halfAngle)
                    {
                        CombatComponentUtils.ApplyDamage(entityManager, attacker, enemies[i], attackData.baseDamage,
                            transforms[i].position, attackData.damageType);
                        hitCount++;
                    }
                }
            }
            
            enemies.Dispose();
            transforms.Dispose();
        }
        
        /// <summary>
        /// Execute a circular attack around the attacker
        /// </summary>
        public static void ExecuteCircleAttack(EntityManager entityManager, Entity attacker, dfloat3 attackerPos,
            AttackData attackData, dfloat currentTime, EntityQuery enemyQuery)
        {
            var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
            var transforms = enemyQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int hitCount = 0;
            for (int i = 0; i < enemies.Length && hitCount < attackData.maxTargets; i++)
            {
                dfloat distance = dmath.distance(transforms[i].position, attackerPos);
                if (distance <= attackData.attackRange)
                {
                    CombatComponentUtils.ApplyDamage(entityManager, attacker, enemies[i], attackData.baseDamage,
                        transforms[i].position, attackData.damageType);
                    hitCount++;
                }
            }
            
            enemies.Dispose();
            transforms.Dispose();
        }
        
        /// <summary>
        /// Execute a full map attack that hits all enemies
        /// </summary>
        public static void ExecuteFullMapAttack(EntityManager entityManager, Entity attacker, AttackData attackData,
            dfloat currentTime, EntityQuery enemyQuery)
        {
            var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
            var transforms = enemyQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            int hitCount = 0;
            for (int i = 0; i < enemies.Length && hitCount < attackData.maxTargets; i++)
            {
                CombatComponentUtils.ApplyDamage(entityManager, attacker, enemies[i], attackData.baseDamage,
                    transforms[i].position, attackData.damageType);
                hitCount++;
            }
            
            enemies.Dispose();
            transforms.Dispose();
        }
        
        /// <summary>
        /// Rotate a direction vector by an angle around the Y axis
        /// </summary>
        private static dfloat3 RotateDirection(dfloat3 direction, dfloat angle)
        {
            dfloat cos = dmath.cos(angle);
            dfloat sin = dmath.sin(angle);
            
            return new dfloat3(
                direction.x * cos - direction.z * sin,
                direction.y,
                direction.x * sin + direction.z * cos
            );
        }
    }
}
