using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Attacks
{
    /// <summary>
    /// System that processes attack execution for all combat units
    /// Handles cooldowns, attack patterns, and triggers appropriate attack behaviors
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    public partial struct AttackProcessingSystem : ISystem
    {
        private EntityQuery attackerQuery;
        private EntityQuery enemyQuery;
        private EntityQuery fixedTimestepQuery;
        
        // Component lookups for efficient access
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<UnitStats> unitStatsLookup;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for entities that can attack
            attackerQuery = SystemAPI.QueryBuilder()
                .WithAll<AttackData, TargetingData, SimulationTransform>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for potential enemy targets
            enemyQuery = SystemAPI.QueryBuilder()
                .WithAll<SimulationTransform, UnitStats>()
                .WithNone<Disabled>()
                .Build();
                
            // Query for fixed timestep
            fixedTimestepQuery = SystemAPI.QueryBuilder()
                .WithAll<FixedTimestep>()
                .Build();
                
            // Initialize component lookups
            transformLookup = SystemAPI.GetComponentLookup<SimulationTransform>(true);
            unitStatsLookup = SystemAPI.GetComponentLookup<UnitStats>(true);
            
            state.RequireForUpdate(attackerQuery);
            state.RequireForUpdate(fixedTimestepQuery);
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Update component lookups
            transformLookup.Update(ref state);
            unitStatsLookup.Update(ref state);
            
            // Schedule attack processing job
            var attackJob = new ProcessAttacksJob
            {
                currentTime = fixedTimestep.currentTime,
                transformLookup = transformLookup,
                unitStatsLookup = unitStatsLookup,
                enemyQuery = enemyQuery
            };
            
            state.Dependency = attackJob.ScheduleParallel(attackerQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that processes attacks for all attacking entities
    /// </summary>
    [BurstCompile]
    public partial struct ProcessAttacksJob : IJobEntity
    {
        [ReadOnly] public dfloat currentTime;
        [ReadOnly] public ComponentLookup<SimulationTransform> transformLookup;
        [ReadOnly] public ComponentLookup<UnitStats> unitStatsLookup;
        [ReadOnly] public EntityQuery enemyQuery;
        
        public void Execute(Entity entity, ref AttackData attackData, ref TargetingData targetingData,
            in SimulationTransform transform)
        {
            // Check if unit can attack
            if (!attackData.CanAttack(currentTime))
            {
                // Update combat state - remove attacking flag if cooldown expired
                if ((currentTime - attackData.lastAttackTime) >= attackData.attackCooldown)
                {
                    attackData.combatState &= ~(CombatState.Attacking | CombatState.OnCooldown);
                }
                return;
            }
            
            // Check if we have a valid target
            if (!targetingData.HasValidTarget())
            {
                attackData.combatState &= ~CombatState.HasTarget;
                return;
            }
            
            // Verify target still exists and is in range
            if (!transformLookup.HasComponent(targetingData.currentTarget))
            {
                targetingData.ClearTarget(currentTime);
                attackData.combatState &= ~CombatState.HasTarget;
                return;
            }
            
            var targetTransform = transformLookup[targetingData.currentTarget];
            dfloat3 attackerPos = transform.position;
            dfloat3 targetPos = targetTransform.position;
            
            // Check if target is still in range
            if (targetingData.ShouldLoseTarget(attackerPos, targetPos))
            {
                targetingData.ClearTarget(currentTime);
                attackData.combatState &= ~CombatState.HasTarget;
                return;
            }
            
            // Execute attack based on pattern
            ExecuteAttackPattern(entity, ref attackData, ref targetingData, attackerPos, targetPos);
        }
        
        private void ExecuteAttackPattern(Entity attacker, ref AttackData attackData, ref TargetingData targetingData,
            dfloat3 attackerPos, dfloat3 targetPos)
        {
            // Start the attack
            attackData.StartAttack(currentTime);
            
            // Note: In a burst-compiled job, we can't call EntityManager methods directly
            // Instead, we would typically use EntityCommandBuffer or defer the actual execution
            // For now, we'll update the attack state and let another system handle execution
            
            switch (attackData.attackPattern)
            {
                case AttackPattern.SingleTarget:
                    // Mark for single target attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Linear:
                case AttackPattern.LinearPiercing:
                    // Mark for linear attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.MultiLinearWave:
                    // Mark for multi-linear attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Homing:
                    // Mark for homing attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.AOEPosition:
                    // Mark for AOE position attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.AOESelf:
                    // Mark for AOE self attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Cone:
                    // Mark for cone attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Circle:
                    // Mark for circle attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.FullMap:
                    // Mark for full map attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Chain:
                    // Mark for chain attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
                    
                case AttackPattern.Beam:
                    // Mark for beam attack execution
                    attackData.combatState |= CombatState.Attacking;
                    break;
            }
        }
    }
}
