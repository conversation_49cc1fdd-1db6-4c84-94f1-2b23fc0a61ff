using Avalon.Simulation.Combat.Core;
using FlowField;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Attacks
{
    /// <summary>
    /// System that executes the actual attack patterns and creates projectiles/applies damage
    /// Runs after AttackProcessingSystem to handle EntityManager operations
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    [UpdateAfter(typeof(AttackProcessingSystem))]
    public partial class AttackExecutionSystem : SystemBase
    {
        private EntityQuery attackingUnitsQuery;
        private EntityQuery enemyQuery;
        private EntityQuery fixedTimestepQuery;
        
        protected override void OnCreate()
        {
            // Query for units that are currently attacking
            attackingUnitsQuery = GetEntityQuery(
                ComponentType.ReadWrite<AttackData>(),
                ComponentType.ReadWrite<TargetingData>(),
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.Exclude<Disabled>()
            );
            
            // Query for potential targets
            enemyQuery = GetEntityQuery(
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.Exclude<Disabled>()
            );
            
            // Query for fixed timestep
            fixedTimestepQuery = GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());
            
            RequireForUpdate(attackingUnitsQuery);
            RequireForUpdate(fixedTimestepQuery);
        }
        
        protected override void OnUpdate()
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            dfloat currentTime = fixedTimestep.currentTime;
            
            // Process all attacking units
            Entities
                .WithAll<AttackData, TargetingData, SimulationTransform>()
                .WithNone<Disabled>()
                .ForEach((Entity entity, ref AttackData attackData, ref TargetingData targetingData, 
                    in SimulationTransform transform) =>
                {
                    // Only process units that are currently attacking
                    if ((attackData.combatState & CombatState.Attacking) == 0)
                        return;
                    
                    // Get target position
                    dfloat3 attackerPos = transform.position;
                    dfloat3 targetPos = targetingData.lastTargetPosition;
                    
                    // If we have a valid target entity, get its current position
                    if (targetingData.HasValidTarget() && HasComponent<SimulationTransform>(targetingData.currentTarget))
                    {
                        targetPos = GetComponent<SimulationTransform>(targetingData.currentTarget).position;
                        targetingData.lastTargetPosition = targetPos;
                    }
                    
                    // Execute the appropriate attack pattern
                    ExecuteAttackPattern(entity, attackData, targetingData, attackerPos, targetPos, currentTime);
                    
                    // Clear attacking state after execution
                    attackData.combatState &= ~CombatState.Attacking;
                    
                }).WithoutBurst().Run();
        }
        
        private void ExecuteAttackPattern(Entity attacker, AttackData attackData, TargetingData targetingData,
            dfloat3 attackerPos, dfloat3 targetPos, dfloat currentTime)
        {
            switch (attackData.attackPattern)
            {
                case AttackPattern.SingleTarget:
                    if (targetingData.HasValidTarget())
                    {
                        AttackPatternUtils.ExecuteSingleTargetAttack(EntityManager, attacker, 
                            targetingData.currentTarget, attackData, attackerPos, targetPos, currentTime);
                    }
                    break;
                    
                case AttackPattern.Linear:
                case AttackPattern.LinearPiercing:
                    AttackPatternUtils.ExecuteLinearAttack(EntityManager, attacker, attackerPos, 
                        targetPos, attackData, currentTime);
                    break;
                    
                case AttackPattern.MultiLinearWave:
                    AttackPatternUtils.ExecuteMultiLinearWaveAttack(EntityManager, attacker, attackerPos,
                        targetPos, attackData, currentTime);
                    break;
                    
                case AttackPattern.Homing:
                    if (targetingData.HasValidTarget())
                    {
                        AttackPatternUtils.ExecuteHomingAttack(EntityManager, attacker, 
                            targetingData.currentTarget, attackerPos, attackData, currentTime);
                    }
                    break;
                    
                case AttackPattern.AOEPosition:
                    AttackPatternUtils.ExecuteAOEPositionAttack(EntityManager, attacker, attackerPos,
                        targetPos, attackData, currentTime, enemyQuery);
                    break;
                    
                case AttackPattern.AOESelf:
                    AttackPatternUtils.ExecuteAOESelfAttack(EntityManager, attacker, attackerPos,
                        attackData, currentTime, enemyQuery);
                    break;
                    
                case AttackPattern.Cone:
                    AttackPatternUtils.ExecuteConeAttack(EntityManager, attacker, attackerPos,
                        targetPos, attackData, currentTime, enemyQuery);
                    break;
                    
                case AttackPattern.Circle:
                    AttackPatternUtils.ExecuteCircleAttack(EntityManager, attacker, attackerPos,
                        attackData, currentTime, enemyQuery);
                    break;
                    
                case AttackPattern.FullMap:
                    AttackPatternUtils.ExecuteFullMapAttack(EntityManager, attacker, attackData,
                        currentTime, enemyQuery);
                    break;
                    
                case AttackPattern.Chain:
                    ExecuteChainAttack(attacker, attackData, targetingData, attackerPos, targetPos, currentTime);
                    break;
                    
                case AttackPattern.Beam:
                    ExecuteBeamAttack(attacker, attackData, targetingData, attackerPos, targetPos, currentTime);
                    break;
            }
        }
        
        private void ExecuteChainAttack(Entity attacker, AttackData attackData, TargetingData targetingData,
            dfloat3 attackerPos, dfloat3 targetPos, dfloat currentTime)
        {
            // Start with the primary target
            if (!targetingData.HasValidTarget()) return;
            
            Entity currentTarget = targetingData.currentTarget;
            dfloat3 currentPos = targetPos;
            dfloat currentDamage = attackData.baseDamage;
            
            // Apply damage to primary target
            CombatComponentUtils.ApplyDamage(EntityManager, attacker, currentTarget, currentDamage, 
                currentPos, attackData.damageType);
            
            // Chain to additional targets
            var enemies = enemyQuery.ToEntityArray(Allocator.Temp);
            var transforms = enemyQuery.ToComponentDataArray<SimulationTransform>(Allocator.Temp);
            
            for (int chain = 1; chain < attackData.chainCount && chain < enemies.Length; chain++)
            {
                Entity nextTarget = Entity.Null;
                dfloat closestDistance = dfloat.MAX_VALUE;
                int closestIndex = -1;
                
                // Find closest enemy within chain range
                for (int i = 0; i < enemies.Length; i++)
                {
                    if (enemies[i] == currentTarget) continue; // Skip current target
                    
                    dfloat distance = dmath.distance(transforms[i].position, currentPos);
                    if (distance <= attackData.chainRange && distance < closestDistance)
                    {
                        closestDistance = distance;
                        nextTarget = enemies[i];
                        closestIndex = i;
                    }
                }
                
                if (nextTarget != Entity.Null)
                {
                    // Reduce damage for chain
                    currentDamage *= (dfloat)0.8f; // 20% damage reduction per chain
                    
                    // Apply damage to chained target
                    CombatComponentUtils.ApplyDamage(EntityManager, attacker, nextTarget, currentDamage,
                        transforms[closestIndex].position, attackData.damageType);
                    
                    // Update for next chain
                    currentTarget = nextTarget;
                    currentPos = transforms[closestIndex].position;
                }
                else
                {
                    break; // No more valid targets in range
                }
            }
            
            enemies.Dispose();
            transforms.Dispose();
        }
        
        private void ExecuteBeamAttack(Entity attacker, AttackData attackData, TargetingData targetingData,
            dfloat3 attackerPos, dfloat3 targetPos, dfloat currentTime)
        {
            // Create a beam entity that will continuously damage targets
            var beam = EntityManager.CreateEntity();
            
            // Add beam components (would need to create BeamData component)
            EntityManager.AddComponentData(beam, new SimulationTransform
            {
                position = attackerPos,
                rotation = dquaternion.LookRotationSafe(dmath.normalize(targetPos - attackerPos), dmath.up()),
                scale = dfloat.One
            });
            
            // For now, just do a linear attack as placeholder
            AttackPatternUtils.ExecuteLinearAttack(EntityManager, attacker, attackerPos, targetPos, attackData, currentTime);
        }
    }
}
