﻿namespace Avalon.Simulation.Combat.Attacks.Patterns
{
    public abstract class BaseAttackPattern
    {
        public int Pierce { get; set; }
    }
    
    public class InstantSingleTargetAttackPattern : BaseAttackPattern
    {
        
    }
    
    /// <summary>
    /// Attack pattern for linear projectiles
    /// </summary>
    public class LinearAttackPattern : BaseAttackPattern
    {
        
    }
    
    /// <summary>
    /// Attack pattern that requires a target to be selected to execute
    /// </summary>
    public class TargetedAreaOfEffectAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class AreaOfEffectAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class ConeAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class CircleAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class FullMapAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class ChainAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class BeamAttackPattern : BaseAttackPattern
    {
        
    }
    
    public class HomingAttackPattern : BaseAttackPattern
    {
        
    }
}