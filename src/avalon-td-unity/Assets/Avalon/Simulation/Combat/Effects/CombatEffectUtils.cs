using Avalon.Simulation.Effects;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Effects
{
    /// <summary>
    /// Utility functions for applying combat-specific effects
    /// Provides convenient methods for applying DOT, shields, stuns, and other combat effects
    /// </summary>
    public static class CombatEffectUtils
    {
        /// <summary>
        /// Apply a damage over time effect to a target
        /// </summary>
        public static void ApplyDamageOverTime(EntityManager entityManager, Entity target, dfloat damagePerTick, 
            dfloat duration, dfloat tickRate, DamageType damageType = DamageType.Poison, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time; // Would use proper time system
            
            var dotEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Health,
                effectType = GetDOTEffectType(damageType),
                stackingType = GameEffect.StackingType.Independent,
                primaryValue = damagePerTick,
                duration = duration,
                startTime = currentTime,
                tickInterval = dfloat.One / tickRate,
                lastTickTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 10, // Allow multiple DOT stacks
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, dotEffect);
        }
        
        /// <summary>
        /// Apply a healing over time effect to a target
        /// </summary>
        public static void ApplyHealingOverTime(EntityManager entityManager, Entity target, dfloat healingPerTick, 
            dfloat duration, dfloat tickRate, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var hotEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Health,
                effectType = GameEffect.EffectType.HealOverTime,
                stackingType = GameEffect.StackingType.Additive,
                primaryValue = healingPerTick,
                duration = duration,
                startTime = currentTime,
                tickInterval = dfloat.One / tickRate,
                lastTickTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 5,
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, hotEffect);
        }
        
        /// <summary>
        /// Apply a shield effect to a target
        /// </summary>
        public static void ApplyShield(EntityManager entityManager, Entity target, dfloat shieldAmount, 
            dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var shieldEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Health,
                effectType = GameEffect.EffectType.Shield,
                stackingType = GameEffect.StackingType.Additive,
                primaryValue = shieldAmount,
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 3, // Allow multiple shield layers
                priority = 2
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, shieldEffect);
        }
        
        /// <summary>
        /// Apply a stun effect to a target
        /// </summary>
        public static void ApplyStun(EntityManager entityManager, Entity target, dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var stunEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Movement,
                effectType = GameEffect.EffectType.Stun,
                stackingType = GameEffect.StackingType.Refresh,
                primaryValue = dfloat.One, // Full stun
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 1,
                priority = 3
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, stunEffect);
        }
        
        /// <summary>
        /// Apply a slow effect to a target
        /// </summary>
        public static void ApplySlow(EntityManager entityManager, Entity target, dfloat slowAmount, 
            dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var slowEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Movement,
                effectType = GameEffect.EffectType.Slow,
                stackingType = GameEffect.StackingType.Multiplicative,
                primaryValue = slowAmount,
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 5,
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, slowEffect);
        }
        
        /// <summary>
        /// Apply a damage buff to a target
        /// </summary>
        public static void ApplyDamageBuff(EntityManager entityManager, Entity target, dfloat damageMultiplier, 
            dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var damageBuffEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Combat,
                effectType = GameEffect.EffectType.AttackBuff,
                stackingType = GameEffect.StackingType.Multiplicative,
                primaryValue = damageMultiplier,
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 3,
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, damageBuffEffect);
        }
        
        /// <summary>
        /// Apply an attack speed buff to a target
        /// </summary>
        public static void ApplyAttackSpeedBuff(EntityManager entityManager, Entity target, dfloat speedMultiplier, 
            dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var speedBuffEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Combat,
                effectType = GameEffect.EffectType.AttackSpeed,
                stackingType = GameEffect.StackingType.Multiplicative,
                primaryValue = speedMultiplier,
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 3,
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, speedBuffEffect);
        }
        
        /// <summary>
        /// Apply vulnerability effect (increased damage taken)
        /// </summary>
        public static void ApplyVulnerability(EntityManager entityManager, Entity target, dfloat vulnerabilityAmount, 
            dfloat duration, int sourceId = 0)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            var vulnerabilityEffect = new GameEffect
            {
                category = GameEffect.EffectCategory.Health,
                effectType = GameEffect.EffectType.Vulnerability,
                stackingType = GameEffect.StackingType.Additive,
                primaryValue = vulnerabilityAmount,
                duration = duration,
                startTime = currentTime,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = 3,
                priority = 1
            };
            
            GameEffectUtils.ApplyEffect(effectBuffer, vulnerabilityEffect);
        }
        
        /// <summary>
        /// Remove all effects of a specific type from a target
        /// </summary>
        public static void RemoveEffectType(EntityManager entityManager, Entity target, GameEffect.EffectType effectType)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            
            for (int i = effectBuffer.Length - 1; i >= 0; i--)
            {
                if (effectBuffer[i].effectType == effectType)
                {
                    effectBuffer.RemoveAt(i);
                }
            }
        }
        
        /// <summary>
        /// Remove all effects from a specific source
        /// </summary>
        public static void RemoveEffectsFromSource(EntityManager entityManager, Entity target, int sourceId)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            
            for (int i = effectBuffer.Length - 1; i >= 0; i--)
            {
                if (effectBuffer[i].sourceId == sourceId)
                {
                    effectBuffer.RemoveAt(i);
                }
            }
        }
        
        /// <summary>
        /// Check if a target has a specific effect type
        /// </summary>
        public static bool HasEffectType(EntityManager entityManager, Entity target, GameEffect.EffectType effectType)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return false;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            
            for (int i = 0; i < effectBuffer.Length; i++)
            {
                var effect = effectBuffer[i];
                if (effect.effectType == effectType && (currentTime - effect.startTime) < effect.duration)
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// Get the total value of all effects of a specific type
        /// </summary>
        public static dfloat GetEffectTypeValue(EntityManager entityManager, Entity target, GameEffect.EffectType effectType)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return dfloat.Zero;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            dfloat currentTime = (dfloat)UnityEngine.Time.time;
            dfloat totalValue = dfloat.Zero;
            
            for (int i = 0; i < effectBuffer.Length; i++)
            {
                var effect = effectBuffer[i];
                if (effect.effectType == effectType && (currentTime - effect.startTime) < effect.duration)
                {
                    totalValue += effect.primaryValue;
                }
            }
            
            return totalValue;
        }
        
        /// <summary>
        /// Apply a cleanse effect that removes debuffs
        /// </summary>
        public static void ApplyCleanse(EntityManager entityManager, Entity target, bool removeDebuffs = true, bool removeDOTs = true)
        {
            if (!entityManager.HasComponent<DynamicBuffer<GameEffect>>(target))
                return;
                
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            
            for (int i = effectBuffer.Length - 1; i >= 0; i--)
            {
                var effect = effectBuffer[i];
                bool shouldRemove = false;
                
                if (removeDebuffs)
                {
                    // Remove movement debuffs
                    if (effect.effectType == GameEffect.EffectType.Slow ||
                        effect.effectType == GameEffect.EffectType.Stun ||
                        effect.effectType == GameEffect.EffectType.Root)
                    {
                        shouldRemove = true;
                    }
                    
                    // Remove combat debuffs
                    if (effect.effectType == GameEffect.EffectType.AttackDebuff ||
                        effect.effectType == GameEffect.EffectType.Vulnerability)
                    {
                        shouldRemove = true;
                    }
                }
                
                if (removeDOTs)
                {
                    // Remove damage over time effects
                    if (effect.effectType == GameEffect.EffectType.DamageOverTime ||
                        effect.effectType == GameEffect.EffectType.Poison ||
                        effect.effectType == GameEffect.EffectType.Burn ||
                        effect.effectType == GameEffect.EffectType.Bleed)
                    {
                        shouldRemove = true;
                    }
                }
                
                if (shouldRemove)
                {
                    effectBuffer.RemoveAt(i);
                }
            }
        }
        
        private static GameEffect.EffectType GetDOTEffectType(DamageType damageType)
        {
            switch (damageType)
            {
                case DamageType.Poison:
                    return GameEffect.EffectType.Poison;
                case DamageType.Fire:
                    return GameEffect.EffectType.Burn;
                default:
                    return GameEffect.EffectType.DamageOverTime;
            }
        }
    }
}
