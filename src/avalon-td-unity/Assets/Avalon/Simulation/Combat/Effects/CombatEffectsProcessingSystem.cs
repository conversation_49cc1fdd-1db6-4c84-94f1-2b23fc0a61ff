using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Combat.Damage;
using Avalon.Simulation.Effects;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Effects
{
    /// <summary>
    /// System that processes combat-specific effects like damage over time, shields, and combat buffs
    /// Integrates with the existing GameEffect system to provide combat-specific functionality
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    [UpdateAfter(typeof(DamageApplicationSystem))]
    public partial class CombatEffectsProcessingSystem : SystemBase
    {
        private EntityQuery combatEffectsQuery;
        private EntityQuery fixedTimestepQuery;
        private EndSimulationEntityCommandBufferSystem ecbSystem;
        
        protected override void OnCreate()
        {
            // Query for entities with combat effects
            // Use typeof(GameEffect) instead of DynamicBuffer<GameEffect> for buffer queries
            combatEffectsQuery = GetEntityQuery(
                ComponentType.ReadWrite<GameEffect>(),
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.Exclude<Disabled>()
            );

            // Query for fixed timestep
            fixedTimestepQuery = GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());

            // Get ECB system
            ecbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();

            // Only run when there are entities with combat effects to process
            RequireForUpdate(combatEffectsQuery);
            RequireForUpdate(fixedTimestepQuery);
        }
        
        protected override void OnUpdate()
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            dfloat currentTime = fixedTimestep.currentTime;
            var ecb = ecbSystem.CreateCommandBuffer();
            
            // Process damage over time effects
            ProcessDamageOverTimeEffects(currentTime, ecb);
            
            // Process healing over time effects
            ProcessHealingOverTimeEffects(currentTime, ecb);
            
            // Process shield effects
            ProcessShieldEffects(currentTime, ecb);
            
            // Process combat state effects (stun, disarm, etc.)
            ProcessCombatStateEffects(currentTime, ecb);
            
            ecbSystem.AddJobHandleForProducer(Dependency);
        }
        
        private void ProcessDamageOverTimeEffects(dfloat currentTime, EntityCommandBuffer ecb)
        {
            Entities
                .ForEach((Entity entity, DynamicBuffer<GameEffect> effectBuffer, ref UnitStats unitStats) =>
                {
                    for (int i = effectBuffer.Length - 1; i >= 0; i--)
                    {
                        var effect = effectBuffer[i];
                        
                        // Process DOT effects
                        if (effect.effectType == GameEffect.EffectType.DamageOverTime ||
                            effect.effectType == GameEffect.EffectType.Poison ||
                            effect.effectType == GameEffect.EffectType.Burn ||
                            effect.effectType == GameEffect.EffectType.Bleed)
                        {
                            if (ShouldTickEffect(effect, currentTime))
                            {
                                // Apply DOT damage
                                dfloat dotDamage = effect.primaryValue;
                                unitStats.currentHealth = dmath.max(dfloat.Zero, unitStats.currentHealth - dotDamage);
                                
                                // Update last tick time
                                effect.lastTickTime = currentTime;
                                effectBuffer[i] = effect;
                                
                                // Spawn DOT visual effect
                                SpawnDOTEffect(ecb, entity, effect.effectType, dotDamage);
                                
                                // Check if target died from DOT
                                if (unitStats.currentHealth <= dfloat.Zero)
                                {
                                    HandleDOTDeath(ecb, entity, effect.sourceId, currentTime);
                                }
                            }
                            
                            // Remove expired effects
                            if (IsEffectExpired(effect, currentTime))
                            {
                                effectBuffer.RemoveAt(i);
                            }
                        }
                    }
                }).WithoutBurst().Run();
        }
        
        private void ProcessHealingOverTimeEffects(dfloat currentTime, EntityCommandBuffer ecb)
        {
            Entities
                .ForEach((Entity entity, DynamicBuffer<GameEffect> effectBuffer, ref UnitStats unitStats) =>
                {
                    for (int i = effectBuffer.Length - 1; i >= 0; i--)
                    {
                        var effect = effectBuffer[i];
                        
                        // Process HOT effects
                        if (effect.effectType == GameEffect.EffectType.HealOverTime ||
                            effect.effectType == GameEffect.EffectType.Regeneration)
                        {
                            if (ShouldTickEffect(effect, currentTime))
                            {
                                // Apply healing
                                dfloat healAmount = effect.primaryValue;
                                unitStats.currentHealth = dmath.min(unitStats.maxHealth, unitStats.currentHealth + healAmount);
                                
                                // Update last tick time
                                effect.lastTickTime = currentTime;
                                effectBuffer[i] = effect;
                                
                                // Spawn healing visual effect
                                SpawnHealingEffect(ecb, entity, healAmount, currentTime);
                            }
                            
                            // Remove expired effects
                            if (IsEffectExpired(effect, currentTime))
                            {
                                effectBuffer.RemoveAt(i);
                            }
                        }
                    }
                }).WithoutBurst().Run();
        }
        
        private void ProcessShieldEffects(dfloat currentTime, EntityCommandBuffer ecb)
        {
            Entities
                .ForEach((Entity entity, DynamicBuffer<GameEffect> effectBuffer) =>
                {
                    dfloat totalShieldAmount = dfloat.Zero;
                    
                    for (int i = effectBuffer.Length - 1; i >= 0; i--)
                    {
                        var effect = effectBuffer[i];
                        
                        // Process shield effects
                        if (effect.effectType == GameEffect.EffectType.Shield)
                        {
                            if (!IsEffectExpired(effect, currentTime))
                            {
                                totalShieldAmount += effect.primaryValue;
                            }
                            else
                            {
                                // Remove expired shield
                                effectBuffer.RemoveAt(i);
                                SpawnShieldBreakEffect(ecb, entity);
                            }
                        }
                    }
                    
                    // Update shield component if it exists
                    if (HasComponent<ShieldData>(entity))
                    {
                        var shieldData = GetComponent<ShieldData>(entity);
                        shieldData.currentShield = totalShieldAmount;
                        ecb.SetComponent(entity, shieldData);
                    }
                    else if (totalShieldAmount > dfloat.Zero)
                    {
                        // Add shield component if shields are active
                        ecb.AddComponent(entity, new ShieldData
                        {
                            currentShield = totalShieldAmount,
                            maxShield = totalShieldAmount
                        });
                    }
                    
                }).WithoutBurst().Run();
        }
        
        private void ProcessCombatStateEffects(dfloat currentTime, EntityCommandBuffer ecb)
        {
            Entities
                .WithAll<AttackData>()
                .ForEach((Entity entity, DynamicBuffer<GameEffect> effectBuffer, ref AttackData attackData) =>
                {
                    // Reset combat state flags
                    attackData.combatState &= ~(CombatState.Stunned | CombatState.Disarmed | CombatState.Silenced);
                    
                    for (int i = effectBuffer.Length - 1; i >= 0; i--)
                    {
                        var effect = effectBuffer[i];
                        
                        if (!IsEffectExpired(effect, currentTime))
                        {
                            // Apply combat state effects
                            switch (effect.effectType)
                            {
                                case GameEffect.EffectType.Stun:
                                    attackData.combatState |= CombatState.Stunned;
                                    break;
                                    
                                case GameEffect.EffectType.Root:
                                    // Root would affect movement, handled by movement systems
                                    break;
                                    
                                // Add more combat state effects as needed
                            }
                        }
                        else
                        {
                            // Remove expired effects
                            effectBuffer.RemoveAt(i);
                        }
                    }
                }).WithoutBurst().Run();
        }
        
        private bool ShouldTickEffect(GameEffect effect, dfloat currentTime)
        {
            if (effect.tickInterval <= dfloat.Zero)
                return false;
                
            return (currentTime - effect.lastTickTime) >= effect.tickInterval;
        }
        
        private bool IsEffectExpired(GameEffect effect, dfloat currentTime)
        {
            return (currentTime - effect.startTime) >= effect.duration;
        }
        
        private void SpawnDOTEffect(EntityCommandBuffer ecb, Entity target, GameEffect.EffectType effectType, dfloat damage)
        {
            // Spawn visual effect based on DOT type
            if (HasComponent<SimulationTransform>(target))
            {
                var transform = GetComponent<SimulationTransform>(target);
                
                // Create different effects for different DOT types
                switch (effectType)
                {
                    case GameEffect.EffectType.Poison:
                        SpawnPoisonEffect(ecb, transform.position);
                        break;
                    case GameEffect.EffectType.Burn:
                        SpawnBurnEffect(ecb, transform.position);
                        break;
                    case GameEffect.EffectType.Bleed:
                        SpawnBleedEffect(ecb, transform.position);
                        break;
                }
            }
        }
        
        private void SpawnHealingEffect(EntityCommandBuffer ecb, Entity target, dfloat healAmount, dfloat currentTime)
        {
            if (HasComponent<SimulationTransform>(target))
            {
                var transform = GetComponent<SimulationTransform>(target);
                // Create healing visual effect at target position
                CreateFloatingText(ecb, transform.position, healAmount, FloatingTextType.Healing, currentTime);
            }
        }
        
        private void SpawnShieldBreakEffect(EntityCommandBuffer ecb, Entity target)
        {
            if (HasComponent<SimulationTransform>(target))
            {
                var transform = GetComponent<SimulationTransform>(target);
                // Create shield break visual effect
            }
        }
        
        private void SpawnPoisonEffect(EntityCommandBuffer ecb, dfloat3 position)
        {
            // Create poison cloud/bubble effect
        }
        
        private void SpawnBurnEffect(EntityCommandBuffer ecb, dfloat3 position)
        {
            // Create fire/burn effect
        }
        
        private void SpawnBleedEffect(EntityCommandBuffer ecb, dfloat3 position)
        {
            // Create blood/bleed effect
        }
        
        private void CreateFloatingText(EntityCommandBuffer ecb, dfloat3 position, dfloat value, FloatingTextType textType, dfloat currentTime)
        {
            // Create floating text entity for damage/healing numbers
            var textEntity = ecb.CreateEntity();
            ecb.AddComponent(textEntity, new FloatingTextData
            {
                position = position,
                value = value,
                textType = textType,
                lifetime = (dfloat)2.0f,
                spawnTime = currentTime
            });
        }
        
        private void HandleDOTDeath(EntityCommandBuffer ecb, Entity target, int sourceId, dfloat currentTime)
        {
            // Handle death from damage over time
            ecb.AddComponent(target, new DeathData
            {
                deathTime = currentTime,
                killer = Entity.Null // Would need to track source entity
            });
        }
    }
    
    /// <summary>
    /// Component for shield data
    /// </summary>
    public struct ShieldData : IComponentData
    {
        public dfloat currentShield;
        public dfloat maxShield;
        public dfloat regenRate;
        public dfloat regenDelay;
        public dfloat lastDamageTime;
    }
    
    /// <summary>
    /// Component for floating text effects
    /// </summary>
    public struct FloatingTextData : IComponentData
    {
        public dfloat3 position;
        public dfloat value;
        public FloatingTextType textType;
        public dfloat lifetime;
        public dfloat spawnTime;
    }
    
    /// <summary>
    /// Types of floating text
    /// </summary>
    public enum FloatingTextType : byte
    {
        Damage = 0,
        CriticalDamage = 1,
        Healing = 2,
        Shield = 3,
        Miss = 4,
        Immune = 5
    }
}
