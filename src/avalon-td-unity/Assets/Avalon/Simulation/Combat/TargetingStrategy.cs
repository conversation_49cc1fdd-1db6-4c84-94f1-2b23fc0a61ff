﻿namespace Avalon.Simulation.Combat
{
    /// <summary>
    /// Different targeting strategies for selecting attack targets
    /// </summary>
    public enum TargetingStrategy : byte
    {
        /// <summary>Target the nearest enemy</summary>
        Nearest = 0,
        
        /// <summary>Target the farthest enemy</summary>
        Farthest = 1,
        
        /// <summary>Target the enemy with highest health</summary>
        HighestHealth = 2,
        
        /// <summary>Target the enemy with lowest health</summary>
        LowestHealth = 3,
        
        /// <summary>Target the first enemy in path</summary>
        First = 4,
        
        /// <summary>Target the last enemy in path</summary>
        Last = 5,
        
        /// <summary>Target the strongest enemy (highest total stats)</summary>
        Strongest = 6,
        
        /// <summary>Target the weakest enemy (lowest total stats)</summary>
        Weakest = 7,
        
        /// <summary>Target randomly within range</summary>
        Random = 8
    }
}