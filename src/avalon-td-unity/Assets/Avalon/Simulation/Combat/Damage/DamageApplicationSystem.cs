using Avalon.Simulation;
using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Combat.Projectiles;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Combat.Damage
{
    /// <summary>
    /// System that processes damage application to entities
    /// Handles damage calculation, armor reduction, critical hits, and status effect application
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldCombatSystemGroup))]
    [UpdateAfter(typeof(ProjectileCleanupSystem))]
    public partial class DamageApplicationSystem : SystemBase
    {
        private EntityQuery damageQuery;
        private EntityQuery fixedTimestepQuery;
        private EndSimulationEntityCommandBufferSystem ecbSystem;
        
        protected override void OnCreate()
        {
            // Query for damage events to process
            damageQuery = GetEntityQuery(
                ComponentType.ReadOnly<DamageData>(),
                ComponentType.Exclude<Disabled>()
            );
            
            // Query for fixed timestep
            fixedTimestepQuery = GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());
            
            // Get ECB system for deferred operations
            ecbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();

            // Only run when there are damage events to process
            RequireForUpdate(damageQuery);
            RequireForUpdate(fixedTimestepQuery);
        }
        
        protected override void OnUpdate()
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;
            
            dfloat currentTime = fixedTimestep.currentTime;
            var ecb = ecbSystem.CreateCommandBuffer();
            
            // Process all damage events
            Entities
                .WithAll<DamageData>()
                .ForEach((Entity damageEntity, in DamageData damageData) =>
                {
                    // Apply damage to target
                    ApplyDamageToTarget(ecb, damageData, currentTime);
                    
                    // Apply life steal to source if applicable
                    if (damageData.HasLifeSteal() && HasComponent<UnitStats>(damageData.sourceEntity))
                    {
                        ApplyLifeSteal(ecb, damageData);
                    }
                    
                    // Destroy the damage event entity
                    ecb.DestroyEntity(damageEntity);
                    
                }).WithoutBurst().Run();
            
            ecbSystem.AddJobHandleForProducer(Dependency);
        }
        
        private void ApplyDamageToTarget(EntityCommandBuffer ecb, DamageData damageData, dfloat currentTime)
        {
            Entity target = damageData.targetEntity;
            
            // Check if target still exists and is alive
            if (!HasComponent<UnitStats>(target))
                return;
                
            var targetStats = GetComponent<UnitStats>(target);
            if (targetStats.currentHealth <= dfloat.Zero)
                return; // Target is already dead
            
            // Calculate final damage
            dfloat armor = GetEffectiveArmor(target);
            dfloat resistance = GetEffectiveResistance(target, damageData.damageType);
            dfloat finalDamage = CalculateFinalDamage(damageData, armor, resistance);
            
            // Apply damage to health
            targetStats.currentHealth = dmath.max(dfloat.Zero, targetStats.currentHealth - finalDamage);
            ecb.SetComponent(target, targetStats);
            
            // Apply status effects if any
            if (damageData.HasStatusEffects())
            {
                ApplyStatusEffects(ecb, target, damageData, currentTime);
            }
            
            // Apply knockback if specified
            if (damageData.knockbackForce > dfloat.Zero)
            {
                ApplyKnockback(ecb, target, damageData);
            }
            
            // Spawn damage number visual effect
            SpawnDamageNumber(ecb, damageData.damagePosition, finalDamage, damageData.isCritical);
            
            // Spawn hit effect if specified
            if (damageData.hitEffectPrefab != Entity.Null)
            {
                SpawnHitEffect(ecb, damageData.hitEffectPrefab, damageData.damagePosition, damageData.effectScale);
            }
            
            // Check if target died
            if (targetStats.currentHealth <= dfloat.Zero)
            {
                HandleTargetDeath(ecb, target, damageData.sourceEntity, currentTime);
            }
        }
        
        private dfloat GetEffectiveArmor(Entity target)
        {
            dfloat armor = dfloat.Zero;
            
            // Get base armor from UnitStats
            if (HasComponent<UnitStats>(target))
            {
                // UnitStats doesn't have armor field yet, would need to add it
                // For now, assume no armor
                armor = dfloat.Zero;
            }
            
            // Apply armor modifiers from effects
            if (HasComponent<HealthEffects>(target))
            {
                var healthEffects = GetComponent<HealthEffects>(target);
                // Note: damageReduction is handled separately in CalculateFinalDamage
                // Armor calculation doesn't need modification here
            }
            
            return armor;
        }
        
        private dfloat GetEffectiveResistance(Entity target, DamageType damageType)
        {
            // TODO: Implement resistance calculation based on target and damage type
            _ = target; // Suppress unused parameter warning
            _ = damageType; // Suppress unused parameter warning
            dfloat resistance = dfloat.Zero;
            
            // Get resistance based on damage type
            switch (damageType)
            {
                case DamageType.Physical:
                    // Physical resistance would come from armor
                    break;
                case DamageType.Magical:
                    // Magical resistance would be a separate stat
                    break;
                case DamageType.True:
                    // True damage ignores all resistance
                    resistance = dfloat.Zero;
                    break;
                default:
                    break;
            }
            
            return resistance;
        }
        
        private dfloat CalculateFinalDamage(DamageData damageData, dfloat armor, dfloat resistance)
        {
            dfloat damage = damageData.baseDamage;
            
            // Apply critical hit multiplier
            if (damageData.isCritical)
            {
                damage *= damageData.criticalMultiplier;
            }
            
            // Apply damage multiplier
            damage *= damageData.damageMultiplier;
            
            // Apply armor reduction if not ignored
            if (!damageData.ignoresArmor && !damageData.isTrueDamage)
            {
                dfloat effectiveArmor = armor * (dfloat.One - damageData.armorPenetration);
                dfloat armorReduction = effectiveArmor / (effectiveArmor + (dfloat)100);
                damage *= dfloat.One - armorReduction;
            }
            
            // Apply resistance if not true damage
            if (!damageData.isTrueDamage)
            {
                dfloat effectiveResistance = dmath.max(dfloat.Zero, resistance - damageData.resistanceReduction);
                damage *= dfloat.One - effectiveResistance;
            }
            
            // Ensure minimum damage
            return dmath.max(damage, dfloat.Zero);
        }
        
        private void ApplyStatusEffects(EntityCommandBuffer ecb, Entity target, DamageData damageData, dfloat currentTime)
        {
            // Apply stun effect
            if (damageData.stunDuration > dfloat.Zero)
            {
                ApplyStunEffect(ecb, target, damageData.stunDuration, currentTime);
            }
            
            // Apply slow effect
            if (damageData.slowDuration > dfloat.Zero && damageData.slowAmount > dfloat.Zero)
            {
                ApplySlowEffect(ecb, target, damageData.slowAmount, damageData.slowDuration, currentTime);
            }
            
            // Apply damage over time effect
            if (damageData.dotDuration > dfloat.Zero && damageData.dotDamage > dfloat.Zero)
            {
                ApplyDamageOverTimeEffect(ecb, target, damageData.dotDamage, damageData.dotDuration, 
                    damageData.dotTickRate, damageData.dotType, currentTime);
            }
        }
        
        private void ApplyStunEffect(EntityCommandBuffer ecb, Entity target, dfloat duration, dfloat currentTime)
        {
            // Use the existing GameEffect system to apply stun
            if (HasBuffer<GameEffect>(target))
            {
                var effectBuffer = GetBuffer<GameEffect>(target);
                GameEffectUtils.ApplyStunEffect(effectBuffer, duration, currentTime, 0);
            }
        }

        private void ApplySlowEffect(EntityCommandBuffer ecb, Entity target, dfloat slowAmount, dfloat duration, dfloat currentTime)
        {
            // Use the existing GameEffect system to apply slow
            if (HasBuffer<GameEffect>(target))
            {
                var effectBuffer = GetBuffer<GameEffect>(target);
                GameEffectUtils.ApplySlowEffect(effectBuffer, slowAmount, duration, currentTime, 0);
            }
        }

        private void ApplyDamageOverTimeEffect(EntityCommandBuffer ecb, Entity target, dfloat damage, dfloat duration,
            dfloat tickRate, DamageType damageType, dfloat currentTime)
        {
            // Use the existing GameEffect system to apply DOT
            if (HasBuffer<GameEffect>(target))
            {
                var effectBuffer = GetBuffer<GameEffect>(target);
                GameEffectUtils.ApplyDamageOverTimeEffect(effectBuffer, damage, duration, tickRate, currentTime, 0);
            }
        }
        
        private void ApplyKnockback(EntityCommandBuffer ecb, Entity target, DamageData damageData)
        {
            // TODO: Implement knockback force application
            _ = ecb; // Suppress unused parameter warning
            _ = target; // Suppress unused parameter warning
            _ = damageData; // Suppress unused parameter warning
            // Apply knockback force to target
            // This would typically modify velocity or add a knockback component
            if (HasComponent<FlowFieldFollower>(target))
            {
                // For now, just apply a temporary velocity modification
                // In a full implementation, this would be handled by a knockback system
            }
        }
        
        private void ApplyLifeSteal(EntityCommandBuffer ecb, DamageData damageData)
        {
            Entity source = damageData.sourceEntity;
            dfloat healAmount = damageData.CalculateLifeSteal();
            
            if (healAmount > dfloat.Zero && HasComponent<UnitStats>(source))
            {
                var sourceStats = GetComponent<UnitStats>(source);
                sourceStats.currentHealth = dmath.min(sourceStats.maxHealth, sourceStats.currentHealth + healAmount);
                ecb.SetComponent(source, sourceStats);
                
                // Spawn healing visual effect
                if (HasComponent<SimulationTransform>(source))
                {
                    var transform = GetComponent<SimulationTransform>(source);
                    SpawnHealingEffect(ecb, transform.position, healAmount);
                }
            }
        }
        
        private void SpawnDamageNumber(EntityCommandBuffer ecb, dfloat3 position, dfloat damage, bool isCritical)
        {
            // TODO: Implement damage number visual creation
            _ = ecb; // Suppress unused parameter warning
            _ = position; // Suppress unused parameter warning
            _ = damage; // Suppress unused parameter warning
            _ = isCritical; // Suppress unused parameter warning
            // Create damage number visual entity
            // This would typically be handled by a UI/visual effects system
            // For now, just log the damage (in a real implementation, this would create a visual entity)
        }
        
        private void SpawnHitEffect(EntityCommandBuffer ecb, Entity effectPrefab, dfloat3 position, dfloat3 scale)
        {
            var effectEntity = ecb.Instantiate(effectPrefab);
            ecb.SetComponent(effectEntity, new SimulationTransform
            {
                position = position,
                rotation = dquaternion.identity,
                scale = scale.x // Assuming uniform scale
            });
        }
        
        private void SpawnHealingEffect(EntityCommandBuffer ecb, dfloat3 position, dfloat healAmount)
        {
            // TODO: Implement healing effect visual creation
            _ = ecb; // Suppress unused parameter warning
            _ = position; // Suppress unused parameter warning
            _ = healAmount; // Suppress unused parameter warning
            // Create healing visual effect
            // Similar to damage numbers but with different color/style
        }
        
        private void HandleTargetDeath(EntityCommandBuffer ecb, Entity target, Entity killer, dfloat currentTime)
        {
            // Handle target death
            // This could include:
            // - Dropping loot
            // - Awarding experience/currency to killer
            // - Playing death animation
            // - Spawning death effects
            
            // For now, just mark the entity for destruction
            ecb.AddComponent<Disabled>(target);
            
            // Could also add a death component for delayed cleanup
            ecb.AddComponent(target, new DeathData
            {
                deathTime = currentTime,
                killer = killer
            });
        }
    }
    
    /// <summary>
    /// Component to mark entities that have died
    /// </summary>
    public struct DeathData : IComponentData
    {
        public dfloat deathTime;
        public Entity killer;
        public bool hasDeathEffect;
        public Entity deathEffectPrefab;
    }
}
