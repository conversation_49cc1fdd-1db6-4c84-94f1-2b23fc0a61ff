using Avalon.Simulation.Combat.Core;
using Avalon.Simulation.Effects;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Collections;

namespace Avalon.Simulation.Combat.Damage
{
    /// <summary>
    /// Utility functions for damage calculations and application
    /// Provides helper methods for damage types, resistances, and special damage effects
    /// </summary>
    public static class DamageUtils
    {
        /// <summary>
        /// Calculate damage reduction from armor
        /// </summary>
        public static dfloat CalculateArmorReduction(dfloat armor, dfloat armorPenetration)
        {
            dfloat effectiveArmor = armor * (dfloat.One - dmath.clamp(armorPenetration, dfloat.Zero, dfloat.One));
            return effectiveArmor / (effectiveArmor + (dfloat)100);
        }

        /// <summary>
        /// Calculate damage reduction from armor with no armor penetration
        /// </summary>
        public static dfloat CalculateArmorReduction(dfloat armor)
        {
            return CalculateArmorReduction(armor, dfloat.Zero);
        }
        
        /// <summary>
        /// Calculate final damage after all modifiers
        /// </summary>
        public static dfloat CalculateFinalDamage(dfloat baseDamage, dfloat armor, dfloat resistance, 
            dfloat armorPenetration, dfloat criticalMultiplier, bool isCritical, bool ignoreArmor, bool isTrueDamage)
        {
            dfloat damage = baseDamage;
            
            // Apply critical hit
            if (isCritical)
            {
                damage *= criticalMultiplier;
            }
            
            // Apply armor reduction
            if (!ignoreArmor && !isTrueDamage)
            {
                dfloat armorReduction = CalculateArmorReduction(armor, armorPenetration);
                damage *= (dfloat.One - armorReduction);
            }
            
            // Apply resistance
            if (!isTrueDamage)
            {
                damage *= (dfloat.One - dmath.clamp(resistance, dfloat.Zero, dfloat.One));
            }
            
            return dmath.max(damage, dfloat.Zero);
        }
        
        /// <summary>
        /// Calculate critical hit chance with modifiers
        /// </summary>
        public static bool RollCriticalHit(dfloat baseCritChance, dfloat critChanceBonus, uint randomSeed)
        {
            dfloat totalCritChance = dmath.clamp(baseCritChance + critChanceBonus, dfloat.Zero, dfloat.One);
            var random = new Unity.Mathematics.Random(randomSeed);
            return random.NextFloat() < (float)totalCritChance;
        }
        
        /// <summary>
        /// Calculate damage over time tick damage
        /// </summary>
        public static dfloat CalculateDOTTickDamage(dfloat totalDamage, dfloat duration, dfloat tickRate)
        {
            if (duration <= dfloat.Zero || tickRate <= dfloat.Zero)
                return dfloat.Zero;
                
            dfloat totalTicks = duration * tickRate;
            return totalDamage / totalTicks;
        }
        
        /// <summary>
        /// Calculate area of effect damage with falloff
        /// </summary>
        public static dfloat CalculateAOEDamage(dfloat baseDamage, dfloat distance, dfloat maxRadius, dfloat falloffPercent)
        {
            if (distance > maxRadius)
                return dfloat.Zero;
                
            if (falloffPercent <= dfloat.Zero)
                return baseDamage; // No falloff
                
            dfloat falloffRatio = distance / maxRadius;
            dfloat damageMultiplier = dfloat.One - (falloffRatio * falloffPercent);
            
            return baseDamage * dmath.max(damageMultiplier, dfloat.Zero);
        }
        
        /// <summary>
        /// Calculate life steal healing amount
        /// </summary>
        public static dfloat CalculateLifeSteal(dfloat damageDealt, dfloat lifeStealPercent, dfloat flatLifeSteal)
        {
            dfloat percentHealing = damageDealt * dmath.clamp(lifeStealPercent, dfloat.Zero, dfloat.One);
            return percentHealing + flatLifeSteal;
        }
        
        /// <summary>
        /// Get damage type effectiveness against target
        /// </summary>
        public static dfloat GetDamageTypeEffectiveness(DamageType damageType, DamageType targetWeakness, DamageType targetResistance)
        {
            if (damageType == targetWeakness)
                return (dfloat)1.5f; // 50% more damage
            else if (damageType == targetResistance)
                return (dfloat)0.5f; // 50% less damage
            else
                return dfloat.One; // Normal damage
        }
        
        /// <summary>
        /// Calculate chain damage reduction
        /// </summary>
        public static dfloat CalculateChainDamage(dfloat baseDamage, int chainIndex, dfloat damageReductionPerChain)
        {
            dfloat reductionMultiplier = dmath.pow(dfloat.One - damageReductionPerChain, (dfloat)chainIndex);
            return baseDamage * reductionMultiplier;
        }
        
        /// <summary>
        /// Calculate pierce damage reduction
        /// </summary>
        public static dfloat CalculatePierceDamage(dfloat baseDamage, int pierceIndex, dfloat damageReductionPerPierce)
        {
            dfloat reductionMultiplier = dmath.pow(dfloat.One - damageReductionPerPierce, (dfloat)pierceIndex);
            return baseDamage * reductionMultiplier;
        }
        
        /// <summary>
        /// Create instant damage data
        /// </summary>
        public static DamageData CreateInstantDamage(Entity source, Entity target, dfloat damage, dfloat3 position, 
            DamageType damageType = DamageType.Physical)
        {
            return new DamageData
            {
                sourceEntity = source,
                targetEntity = target,
                damagePosition = position,
                baseDamage = damage,
                finalDamage = damage,
                damageType = damageType,
                criticalMultiplier = dfloat.One,
                damageMultiplier = dfloat.One,
                isCritical = false,
                ignoresArmor = false,
                isTrueDamage = false
            };
        }
        
        /// <summary>
        /// Create critical hit damage data
        /// </summary>
        public static DamageData CreateCriticalDamage(Entity source, Entity target, dfloat damage, dfloat critMultiplier, 
            dfloat3 position, DamageType damageType = DamageType.Physical)
        {
            var damageData = CreateInstantDamage(source, target, damage, position, damageType);
            damageData.isCritical = true;
            damageData.criticalMultiplier = critMultiplier;
            return damageData;
        }
        
        /// <summary>
        /// Create damage over time data
        /// </summary>
        public static DamageData CreateDOTDamage(Entity source, Entity target, dfloat totalDamage, dfloat duration, 
            dfloat tickRate, dfloat3 position, DamageType damageType = DamageType.Poison)
        {
            var damageData = CreateInstantDamage(source, target, dfloat.Zero, position, damageType);
            damageData.dotDamage = CalculateDOTTickDamage(totalDamage, duration, tickRate);
            damageData.dotDuration = duration;
            damageData.dotTickRate = tickRate;
            damageData.dotType = damageType;
            return damageData;
        }
        
        /// <summary>
        /// Create area of effect damage data
        /// </summary>
        public static DamageData CreateAOEDamage(Entity source, Entity target, dfloat baseDamage, dfloat distance, 
            dfloat maxRadius, dfloat falloff, dfloat3 position, DamageType damageType = DamageType.Physical)
        {
            dfloat aoeDamage = CalculateAOEDamage(baseDamage, distance, maxRadius, falloff);
            return CreateInstantDamage(source, target, aoeDamage, position, damageType);
        }
        
        /// <summary>
        /// Apply damage modifiers from combat effects
        /// </summary>
        public static dfloat ApplyCombatEffectModifiers(dfloat baseDamage, CombatEffects combatEffects)
        {
            dfloat damage = baseDamage;
            damage += combatEffects.additiveDamageBonus;
            damage *= combatEffects.damageMultiplier;
            return dmath.max(damage, dfloat.Zero);
        }
        
        /// <summary>
        /// Check if damage should be blocked by shields
        /// </summary>
        public static bool IsBlockedByShield(DamageData damageData, dfloat shieldAmount)
        {
            return !damageData.ignoresShields && shieldAmount > dfloat.Zero;
        }
        
        /// <summary>
        /// Calculate shield absorption
        /// </summary>
        public static dfloat CalculateShieldAbsorption(dfloat incomingDamage, dfloat shieldAmount)
        {
            return dmath.min(incomingDamage, shieldAmount);
        }
        
        /// <summary>
        /// Get random damage variance
        /// </summary>
        public static dfloat ApplyDamageVariance(dfloat baseDamage, dfloat variancePercent, uint randomSeed)
        {
            if (variancePercent <= dfloat.Zero)
                return baseDamage;
                
            var random = new Unity.Deterministic.Mathematics.Random(randomSeed);
            dfloat variance = random.Nextdfloat(-dfloat.One, dfloat.One) * variancePercent;
            return baseDamage * (dfloat.One + variance);
        }
        
        /// <summary>
        /// Calculate elemental damage bonus
        /// </summary>
        public static dfloat CalculateElementalBonus(DamageType attackType, DamageType targetWeakness, dfloat bonusMultiplier)
        {
            if (attackType == targetWeakness)
                return bonusMultiplier;
            return dfloat.One;
        }
        
        /// <summary>
        /// Check if target is immune to damage type
        /// </summary>
        public static bool IsImmuneToType(DamageType damageType, uint immunityMask)
        {
            return (immunityMask & (1u << (int)damageType)) != 0;
        }
        
        /// <summary>
        /// Calculate total damage dealt in an area
        /// </summary>
        public static dfloat CalculateTotalAOEDamage(dfloat baseDamage, NativeArray<dfloat> distances, 
            dfloat maxRadius, dfloat falloff)
        {
            dfloat totalDamage = dfloat.Zero;
            
            for (int i = 0; i < distances.Length; i++)
            {
                if (distances[i] <= maxRadius)
                {
                    totalDamage += CalculateAOEDamage(baseDamage, distances[i], maxRadius, falloff);
                }
            }
            
            return totalDamage;
        }
    }
}
