using Avalon.Simulation.Movement;
using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Component that manages movement-related effects and modifiers
    /// Replaces the SpeedModifiers component with a more comprehensive system
    /// </summary>
    public struct MovementEffects : IComponentData
    {
        // Base values from UnitStats (original values before any modifiers)
        public dfloat baseMaxSpeed;
        public dfloat baseMinSpeed;
        public dfloat baseAcceleration;
        public dfloat baseDeceleration;
        public dfloat baseRotationSpeed;

        // Current effective multipliers (calculated from active effects)
        public dfloat speedMultiplier;
        public dfloat accelerationMultiplier;
        public dfloat decelerationMultiplier;
        public dfloat rotationSpeedMultiplier;

        // Terrain and environmental modifiers
        public dfloat terrainSpeedMultiplier;
        public dfloat environmentalSpeedMultiplier;

        // Additive modifiers (flat bonuses/penalties)
        public dfloat additiveSpeedBonus;
        public dfloat additiveAccelerationBonus;
        public dfloat additiveDecelerationBonus;

        // Movement state flags
        public bool isSlowed;           // Movement speed reduced
        public bool isHasted;           // Movement speed increased
        public bool isStunned;          // Cannot move at all
        public bool isRooted;           // Cannot move but can rotate/act
        public bool isFeared;           // Forced movement in random direction
        public bool isCharmed;          // Movement controlled by external source
        public bool isKnockedDown;      // Temporarily immobilized
        public bool isSlipping;         // Reduced control over movement

        // Effect intensity tracking (for visual feedback)
        public dfloat slowIntensity;    // 0-1, how slowed the unit is
        public dfloat hasteIntensity;   // 0-1, how hasted the unit is
        public dfloat stunDuration;     // Remaining stun duration
        public dfloat rootDuration;     // Remaining root duration

        // Movement type modifiers
        public bool canMoveWhileAttacking;
        public bool canMoveWhileCasting;
        public bool ignoresUnitCollision;
        public bool ignoresTerrainEffects;

        /// <summary>
        /// Get the final effective max speed after all modifiers
        /// </summary>
        public dfloat GetEffectiveMaxSpeed()
        {
            if (isStunned || isRooted) return dfloat.Zero;
            
            var speed = baseMaxSpeed;
            speed += additiveSpeedBonus;
            speed *= speedMultiplier;
            speed *= terrainSpeedMultiplier;
            speed *= environmentalSpeedMultiplier;
            
            return dmath.max(speed, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective min speed after all modifiers
        /// </summary>
        public dfloat GetEffectiveMinSpeed()
        {
            if (isStunned || isRooted) return dfloat.Zero;
            
            var speed = baseMinSpeed;
            speed *= speedMultiplier;
            speed *= terrainSpeedMultiplier;
            speed *= environmentalSpeedMultiplier;
            
            return dmath.max(speed, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective acceleration after all modifiers
        /// </summary>
        public dfloat GetEffectiveAcceleration()
        {
            if (isStunned) return dfloat.Zero;
            
            var acceleration = baseAcceleration;
            acceleration += additiveAccelerationBonus;
            acceleration *= accelerationMultiplier;
            
            return dmath.max(acceleration, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective deceleration after all modifiers
        /// </summary>
        public dfloat GetEffectiveDeceleration()
        {
            if (isStunned) return dfloat.Zero;
            
            var deceleration = baseDeceleration;
            deceleration += additiveDecelerationBonus;
            deceleration *= decelerationMultiplier;
            
            return dmath.max(deceleration, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective rotation speed after all modifiers
        /// </summary>
        public dfloat GetEffectiveRotationSpeed()
        {
            if (isStunned) return dfloat.Zero;
            
            var rotationSpeed = baseRotationSpeed;
            rotationSpeed *= rotationSpeedMultiplier;
            
            return dmath.max(rotationSpeed, dfloat.Zero);
        }

        /// <summary>
        /// Check if the unit can move at all
        /// </summary>
        public bool CanMove()
        {
            return !isStunned && !isRooted && !isKnockedDown;
        }

        /// <summary>
        /// Check if the unit can rotate
        /// </summary>
        public bool CanRotate()
        {
            return !isStunned && !isKnockedDown;
        }

        /// <summary>
        /// Check if the unit has any movement impairing effects
        /// </summary>
        public bool HasMovementImpairment()
        {
            return isSlowed || isStunned || isRooted || isKnockedDown || isSlipping;
        }

        /// <summary>
        /// Check if the unit has any movement enhancing effects
        /// </summary>
        public bool HasMovementEnhancement()
        {
            return isHasted || speedMultiplier > dfloat.One || additiveSpeedBonus > dfloat.Zero;
        }

        /// <summary>
        /// Reset all modifiers to default values
        /// </summary>
        public void ResetModifiers()
        {
            speedMultiplier = dfloat.One;
            accelerationMultiplier = dfloat.One;
            decelerationMultiplier = dfloat.One;
            rotationSpeedMultiplier = dfloat.One;
            terrainSpeedMultiplier = dfloat.One;
            environmentalSpeedMultiplier = dfloat.One;
            
            additiveSpeedBonus = dfloat.Zero;
            additiveAccelerationBonus = dfloat.Zero;
            additiveDecelerationBonus = dfloat.Zero;
            
            isSlowed = false;
            isHasted = false;
            isStunned = false;
            isRooted = false;
            isFeared = false;
            isCharmed = false;
            isKnockedDown = false;
            isSlipping = false;
            
            slowIntensity = dfloat.Zero;
            hasteIntensity = dfloat.Zero;
            stunDuration = dfloat.Zero;
            rootDuration = dfloat.Zero;
        }

        /// <summary>
        /// Initialize from UnitStats
        /// </summary>
        public static MovementEffects CreateFromUnitStats(UnitStats stats)
        {
            return new MovementEffects
            {
                baseMaxSpeed = stats.maxSpeed,
                baseMinSpeed = dfloat.Zero, // Default min speed
                baseAcceleration = stats.acceleration,
                baseDeceleration = stats.deceleration,
                baseRotationSpeed = stats.rotationSpeed,
                
                speedMultiplier = dfloat.One,
                accelerationMultiplier = dfloat.One,
                decelerationMultiplier = dfloat.One,
                rotationSpeedMultiplier = dfloat.One,
                terrainSpeedMultiplier = dfloat.One,
                environmentalSpeedMultiplier = dfloat.One,
                
                additiveSpeedBonus = dfloat.Zero,
                additiveAccelerationBonus = dfloat.Zero,
                additiveDecelerationBonus = dfloat.Zero,
                
                isSlowed = false,
                isHasted = false,
                isStunned = false,
                isRooted = false,
                isFeared = false,
                isCharmed = false,
                isKnockedDown = false,
                isSlipping = false,
                
                slowIntensity = dfloat.Zero,
                hasteIntensity = dfloat.Zero,
                stunDuration = dfloat.Zero,
                rootDuration = dfloat.Zero,
                
                canMoveWhileAttacking = false,
                canMoveWhileCasting = false,
                ignoresUnitCollision = false,
                ignoresTerrainEffects = false
            };
        }

        /// <summary>
        /// Initialize from FlowFieldFollower (for backward compatibility)
        /// </summary>
        public static MovementEffects CreateFromFlowFieldFollower(FlowFieldFollower follower)
        {
            return new MovementEffects
            {
                baseMaxSpeed = follower.maxSpeed,
                baseMinSpeed = follower.minSpeed,
                baseAcceleration = follower.acceleration,
                baseDeceleration = follower.deceleration,
                baseRotationSpeed = new dfloat(5.0f), // Default rotation speed
                
                speedMultiplier = dfloat.One,
                accelerationMultiplier = dfloat.One,
                decelerationMultiplier = dfloat.One,
                rotationSpeedMultiplier = dfloat.One,
                terrainSpeedMultiplier = dfloat.One,
                environmentalSpeedMultiplier = dfloat.One,
                
                additiveSpeedBonus = dfloat.Zero,
                additiveAccelerationBonus = dfloat.Zero,
                additiveDecelerationBonus = dfloat.Zero,
                
                isSlowed = false,
                isHasted = false,
                isStunned = false,
                isRooted = false,
                isFeared = false,
                isCharmed = false,
                isKnockedDown = false,
                isSlipping = false,
                
                slowIntensity = dfloat.Zero,
                hasteIntensity = dfloat.Zero,
                stunDuration = dfloat.Zero,
                rootDuration = dfloat.Zero,
                
                canMoveWhileAttacking = false,
                canMoveWhileCasting = false,
                ignoresUnitCollision = false,
                ignoresTerrainEffects = false
            };
        }
    }
}
