using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Simplified compatibility layer for applying effects using the new GameEffect system
    /// All legacy SpeedModifier system support has been removed
    /// </summary>
    public static class SpeedModifierCompatibility
    {
        /// <summary>
        /// Apply a speed effect using the new GameEffect system
        /// </summary>
        public static void ApplySpeedEffect(EntityManager entityManager, Entity entity,
            dfloat speedMultiplier, dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.ApplySpeedEffect(gameEffectBuffer, speedMultiplier, duration, currentTime, sourceId);
            }
        }

        /// <summary>
        /// Apply a slow effect using the new GameEffect system
        /// </summary>
        public static void ApplySlowEffect(EntityManager entityManager, Entity entity,
            dfloat slowAmount, dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.ApplySlowEffect(gameEffectBuffer, slowAmount, duration, currentTime, sourceId);
            }
        }

        /// <summary>
        /// Apply a haste effect using the new GameEffect system
        /// </summary>
        public static void ApplyHasteEffect(EntityManager entityManager, Entity entity,
            dfloat hasteAmount, dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.ApplyHasteEffect(gameEffectBuffer, hasteAmount, duration, currentTime, sourceId);
            }
        }

        /// <summary>
        /// Apply a stun effect using the new GameEffect system
        /// </summary>
        public static void ApplyStunEffect(EntityManager entityManager, Entity entity,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.ApplyStunEffect(gameEffectBuffer, duration, currentTime, sourceId);
            }
        }

        /// <summary>
        /// Remove effects from a specific source using the new GameEffect system
        /// </summary>
        public static void RemoveEffectsFromSource(EntityManager entityManager, Entity entity, int sourceId)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.RemoveEffectsFromSource(gameEffectBuffer, sourceId);
            }
        }

        /// <summary>
        /// Remove effects of a specific type using the new GameEffect system
        /// </summary>
        public static void RemoveEffectsOfType(EntityManager entityManager, Entity entity,
            GameEffect.EffectType effectType)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                GameEffectUtils.RemoveEffectsOfType(gameEffectBuffer, effectType);
            }
        }

        /// <summary>
        /// Check if entity has a specific effect type using the new GameEffect system
        /// </summary>
        public static bool HasEffectType(EntityManager entityManager, Entity entity,
            GameEffect.EffectType effectType, dfloat currentTime)
        {
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                return GameEffectUtils.HasEffectType(gameEffectBuffer, effectType, currentTime);
            }

            return false;
        }

        /// <summary>
        /// Get debug information about active effects using the new GameEffect system
        /// </summary>
        public static string GetEffectDebugInfo(EntityManager entityManager, Entity entity, dfloat currentTime)
        {
            var info = "";

            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(entity);
                var activeCount = GameEffectUtils.GetActiveEffectCount(gameEffectBuffer, currentTime);
                info += $"GameEffects: {activeCount} active\n";
            }

            return info;
        }

        /// <summary>
        /// Check if an entity is using the new GameEffect system
        /// </summary>
        public static bool IsUsingNewSystem(EntityManager entityManager, Entity entity)
        {
            return EffectComponentUtils.HasEffectComponents(entityManager, entity);
        }
    }
}
