using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Generic effect buffer element that can represent any type of duration-based effect
    /// </summary>
    public struct GameEffect : IBufferElementData
    {
        /// <summary>
        /// Categories of effects for processing optimization
        /// </summary>
        public enum EffectCategory : byte
        {
            Movement = 0,
            Combat = 1,
            Health = 2,
            Abilities = 3,
            Visual = 4
        }

        /// <summary>
        /// Specific effect types within each category
        /// </summary>
        public enum EffectType : byte
        {
            // Movement Effects (0-19)
            MovementSpeed = 0,
            Acceleration = 1,
            Deceleration = 2,
            Stun = 3,
            Root = 4,
            Slow = 5,
            Haste = 6,
            TerrainModifier = 7,

            // Combat Effects (20-39)
            AttackDamage = 20,
            AttackSpeed = 21,
            AttackRange = 22,
            CriticalChance = 23,
            CriticalDamage = 24,
            ArmorPenetration = 25,
            LifeSteal = 26,
            AttackBuff = 27,
            AttackDebuff = 28,

            // Health Effects (40-59)
            DamageOverTime = 40,
            HealOverTime = 41,
            Shield = 42,
            Regeneration = 43,
            MaxHealthModifier = 44,
            DamageReduction = 45,
            Vulnerability = 46,
            Poison = 47,
            Burn = 48,
            Bleed = 49,

            // Ability Effects (60-79)
            CooldownReduction = 60,
            ManaCostReduction = 61,
            AbilityRange = 62,
            AbilityDamage = 63,
            CastSpeed = 64,
            ManaRegeneration = 65,

            // Visual Effects (80-99)
            SizeModifier = 80,
            ColorTint = 81,
            Transparency = 82,
            ParticleEffect = 83
        }

        /// <summary>
        /// How effects of the same type should stack
        /// </summary>
        public enum StackingType : byte
        {
            None = 0,           // Cannot stack, new effect replaces old
            Additive = 1,       // Values add together
            Multiplicative = 2, // Values multiply together
            Refresh = 3,        // Refresh duration, keep highest value
            Independent = 4     // Each effect applies independently
        }

        // Core effect identification
        public EffectCategory category;
        public EffectType effectType;
        public StackingType stackingType;

        // Effect values
        public dfloat primaryValue;    // Main effect value (damage, multiplier, etc.)
        public dfloat secondaryValue;  // Secondary value (tick rate for DOT, etc.)
        public dfloat tertiaryValue;   // Additional value for complex effects

        // Timing information
        public dfloat duration;        // Total effect duration
        public dfloat startTime;       // When effect was applied
        public dfloat tickInterval;    // For periodic effects (DOT, HOT)
        public dfloat lastTickTime;    // Last time effect ticked

        // Stacking and source tracking
        public int sourceId;           // ID of the source that applied this effect
        public byte stackCount;        // Current stack count
        public byte maxStacks;         // Maximum stacks allowed
        public byte priority;          // Effect priority for conflicts

        // Flags for special behaviors
        public byte flags;             // Bitmask for special effect behaviors

        /// <summary>
        /// Effect behavior flags
        /// </summary>
        public static class EffectFlags
        {
            public const byte None = 0;
            public const byte Dispellable = 1 << 0;      // Can be removed by dispel effects
            public const byte Beneficial = 1 << 1;       // Is a positive effect (buff)
            public const byte Harmful = 1 << 2;          // Is a negative effect (debuff)
            public const byte Permanent = 1 << 3;        // Effect doesn't expire naturally
            public const byte Unique = 1 << 4;           // Only one instance can exist per source
            public const byte TickOnApplication = 1 << 5; // Tick immediately when applied
            public const byte ScalesWithLevel = 1 << 6;  // Effect scales with caster level
            public const byte IgnoreImmunity = 1 << 7;   // Bypasses immunity effects
        }

        /// <summary>
        /// Check if effect has expired
        /// </summary>
        public bool IsExpired(dfloat currentTime)
        {
            if ((flags & EffectFlags.Permanent) != 0) return false;
            return currentTime >= startTime + duration;
        }

        /// <summary>
        /// Check if effect should tick
        /// </summary>
        public bool ShouldTick(dfloat currentTime)
        {
            if (tickInterval <= dfloat.Zero) return false;
            return currentTime >= lastTickTime + tickInterval;
        }

        /// <summary>
        /// Check if effect has specific flag
        /// </summary>
        public bool HasFlag(byte flag)
        {
            return (flags & flag) != 0;
        }

        /// <summary>
        /// Get remaining duration
        /// </summary>
        public dfloat GetRemainingDuration(dfloat currentTime)
        {
            if ((flags & EffectFlags.Permanent) != 0) return dfloat.MAX_VALUE;
            var remaining = (startTime + duration) - currentTime;
            return dmath.max(remaining, dfloat.Zero);
        }

        /// <summary>
        /// Get effect progress (0-1)
        /// </summary>
        public dfloat GetProgress(dfloat currentTime)
        {
            if ((flags & EffectFlags.Permanent) != 0) return dfloat.Zero;
            if (duration <= dfloat.Zero) return dfloat.One;
            var elapsed = currentTime - startTime;
            return dmath.clamp(elapsed / duration, dfloat.Zero, dfloat.One);
        }
    }
}
