using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Example usage patterns for the GameEffect system
    /// Demonstrates common scenarios and best practices
    /// </summary>
    public static class EffectSystemExamples
    {
        /// <summary>
        /// Example: Creating a unit with the new effect system
        /// </summary>
        public static Entity CreateUnitWithEffects(EntityManager entityManager, UnitStats stats, float3 position)
        {
            var unit = entityManager.CreateEntity();
            
            // Add basic components
            entityManager.AddComponentData(unit, stats);
            entityManager.AddComponentData(unit, new SimulationTransform 
            { 
                position = new dfloat3((dfloat)position.x, (dfloat)position.y, (dfloat)position.z),
                rotation = dquaternion.identity,
                scale = dfloat.One
            });
            
            // Add effect system components
            EffectComponentUtils.AddEffectComponents(entityManager, unit, stats);
            
            return unit;
        }

        /// <summary>
        /// Example: Applying various movement effects
        /// </summary>
        public static void ApplyMovementEffectsExample(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasBuffer<GameEffect>(unit)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
            var currentTime = new dfloat(Time.time);
            
            // Apply a 50% slow for 5 seconds
            GameEffectUtils.ApplySlowEffect(effectBuffer, new dfloat(0.5f), new dfloat(5.0f), currentTime, 100);
            
            // Apply a 200% haste for 3 seconds (will stack with slow)
            GameEffectUtils.ApplyHasteEffect(effectBuffer, new dfloat(2.0f), new dfloat(3.0f), currentTime, 101);
            
            // Apply a 2-second stun
            GameEffectUtils.ApplyStunEffect(effectBuffer, new dfloat(2.0f), currentTime, 102);
            
            // Apply a root effect that prevents movement but allows rotation
            GameEffectUtils.ApplyRootEffect(effectBuffer, new dfloat(4.0f), currentTime, 103);
        }

        /// <summary>
        /// Example: Applying combat effects
        /// </summary>
        public static void ApplyCombatEffectsExample(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasBuffer<GameEffect>(unit)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
            var currentTime = new dfloat(Time.time);
            
            // Apply a 150% damage buff for 10 seconds
            GameEffectUtils.ApplyAttackDamageEffect(effectBuffer, new dfloat(1.5f), new dfloat(10.0f), currentTime, 200);
            
            // Apply a 200% attack speed buff for 8 seconds
            GameEffectUtils.ApplyAttackSpeedEffect(effectBuffer, new dfloat(2.0f), new dfloat(8.0f), currentTime, 201);
            
            // Apply a critical chance boost
            var critEffect = GameEffectUtils.CreateEffect(
                GameEffect.EffectCategory.Combat,
                GameEffect.EffectType.CriticalChance,
                new dfloat(0.25f), // +25% crit chance
                new dfloat(15.0f), // 15 seconds
                currentTime,
                202,
                GameEffect.StackingType.Additive,
                flags: GameEffect.EffectFlags.Beneficial | GameEffect.EffectFlags.Dispellable
            );
            GameEffectUtils.ApplyEffect(effectBuffer, critEffect);
        }

        /// <summary>
        /// Example: Applying health effects (DOT, HOT, shields)
        /// </summary>
        public static void ApplyHealthEffectsExample(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasBuffer<GameEffect>(unit)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
            var currentTime = new dfloat(Time.time);
            
            // Apply poison (10 damage per second for 8 seconds)
            GameEffectUtils.ApplyPoisonEffect(effectBuffer, new dfloat(10.0f), new dfloat(8.0f), currentTime, 300);
            
            // Apply healing over time (5 healing per second for 10 seconds)
            GameEffectUtils.ApplyHealOverTimeEffect(effectBuffer, new dfloat(5.0f), new dfloat(10.0f), 
                new dfloat(1.0f), currentTime, 301);
            
            // Apply a shield (100 shield points for 30 seconds)
            GameEffectUtils.ApplyShieldEffect(effectBuffer, new dfloat(100.0f), new dfloat(30.0f), currentTime, 302);
            
            // Apply damage reduction (25% damage reduction for 15 seconds)
            var damageReductionEffect = GameEffectUtils.CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.DamageReduction,
                new dfloat(0.25f),
                new dfloat(15.0f),
                currentTime,
                303,
                GameEffect.StackingType.Additive,
                flags: GameEffect.EffectFlags.Beneficial
            );
            GameEffectUtils.ApplyEffect(effectBuffer, damageReductionEffect);
        }

        /// <summary>
        /// Example: Creating a complex spell effect with multiple components
        /// </summary>
        public static void ApplyFireballSpellExample(EntityManager entityManager, Entity target)
        {
            if (!entityManager.HasBuffer<GameEffect>(target)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            var currentTime = new dfloat(Time.time);
            const int spellSourceId = 1000;
            
            // Immediate damage would be handled elsewhere, this is just the ongoing effects
            
            // Apply burn damage over time (15 damage per second for 6 seconds)
            var burnEffect = GameEffectUtils.CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.Burn,
                new dfloat(15.0f),
                new dfloat(6.0f),
                currentTime,
                spellSourceId,
                GameEffect.StackingType.Independent, // Multiple burns can stack
                maxStacks: 3,
                flags: GameEffect.EffectFlags.Harmful | GameEffect.EffectFlags.Dispellable
            );
            burnEffect.tickInterval = new dfloat(1.0f); // Tick every second
            burnEffect.lastTickTime = currentTime;
            GameEffectUtils.ApplyEffect(effectBuffer, burnEffect);
            
            // Apply movement slow (30% speed reduction for 4 seconds)
            GameEffectUtils.ApplySlowEffect(effectBuffer, new dfloat(0.7f), new dfloat(4.0f), currentTime, spellSourceId);
            
            // Apply attack speed reduction (50% attack speed for 3 seconds)
            GameEffectUtils.ApplyAttackSpeedEffect(effectBuffer, new dfloat(0.5f), new dfloat(3.0f), currentTime, spellSourceId);
        }

        /// <summary>
        /// Example: Creating a healing spell with multiple beneficial effects
        /// </summary>
        public static void ApplyHealingSpellExample(EntityManager entityManager, Entity target)
        {
            if (!entityManager.HasBuffer<GameEffect>(target)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            var currentTime = new dfloat(Time.time);
            const int spellSourceId = 2000;
            
            // Immediate healing would be handled elsewhere
            
            // Apply regeneration (8 healing per second for 12 seconds)
            GameEffectUtils.ApplyHealOverTimeEffect(effectBuffer, new dfloat(8.0f), new dfloat(12.0f), 
                new dfloat(1.0f), currentTime, spellSourceId);
            
            // Apply temporary max health boost (20% for 60 seconds)
            var healthBoostEffect = GameEffectUtils.CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.MaxHealthModifier,
                new dfloat(1.2f), // 120% of base health
                new dfloat(60.0f),
                currentTime,
                spellSourceId,
                GameEffect.StackingType.Multiplicative,
                flags: GameEffect.EffectFlags.Beneficial | GameEffect.EffectFlags.Dispellable
            );
            GameEffectUtils.ApplyEffect(effectBuffer, healthBoostEffect);
            
            // Apply damage resistance (15% damage reduction for 30 seconds)
            var resistanceEffect = GameEffectUtils.CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.DamageReduction,
                new dfloat(0.15f),
                new dfloat(30.0f),
                currentTime,
                spellSourceId,
                GameEffect.StackingType.Additive,
                flags: GameEffect.EffectFlags.Beneficial
            );
            GameEffectUtils.ApplyEffect(effectBuffer, resistanceEffect);
        }

        /// <summary>
        /// Example: Dispelling effects
        /// </summary>
        public static void DispelEffectsExample(EntityManager entityManager, Entity target)
        {
            if (!entityManager.HasBuffer<GameEffect>(target)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(target);
            
            // Remove all harmful dispellable effects
            GameEffectUtils.DispelEffects(effectBuffer, harmfulOnly: true);
            
            // Remove all effects from a specific source (e.g., when caster dies)
            GameEffectUtils.RemoveEffectsFromSource(effectBuffer, 1000);
            
            // Remove all poison effects
            GameEffectUtils.RemoveEffectsOfType(effectBuffer, GameEffect.EffectType.Poison);
        }

        /// <summary>
        /// Example: Checking for specific effects
        /// </summary>
        public static void CheckEffectsExample(EntityManager entityManager, Entity unit)
        {
            if (!entityManager.HasBuffer<GameEffect>(unit)) return;
            
            var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
            var currentTime = new dfloat(Time.time);
            
            // Check if unit is poisoned
            bool isPoisoned = GameEffectUtils.HasEffectType(effectBuffer, GameEffect.EffectType.Poison, currentTime);
            
            // Get the strongest slow effect
            var strongestSlow = GameEffectUtils.GetStrongestEffect(effectBuffer, GameEffect.EffectType.Slow, currentTime);
            if (strongestSlow.HasValue)
            {
                Debug.Log($"Unit has slow effect with {strongestSlow.Value.primaryValue} multiplier");
            }
            
            // Count active effects
            int activeEffectCount = GameEffectUtils.GetActiveEffectCount(effectBuffer, currentTime);
            Debug.Log($"Unit has {activeEffectCount} active effects");
        }

        /// <summary>
        /// Example: Using the compatibility layer for applying effects
        /// </summary>
        public static void CompatibilityExample(EntityManager entityManager, Entity unit)
        {
            // Check if unit is using the new GameEffect system
            if (SpeedModifierCompatibility.IsUsingNewSystem(entityManager, unit))
            {
                Debug.Log("Unit is using new GameEffect system");

                // Apply effects using compatibility layer
                var currentTime = new dfloat(Time.time);
                SpeedModifierCompatibility.ApplySlowEffect(entityManager, unit, new dfloat(0.5f), new dfloat(5.0f), currentTime, 999);
            }
        }

        /// <summary>
        /// Example: Performance monitoring
        /// </summary>
        public static void PerformanceMonitoringExample(EntityManager entityManager)
        {
            // Get system performance metrics
            string metrics = EffectDebugger.GetPerformanceMetrics(entityManager);
            Debug.Log(metrics);
            
            // Get system summary
            var currentTime = new dfloat(Time.time);
            string summary = EffectDebugger.GetSystemEffectSummary(entityManager, currentTime);
            Debug.Log(summary);
            
            // Find all entities with poison effects
            var poisonedEntities = EffectDebugger.FindEntitiesWithEffect(entityManager, GameEffect.EffectType.Poison, currentTime);
            Debug.Log($"Found {poisonedEntities.Length} poisoned entities");
        }
    }
}
