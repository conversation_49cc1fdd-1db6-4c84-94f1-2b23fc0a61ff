using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Job to initialize effect components from UnitStats
    /// Replaces the old SpeedModifier initialization with comprehensive effect system
    /// </summary>
    [BurstCompile]
    public partial struct InitializeEffectComponentsJob : IJobEntity
    {
        public void Execute(in UnitStats stats, ref FlowFieldFollower follower, 
            ref MovementEffects movementEffects, ref CombatEffects combatEffects, 
            ref HealthEffects healthEffects)
        {
            // Initialize movement effects from UnitStats
            movementEffects = MovementEffects.CreateFromUnitStats(stats);
            
            // Initialize combat effects from UnitStats
            combatEffects = CombatEffects.CreateFromUnitStats(stats);
            
            // Initialize health effects from UnitStats
            healthEffects = HealthEffects.CreateFromUnitStats(stats);
            
            // Copy movement stats from UnitStats to FlowFieldFollower (for backward compatibility)
            follower.maxSpeed = stats.maxSpeed;
            follower.acceleration = stats.acceleration;
            follower.deceleration = stats.deceleration;
        }
    }

    /// <summary>
    /// Job to initialize effect components with avoidance data
    /// </summary>
    [BurstCompile]
    public partial struct InitializeEffectComponentsWithAvoidanceJob : IJobEntity
    {
        public void Execute(in UnitStats stats, ref FlowFieldFollower follower, 
            ref AvoidanceData avoidance, ref MovementEffects movementEffects, 
            ref CombatEffects combatEffects, ref HealthEffects healthEffects)
        {
            // Initialize effect components from UnitStats
            movementEffects = MovementEffects.CreateFromUnitStats(stats);
            combatEffects = CombatEffects.CreateFromUnitStats(stats);
            healthEffects = HealthEffects.CreateFromUnitStats(stats);
            
            // Copy movement stats from UnitStats to FlowFieldFollower
            follower.maxSpeed = stats.maxSpeed;
            follower.acceleration = stats.acceleration;
            follower.deceleration = stats.deceleration;

            // Update avoidance data radius
            avoidance.radius = stats.radius;
        }
    }

    /// <summary>
    /// Job to update FlowFieldFollower from MovementEffects
    /// Used when effect components are modified and need to propagate to movement system
    /// </summary>
    [BurstCompile]
    public partial struct UpdateFollowerFromEffectsJob : IJobEntity
    {
        public void Execute(in MovementEffects movementEffects, ref FlowFieldFollower follower)
        {
            // Apply effective values from movement effects to follower
            follower.maxSpeed = movementEffects.GetEffectiveMaxSpeed();
            follower.minSpeed = movementEffects.GetEffectiveMinSpeed();
            follower.acceleration = movementEffects.GetEffectiveAcceleration();
            follower.deceleration = movementEffects.GetEffectiveDeceleration();

            // Ensure minimum values
            follower.maxSpeed = dmath.max(follower.maxSpeed, dfloat.Zero);
            follower.minSpeed = dmath.max(follower.minSpeed, dfloat.Zero);
            follower.acceleration = dmath.max(follower.acceleration, dfloat.Zero);
            follower.deceleration = dmath.max(follower.deceleration, dfloat.Zero);
        }
    }

    /// <summary>
    /// Job to synchronize UnitStats health with HealthEffects
    /// Ensures max health changes are properly applied
    /// </summary>
    [BurstCompile]
    public partial struct SynchronizeHealthStatsJob : IJobEntity
    {
        public void Execute(ref UnitStats stats, in HealthEffects healthEffects)
        {
            // Update max health from health effects
            var newMaxHealth = healthEffects.GetEffectiveMaxHealth();
            
            // If max health changed, adjust current health proportionally
            if (stats.maxHealth != newMaxHealth)
            {
                var healthRatio = stats.currentHealth / stats.maxHealth;
                stats.maxHealth = newMaxHealth;
                stats.currentHealth = stats.maxHealth * healthRatio;
            }
        }
    }
}
