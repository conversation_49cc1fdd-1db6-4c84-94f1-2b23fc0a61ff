using System.Text;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using UnityEngine;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Debugging utilities for visualizing and monitoring the effect system
    /// </summary>
    public static class EffectDebugger
    {
        /// <summary>
        /// Get detailed information about all effects on an entity
        /// </summary>
        public static string GetEntityEffectInfo(EntityManager entityManager, Entity entity, dfloat currentTime)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"=== Effect Debug Info for Entity {entity.Index} ===");

            // Check if entity has new effect system
            if (entityManager.HasBuffer<GameEffect>(entity))
            {
                sb.AppendLine("Using NEW GameEffect System:");
                var gameEffects = entityManager.GetBuffer<GameEffect>(entity);
                AppendGameEffectInfo(sb, gameEffects, currentTime);
            }

            // Show effect component states
            if (entityManager.HasComponent<MovementEffects>(entity))
            {
                sb.AppendLine("\n=== Movement Effects Component ===");
                var movementEffects = entityManager.GetComponentData<MovementEffects>(entity);
                AppendMovementEffectInfo(sb, movementEffects);
            }

            if (entityManager.HasComponent<CombatEffects>(entity))
            {
                sb.AppendLine("\n=== Combat Effects Component ===");
                var combatEffects = entityManager.GetComponentData<CombatEffects>(entity);
                AppendCombatEffectInfo(sb, combatEffects);
            }

            if (entityManager.HasComponent<HealthEffects>(entity))
            {
                sb.AppendLine("\n=== Health Effects Component ===");
                var healthEffects = entityManager.GetComponentData<HealthEffects>(entity);
                AppendHealthEffectInfo(sb, healthEffects);
            }

            return sb.ToString();
        }

        /// <summary>
        /// Get summary information about effects across all entities
        /// </summary>
        public static string GetSystemEffectSummary(EntityManager entityManager, dfloat currentTime)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== Effect System Summary ===");

            // Count entities using different systems
            var newSystemQuery = entityManager.CreateEntityQuery(typeof(GameEffect));
            var movementEffectsQuery = entityManager.CreateEntityQuery(typeof(MovementEffects));

            sb.AppendLine($"Entities with GameEffect system: {newSystemQuery.CalculateEntityCount()}");
            sb.AppendLine($"Entities with MovementEffects: {movementEffectsQuery.CalculateEntityCount()}");

            // Count total active effects
            int totalGameEffects = 0;
            int totalSpeedEffects = 0;

            var gameEffectEntities = newSystemQuery.ToEntityArray(Allocator.Temp);
            foreach (var entity in gameEffectEntities)
            {
                var effects = entityManager.GetBuffer<GameEffect>(entity);
                totalGameEffects += GameEffectUtils.GetActiveEffectCount(effects, currentTime);
            }
            gameEffectEntities.Dispose();

            sb.AppendLine($"Total active GameEffects: {totalGameEffects}");

            return sb.ToString();
        }

        /// <summary>
        /// Find entities with specific effect types
        /// </summary>
        public static NativeArray<Entity> FindEntitiesWithEffect(EntityManager entityManager, GameEffect.EffectType effectType, dfloat currentTime)
        {
            var results = new NativeList<Entity>(Allocator.Temp);
            var query = entityManager.CreateEntityQuery(typeof(GameEffect));
            var entities = query.ToEntityArray(Allocator.Temp);

            foreach (var entity in entities)
            {
                var effects = entityManager.GetBuffer<GameEffect>(entity);
                if (GameEffectUtils.HasEffectType(effects, effectType, currentTime))
                {
                    results.Add(entity);
                }
            }

            var resultArray = results.ToArray(Allocator.Temp);
            results.Dispose();
            entities.Dispose();
            return resultArray;
        }

        /// <summary>
        /// Get performance metrics for the effect system
        /// </summary>
        public static string GetPerformanceMetrics(EntityManager entityManager)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== Effect System Performance Metrics ===");

            var gameEffectQuery = entityManager.CreateEntityQuery(typeof(GameEffect));
            var movementEffectsQuery = entityManager.CreateEntityQuery(typeof(MovementEffects));
            var combatEffectsQuery = entityManager.CreateEntityQuery(typeof(CombatEffects));
            var healthEffectsQuery = entityManager.CreateEntityQuery(typeof(HealthEffects));

            sb.AppendLine($"GameEffect buffers: {gameEffectQuery.CalculateEntityCount()}");
            sb.AppendLine($"MovementEffects components: {movementEffectsQuery.CalculateEntityCount()}");
            sb.AppendLine($"CombatEffects components: {combatEffectsQuery.CalculateEntityCount()}");
            sb.AppendLine($"HealthEffects components: {healthEffectsQuery.CalculateEntityCount()}");

            // Calculate total memory usage estimate
            int totalGameEffects = 0;
            var entities = gameEffectQuery.ToEntityArray(Allocator.Temp);
            foreach (var entity in entities)
            {
                var effects = entityManager.GetBuffer<GameEffect>(entity);
                totalGameEffects += effects.Length;
            }
            entities.Dispose();

            var gameEffectSize = System.Runtime.InteropServices.Marshal.SizeOf<GameEffect>();
            var movementEffectSize = System.Runtime.InteropServices.Marshal.SizeOf<MovementEffects>();
            var combatEffectSize = System.Runtime.InteropServices.Marshal.SizeOf<CombatEffects>();
            var healthEffectSize = System.Runtime.InteropServices.Marshal.SizeOf<HealthEffects>();

            var totalMemory = (totalGameEffects * gameEffectSize) +
                             (movementEffectsQuery.CalculateEntityCount() * movementEffectSize) +
                             (combatEffectsQuery.CalculateEntityCount() * combatEffectSize) +
                             (healthEffectsQuery.CalculateEntityCount() * healthEffectSize);

            sb.AppendLine($"Total GameEffect instances: {totalGameEffects}");
            sb.AppendLine($"Estimated memory usage: {totalMemory / 1024f:F2} KB");

            return sb.ToString();
        }

        private static void AppendGameEffectInfo(StringBuilder sb, DynamicBuffer<GameEffect> effects, dfloat currentTime)
        {
            int activeCount = 0;
            int expiredCount = 0;

            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                bool isExpired = effect.IsExpired(currentTime);
                
                if (isExpired) expiredCount++;
                else activeCount++;

                sb.AppendLine($"  [{i}] {effect.effectType} | Value: {effect.primaryValue} | " +
                             $"Duration: {effect.GetRemainingDuration(currentTime):F2}s | " +
                             $"Stacks: {effect.stackCount}/{effect.maxStacks} | " +
                             $"Source: {effect.sourceId} | " +
                             $"Status: {(isExpired ? "EXPIRED" : "ACTIVE")}");
            }

            sb.AppendLine($"Total: {effects.Length} effects ({activeCount} active, {expiredCount} expired)");
        }

        private static void AppendMovementEffectInfo(StringBuilder sb, MovementEffects effects)
        {
            sb.AppendLine($"  Speed Multiplier: {effects.speedMultiplier:F3}");
            sb.AppendLine($"  Effective Max Speed: {effects.GetEffectiveMaxSpeed():F2}");
            sb.AppendLine($"  Status: Slowed={effects.isSlowed}, Hasted={effects.isHasted}, Stunned={effects.isStunned}, Rooted={effects.isRooted}");
            sb.AppendLine($"  Intensity: Slow={effects.slowIntensity:F2}, Haste={effects.hasteIntensity:F2}");
        }

        private static void AppendCombatEffectInfo(StringBuilder sb, CombatEffects effects)
        {
            sb.AppendLine($"  Damage Multiplier: {effects.damageMultiplier:F3}");
            sb.AppendLine($"  Attack Speed Multiplier: {effects.attackSpeedMultiplier:F3}");
            sb.AppendLine($"  Effective Attack Damage: {effects.GetEffectiveAttackDamage():F2}");
            sb.AppendLine($"  Critical Chance: {effects.GetEffectiveCriticalChance():F3}");
            sb.AppendLine($"  Status: Buff={effects.hasAttackBuff}, Debuff={effects.hasAttackDebuff}");
        }

        private static void AppendHealthEffectInfo(StringBuilder sb, HealthEffects effects)
        {
            sb.AppendLine($"  Max Health Multiplier: {effects.maxHealthMultiplier:F3}");
            sb.AppendLine($"  Effective Max Health: {effects.GetEffectiveMaxHealth():F2}");
            sb.AppendLine($"  DOT DPS: {effects.dotDamagePerSecond:F2}");
            sb.AppendLine($"  HOT HPS: {effects.hotHealingPerSecond:F2}");
            sb.AppendLine($"  Shield: {effects.currentShieldAmount:F2}/{effects.maxShieldAmount:F2}");
            sb.AppendLine($"  Status: DOT={effects.hasDamageOverTime}, HOT={effects.hasHealOverTime}, Shield={effects.hasShield}");
        }
    }
}
