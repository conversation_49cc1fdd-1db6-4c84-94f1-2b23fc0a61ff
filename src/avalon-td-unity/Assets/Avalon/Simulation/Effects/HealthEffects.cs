using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Component that manages health-related effects and modifiers
    /// Handles damage over time, heal over time, shields, and regeneration
    /// </summary>
    public struct HealthEffects : IComponentData
    {
        // Base values from UnitStats
        public dfloat baseMaxHealth;
        public dfloat baseHealthRegeneration;

        // Current health modifiers
        public dfloat maxHealthMultiplier;
        public dfloat maxHealthBonus;           // Flat health bonus
        public dfloat healthRegenerationMultiplier;
        public dfloat healthRegenerationBonus;  // Flat regen bonus per second

        // Damage over time accumulation
        public dfloat pendingDotDamage;         // Accumulated DOT damage to apply
        public dfloat dotDamagePerSecond;       // Current DOT damage rate
        public int activeDotEffects;            // Number of active DOT effects

        // Heal over time accumulation
        public dfloat pendingHotHealing;        // Accumulated HOT healing to apply
        public dfloat hotHealingPerSecond;      // Current HOT healing rate
        public int activeHotEffects;            // Number of active HOT effects

        // Shield system
        public dfloat currentShieldAmount;      // Current shield value
        public dfloat maxShieldAmount;          // Maximum shield capacity
        public dfloat shieldRegeneration;       // Shield regen per second
        public dfloat shieldRegenDelay;         // Time before shield starts regenerating
        public dfloat lastShieldDamageTime;     // When shield was last damaged

        // Damage resistance and vulnerability
        public dfloat damageReduction;          // Percentage damage reduction (0-1)
        public dfloat damageVulnerability;      // Percentage damage increase (0+)
        public dfloat physicalResistance;       // Physical damage resistance (0-1)
        public dfloat magicalResistance;        // Magical damage resistance (0-1)
        public dfloat elementalResistance;      // Elemental damage resistance (0-1)

        // Status effect resistances
        public dfloat poisonResistance;         // Poison effect resistance (0-1)
        public dfloat burnResistance;           // Burn effect resistance (0-1)
        public dfloat bleedResistance;          // Bleed effect resistance (0-1)
        public dfloat diseaseResistance;        // Disease effect resistance (0-1)

        // Health state flags
        public bool hasHealthBuff;              // Has beneficial health effects
        public bool hasHealthDebuff;            // Has detrimental health effects
        public bool isRegenerating;             // Currently regenerating health
        public bool hasDamageOverTime;          // Has active DOT effects
        public bool hasHealOverTime;            // Has active HOT effects
        public bool hasShield;                  // Has active shield
        public bool isInvulnerable;             // Cannot take damage
        public bool isUndead;                   // Affected by healing differently
        public bool cannotHeal;                 // Cannot be healed
        public bool cannotRegenerate;           // Cannot regenerate health

        // Special health effects
        public bool reflectsDamage;             // Reflects damage back to attacker
        public dfloat damageReflectionPercent;  // Percentage of damage reflected
        public bool absorbsHealing;             // Converts healing to damage
        public bool leechesLife;                // Steals life from nearby enemies

        /// <summary>
        /// Get the final effective max health after all modifiers
        /// </summary>
        public dfloat GetEffectiveMaxHealth()
        {
            var health = baseMaxHealth;
            health += maxHealthBonus;
            health *= maxHealthMultiplier;
            return dmath.max(health, dfloat.One);
        }

        /// <summary>
        /// Get the final effective health regeneration rate after all modifiers
        /// </summary>
        public dfloat GetEffectiveHealthRegeneration()
        {
            if (cannotRegenerate) return dfloat.Zero;
            
            var regen = baseHealthRegeneration;
            regen += healthRegenerationBonus;
            regen *= healthRegenerationMultiplier;
            return dmath.max(regen, dfloat.Zero);
        }

        /// <summary>
        /// Get the total damage per second from all DOT effects
        /// </summary>
        public dfloat GetTotalDotDamagePerSecond()
        {
            return dotDamagePerSecond;
        }

        /// <summary>
        /// Get the total healing per second from all HOT effects
        /// </summary>
        public dfloat GetTotalHotHealingPerSecond()
        {
            if (cannotHeal) return dfloat.Zero;
            return hotHealingPerSecond;
        }

        /// <summary>
        /// Calculate damage reduction from all sources
        /// </summary>
        public dfloat CalculateDamageReduction(dfloat incomingDamage, bool isPhysical = true, bool isMagical = false)
        {
            if (isInvulnerable) return dfloat.One; // 100% reduction
            
            dfloat totalReduction = damageReduction;
            
            if (isPhysical) totalReduction += physicalResistance;
            if (isMagical) totalReduction += magicalResistance;
            
            // Apply vulnerability
            totalReduction -= damageVulnerability;
            
            // Clamp between 0 and 0.95 (max 95% reduction)
            return dmath.clamp(totalReduction, dfloat.Zero, new dfloat(0.95f));
        }

        /// <summary>
        /// Apply damage to shields first, then health
        /// </summary>
        public dfloat ApplyDamageToShields(dfloat damage, dfloat currentTime)
        {
            if (currentShieldAmount <= dfloat.Zero) return damage;
            
            var damageToShield = dmath.min(damage, currentShieldAmount);
            currentShieldAmount -= damageToShield;
            lastShieldDamageTime = currentTime;
            
            return damage - damageToShield;
        }

        /// <summary>
        /// Update shield regeneration
        /// </summary>
        public void UpdateShieldRegeneration(dfloat deltaTime, dfloat currentTime)
        {
            if (!hasShield || currentShieldAmount >= maxShieldAmount) return;
            if (currentTime - lastShieldDamageTime < shieldRegenDelay) return;
            
            var regenAmount = shieldRegeneration * deltaTime;
            currentShieldAmount = dmath.min(currentShieldAmount + regenAmount, maxShieldAmount);
        }

        /// <summary>
        /// Check if the unit can take damage
        /// </summary>
        public bool CanTakeDamage()
        {
            return !isInvulnerable;
        }

        /// <summary>
        /// Check if the unit can be healed
        /// </summary>
        public bool CanBeHealed()
        {
            return !cannotHeal && !isUndead;
        }

        /// <summary>
        /// Check if the unit has any health-related buffs
        /// </summary>
        public bool HasHealthBuffs()
        {
            return hasHealthBuff || maxHealthMultiplier > dfloat.One || 
                   healthRegenerationMultiplier > dfloat.One || hasShield;
        }

        /// <summary>
        /// Check if the unit has any health-related debuffs
        /// </summary>
        public bool HasHealthDebuffs()
        {
            return hasHealthDebuff || hasDamageOverTime || 
                   damageVulnerability > dfloat.Zero || cannotHeal;
        }

        /// <summary>
        /// Reset all modifiers to default values
        /// </summary>
        public void ResetModifiers()
        {
            maxHealthMultiplier = dfloat.One;
            maxHealthBonus = dfloat.Zero;
            healthRegenerationMultiplier = dfloat.One;
            healthRegenerationBonus = dfloat.Zero;
            
            pendingDotDamage = dfloat.Zero;
            dotDamagePerSecond = dfloat.Zero;
            activeDotEffects = 0;
            
            pendingHotHealing = dfloat.Zero;
            hotHealingPerSecond = dfloat.Zero;
            activeHotEffects = 0;
            
            damageReduction = dfloat.Zero;
            damageVulnerability = dfloat.Zero;
            physicalResistance = dfloat.Zero;
            magicalResistance = dfloat.Zero;
            elementalResistance = dfloat.Zero;
            
            poisonResistance = dfloat.Zero;
            burnResistance = dfloat.Zero;
            bleedResistance = dfloat.Zero;
            diseaseResistance = dfloat.Zero;
            
            hasHealthBuff = false;
            hasHealthDebuff = false;
            isRegenerating = false;
            hasDamageOverTime = false;
            hasHealOverTime = false;
            isInvulnerable = false;
            cannotHeal = false;
            cannotRegenerate = false;
            
            reflectsDamage = false;
            damageReflectionPercent = dfloat.Zero;
            absorbsHealing = false;
            leechesLife = false;
        }

        /// <summary>
        /// Initialize from UnitStats
        /// </summary>
        public static HealthEffects CreateFromUnitStats(UnitStats stats)
        {
            return new HealthEffects
            {
                baseMaxHealth = stats.maxHealth,
                baseHealthRegeneration = dfloat.Zero, // Default no regen
                
                maxHealthMultiplier = dfloat.One,
                maxHealthBonus = dfloat.Zero,
                healthRegenerationMultiplier = dfloat.One,
                healthRegenerationBonus = dfloat.Zero,
                
                pendingDotDamage = dfloat.Zero,
                dotDamagePerSecond = dfloat.Zero,
                activeDotEffects = 0,
                
                pendingHotHealing = dfloat.Zero,
                hotHealingPerSecond = dfloat.Zero,
                activeHotEffects = 0,
                
                currentShieldAmount = dfloat.Zero,
                maxShieldAmount = dfloat.Zero,
                shieldRegeneration = dfloat.Zero,
                shieldRegenDelay = new dfloat(3.0f), // 3 second delay
                lastShieldDamageTime = dfloat.Zero,
                
                damageReduction = dfloat.Zero,
                damageVulnerability = dfloat.Zero,
                physicalResistance = dfloat.Zero,
                magicalResistance = dfloat.Zero,
                elementalResistance = dfloat.Zero,
                
                poisonResistance = dfloat.Zero,
                burnResistance = dfloat.Zero,
                bleedResistance = dfloat.Zero,
                diseaseResistance = dfloat.Zero,
                
                hasHealthBuff = false,
                hasHealthDebuff = false,
                isRegenerating = false,
                hasDamageOverTime = false,
                hasHealOverTime = false,
                hasShield = false,
                isInvulnerable = false,
                isUndead = false,
                cannotHeal = false,
                cannotRegenerate = false,
                
                reflectsDamage = false,
                damageReflectionPercent = dfloat.Zero,
                absorbsHealing = false,
                leechesLife = false
            };
        }
    }
}
