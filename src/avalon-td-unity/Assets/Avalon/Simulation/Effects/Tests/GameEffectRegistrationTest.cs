using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using UnityEngine;

namespace Avalon.Simulation.Effects.Tests
{
    /// <summary>
    /// Test script to verify that GameEffect buffer registration works correctly
    /// This should resolve the ArgumentException: Unknown Type error
    /// </summary>
    public class GameEffectRegistrationTest : MonoBehaviour
    {
        [SerializeField] private bool runTestOnStart = true;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                TestGameEffectBufferRegistration();
            }
        }
        
        /// <summary>
        /// Test that we can create and use DynamicBuffer<GameEffect> without errors
        /// </summary>
        public void TestGameEffectBufferRegistration()
        {
            Debug.Log("Testing GameEffect buffer registration...");
            
            try
            {
                var world = World.DefaultGameObjectInjectionWorld;
                var entityManager = world.EntityManager;
                
                // Create a test entity
                var testEntity = entityManager.CreateEntity();
                
                // Try to add the GameEffect buffer - this should not throw an exception
                entityManager.AddBuffer<GameEffect>(testEntity);
                
                // Get the buffer and add a test effect
                var effectBuffer = entityManager.GetBuffer<GameEffect>(testEntity);
                
                var testEffect = new GameEffect
                {
                    category = GameEffect.EffectCategory.Combat,
                    effectType = GameEffect.EffectType.AttackDamage,
                    stackingType = GameEffect.StackingType.Additive,
                    primaryValue = new Unity.Deterministic.Mathematics.dfloat(1.5f),
                    duration = new Unity.Deterministic.Mathematics.dfloat(10.0f),
                    startTime = new Unity.Deterministic.Mathematics.dfloat(0.0f),
                    sourceId = 1,
                    stackCount = 1,
                    maxStacks = 3,
                    priority = 1
                };
                
                effectBuffer.Add(testEffect);
                
                Debug.Log($"✅ SUCCESS: GameEffect buffer registration test passed! Buffer length: {effectBuffer.Length}");
                
                // Test FlowFieldCellBuffer as well
                entityManager.AddBuffer<FlowFieldCellBuffer>(testEntity);
                var cellBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(testEntity);
                
                var testCell = new FlowFieldCellBuffer
                {
                    cell = new FlowFieldCell
                    {
                        direction = new Unity.Deterministic.Mathematics.dfloat2(dfloat.One, dfloat.Zero),
                        cost = new Unity.Deterministic.Mathematics.dfloat(1.0f),
                        distance = new Unity.Deterministic.Mathematics.dfloat(0.0f),
                        walkabilityMask = 0xFF,
                        isTarget = false
                    }
                };
                
                cellBuffer.Add(testCell);
                
                Debug.Log($"✅ SUCCESS: FlowFieldCellBuffer registration test passed! Buffer length: {cellBuffer.Length}");
                
                // Clean up
                entityManager.DestroyEntity(testEntity);
                
                Debug.Log("🎉 All buffer registration tests passed successfully!");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ FAILED: GameEffect buffer registration test failed with error: {ex.Message}");
                Debug.LogError($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test creating a query with DynamicBuffer<GameEffect>
        /// </summary>
        public void TestGameEffectQuery()
        {
            Debug.Log("Testing GameEffect query creation...");
            
            try
            {
                var world = World.DefaultGameObjectInjectionWorld;
                var entityManager = world.EntityManager;
                
                // Try to create a query that includes DynamicBuffer<GameEffect>
                // This is what was failing in CombatEffectsProcessingSystem
                var query = entityManager.CreateEntityQuery(
                    ComponentType.ReadWrite<DynamicBuffer<GameEffect>>(),
                    ComponentType.ReadOnly<UnitStats>()
                );
                
                Debug.Log($"✅ SUCCESS: GameEffect query creation test passed! Query entity count: {query.CalculateEntityCount()}");
                
                query.Dispose();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ FAILED: GameEffect query test failed with error: {ex.Message}");
                Debug.LogError($"Stack trace: {ex.StackTrace}");
            }
        }
        
        [ContextMenu("Run Registration Test")]
        public void RunRegistrationTest()
        {
            TestGameEffectBufferRegistration();
        }
        
        [ContextMenu("Run Query Test")]
        public void RunQueryTest()
        {
            TestGameEffectQuery();
        }
    }
}
