using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using UnityEditor;
using UnityEngine;

namespace Avalon.Simulation.Effects.Tests.Editor
{
    /// <summary>
    /// Editor test to verify GameEffect buffer registration works in edit mode
    /// </summary>
    public class GameEffectRegistrationEditorTest
    {
        [MenuItem("FlowField/Test GameEffect Registration")]
        public static void TestGameEffectRegistration()
        {
            Debug.Log("=== Testing GameEffect Buffer Registration ===");
            
            try
            {
                // Create a temporary world for testing
                var world = new World("TestWorld");
                var entityManager = world.EntityManager;
                
                // Create a test entity
                var testEntity = entityManager.CreateEntity();
                
                // Test 1: Add GameEffect buffer
                Debug.Log("Test 1: Adding GameEffect buffer...");
                entityManager.AddBuffer<GameEffect>(testEntity);
                Debug.Log("✅ GameEffect buffer added successfully");
                
                // Test 2: Get and use the buffer
                Debug.Log("Test 2: Getting and using GameEffect buffer...");
                var effectBuffer = entityManager.GetBuffer<GameEffect>(testEntity);
                
                var testEffect = new GameEffect
                {
                    category = GameEffect.EffectCategory.Combat,
                    effectType = GameEffect.EffectType.AttackDamage,
                    stackingType = GameEffect.StackingType.Additive,
                    primaryValue = new Unity.Deterministic.Mathematics.dfloat(1.5f),
                    duration = new Unity.Deterministic.Mathematics.dfloat(10.0f),
                    startTime = new Unity.Deterministic.Mathematics.dfloat(0.0f),
                    sourceId = 1,
                    stackCount = 1,
                    maxStacks = 3,
                    priority = 1
                };
                
                effectBuffer.Add(testEffect);
                Debug.Log($"✅ GameEffect added to buffer. Buffer length: {effectBuffer.Length}");
                
                // Test 3: Create entity query with DynamicBuffer<GameEffect>
                Debug.Log("Test 3: Creating entity query with DynamicBuffer<GameEffect>...");
                var query = entityManager.CreateEntityQuery(
                    ComponentType.ReadWrite<DynamicBuffer<GameEffect>>()
                );
                Debug.Log($"✅ Query created successfully. Entity count: {query.CalculateEntityCount()}");
                
                // Test 4: Test FlowFieldCellBuffer as well
                Debug.Log("Test 4: Testing FlowFieldCellBuffer...");
                entityManager.AddBuffer<FlowFieldCellBuffer>(testEntity);
                var cellBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(testEntity);
                
                var testCell = new FlowFieldCellBuffer
                {
                    cell = new FlowFieldCell
                    {
                        direction = new Unity.Deterministic.Mathematics.dfloat2(dfloat.One, dfloat.Zero),
                        cost = new Unity.Deterministic.Mathematics.dfloat(1.0f),
                        distance = new Unity.Deterministic.Mathematics.dfloat(0.0f),
                        walkabilityMask = 0xFF,
                        isTarget = false
                    }
                };
                
                cellBuffer.Add(testCell);
                Debug.Log($"✅ FlowFieldCellBuffer added successfully. Buffer length: {cellBuffer.Length}");
                
                // Clean up
                query.Dispose();
                world.Dispose();
                
                Debug.Log("🎉 ALL TESTS PASSED! GameEffect and FlowFieldCellBuffer registration is working correctly.");
                EditorUtility.DisplayDialog("Test Results", "All GameEffect registration tests passed successfully!", "OK");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ TEST FAILED: {ex.Message}");
                Debug.LogError($"Stack trace: {ex.StackTrace}");
                EditorUtility.DisplayDialog("Test Results", $"Test failed with error: {ex.Message}", "OK");
            }
        }
        
        [MenuItem("FlowField/Test CombatEffectsProcessingSystem Query")]
        public static void TestCombatEffectsQuery()
        {
            Debug.Log("=== Testing CombatEffectsProcessingSystem Query ===");
            
            try
            {
                var world = new World("TestWorld");
                var entityManager = world.EntityManager;
                
                // Create the exact query that was failing in CombatEffectsProcessingSystem
                Debug.Log("Creating CombatEffectsProcessingSystem query...");
                var combatEffectsQuery = entityManager.CreateEntityQuery(
                    ComponentType.ReadWrite<DynamicBuffer<GameEffect>>(),
                    ComponentType.ReadOnly<UnitStats>(),
                    ComponentType.Exclude<Disabled>()
                );
                
                Debug.Log($"✅ CombatEffectsProcessingSystem query created successfully! Entity count: {combatEffectsQuery.CalculateEntityCount()}");
                
                // Clean up
                combatEffectsQuery.Dispose();
                world.Dispose();
                
                Debug.Log("🎉 CombatEffectsProcessingSystem query test passed!");
                EditorUtility.DisplayDialog("Test Results", "CombatEffectsProcessingSystem query test passed successfully!", "OK");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ COMBAT EFFECTS QUERY TEST FAILED: {ex.Message}");
                Debug.LogError($"Stack trace: {ex.StackTrace}");
                EditorUtility.DisplayDialog("Test Results", $"CombatEffectsProcessingSystem query test failed: {ex.Message}", "OK");
            }
        }
    }
}
