using Unity.Entities;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// MonoBehaviour component for debugging effects in the Unity Inspector
    /// </summary>
    public class EffectDebuggerInspector : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool autoUpdate = true;
        [SerializeField] private float updateInterval = 1.0f;
        [SerializeField] private bool showSystemSummary = true;
        [SerializeField] private bool showPerformanceMetrics = true;
        
        [Header("Entity Debugging")]
        [SerializeField] private int targetEntityIndex = -1;
        [SerializeField] private bool debugSpecificEntity = false;
        
        [Header("Effect Filtering")]
        [SerializeField] private GameEffect.EffectType filterEffectType = GameEffect.EffectType.MovementSpeed;
        [SerializeField] private bool findEntitiesWithEffect = false;
        
        [Header("Debug Output")]
        [TextArea(10, 20)]
        [SerializeField] private string debugOutput = "";
        
        private EntityManager entityManager;
        private float lastUpdateTime;
        private Entity targetEntity;

        private void Start()
        {
            if (World.DefaultGameObjectInjectionWorld != null)
            {
                entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
            }
        }

        private void Update()
        {
            if (entityManager == default) return;
            
            if (autoUpdate && Time.time - lastUpdateTime >= updateInterval)
            {
                UpdateDebugInfo();
                lastUpdateTime = Time.time;
            }
        }

        [ContextMenu("Update Debug Info")]
        public void UpdateDebugInfo()
        {
            if (entityManager == default)
            {
                debugOutput = "EntityManager not available";
                return;
            }

            var currentTime = new dfloat(Time.time);
            var output = "";

            if (showSystemSummary)
            {
                output += EffectDebugger.GetSystemEffectSummary(entityManager, currentTime);
                output += "\n\n";
            }

            if (showPerformanceMetrics)
            {
                output += EffectDebugger.GetPerformanceMetrics(entityManager);
                output += "\n\n";
            }

            if (debugSpecificEntity && targetEntityIndex >= 0)
            {
                targetEntity = new Entity { Index = targetEntityIndex, Version = 1 };
                if (entityManager.Exists(targetEntity))
                {
                    output += EffectDebugger.GetEntityEffectInfo(entityManager, targetEntity, currentTime);
                    output += "\n\n";
                }
                else
                {
                    output += $"Entity {targetEntityIndex} does not exist\n\n";
                }
            }

            if (findEntitiesWithEffect)
            {
                var entities = EffectDebugger.FindEntitiesWithEffect(entityManager, filterEffectType, currentTime);
                output += $"=== Entities with {filterEffectType} ===\n";
                foreach (var entity in entities)
                {
                    output += $"Entity {entity.Index}\n";
                }
                output += $"Total: {entities.Length} entities\n\n";
            }

            debugOutput = output;
        }

        [ContextMenu("Apply Test Slow Effect")]
        public void ApplyTestSlowEffect()
        {
            if (entityManager == default || !debugSpecificEntity || targetEntityIndex < 0) return;

            targetEntity = new Entity { Index = targetEntityIndex, Version = 1 };
            if (!entityManager.Exists(targetEntity)) return;

            var currentTime = new dfloat(Time.time);
            SpeedModifierCompatibility.ApplySlowEffect(entityManager, targetEntity, 
                new dfloat(0.5f), new dfloat(5.0f), currentTime, 999);
            
            UpdateDebugInfo();
        }

        [ContextMenu("Apply Test Haste Effect")]
        public void ApplyTestHasteEffect()
        {
            if (entityManager == default || !debugSpecificEntity || targetEntityIndex < 0) return;

            targetEntity = new Entity { Index = targetEntityIndex, Version = 1 };
            if (!entityManager.Exists(targetEntity)) return;

            var currentTime = new dfloat(Time.time);
            SpeedModifierCompatibility.ApplyHasteEffect(entityManager, targetEntity, 
                new dfloat(2.0f), new dfloat(5.0f), currentTime, 998);
            
            UpdateDebugInfo();
        }

        [ContextMenu("Apply Test DOT Effect")]
        public void ApplyTestDOTEffect()
        {
            if (entityManager == default || !debugSpecificEntity || targetEntityIndex < 0) return;

            targetEntity = new Entity { Index = targetEntityIndex, Version = 1 };
            if (!entityManager.Exists(targetEntity)) return;

            if (entityManager.HasBuffer<GameEffect>(targetEntity))
            {
                var effectBuffer = entityManager.GetBuffer<GameEffect>(targetEntity);
                var currentTime = new dfloat(Time.time);
                GameEffectUtils.ApplyDamageOverTimeEffect(effectBuffer, new dfloat(10.0f), 
                    new dfloat(8.0f), new dfloat(1.0f), currentTime, 997);
            }
            
            UpdateDebugInfo();
        }

        [ContextMenu("Remove All Effects")]
        public void RemoveAllEffects()
        {
            if (entityManager == default || !debugSpecificEntity || targetEntityIndex < 0) return;

            targetEntity = new Entity { Index = targetEntityIndex, Version = 1 };
            if (!entityManager.Exists(targetEntity)) return;

            // Clear GameEffects
            if (entityManager.HasBuffer<GameEffect>(targetEntity))
            {
                var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(targetEntity);
                gameEffectBuffer.Clear();
            }
            
            UpdateDebugInfo();
        }

        [ContextMenu("Find All Entities with Effects")]
        public void FindAllEntitiesWithEffects()
        {
            if (entityManager == default) return;

            var output = "=== All Entities with Effects ===\n";
            
            // Find entities with GameEffect
            var gameEffectQuery = entityManager.CreateEntityQuery(typeof(GameEffect));
            var gameEffectEntities = gameEffectQuery.ToEntityArray(Unity.Collections.Allocator.Temp);
            output += $"Entities with GameEffect: {gameEffectEntities.Length}\n";
            for (int i = 0; i < gameEffectEntities.Length && i < 10; i++)
            {
                output += $"  Entity {gameEffectEntities[i].Index}\n";
            }
            if (gameEffectEntities.Length > 10)
                output += $"  ... and {gameEffectEntities.Length - 10} more\n";
            gameEffectEntities.Dispose();

            debugOutput = output;
        }

        private void OnValidate()
        {
            if (autoUpdate && Application.isPlaying)
            {
                UpdateDebugInfo();
            }
        }

        private void OnGUI()
        {
            if (!Application.isPlaying) return;

            // Simple on-screen debug display
            var rect = new Rect(10, 10, 400, 200);
            GUI.Box(rect, "Effect System Debug");
            
            var currentTime = new dfloat(Time.time);
            var summary = entityManager != default ? 
                EffectDebugger.GetSystemEffectSummary(entityManager, currentTime) : 
                "EntityManager not available";
            
            GUI.Label(new Rect(15, 30, 390, 170), summary);
        }
    }
}
