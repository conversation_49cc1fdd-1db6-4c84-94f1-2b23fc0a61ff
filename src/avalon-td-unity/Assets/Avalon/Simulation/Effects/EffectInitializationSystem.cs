using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// System to initialize effect components from UnitStats
    /// Works alongside the existing UnitStatsSystem to set up the new effect system
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(UnitStatsSystem))]
    [UpdateBefore(typeof(GameEffectSystem))]
    public partial struct EffectInitializationSystem : ISystem
    {
        private EntityQuery unitsWithAvoidanceQuery;
        private EntityQuery unitsWithoutAvoidanceQuery;
        private EntityQuery unitsNeedingEffectSync;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EndSimulationEntityCommandBufferSystem.Singleton>();
            // Query for units with avoidance that need effect initialization
            unitsWithAvoidanceQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadWrite<FlowFieldFollower>(),
                ComponentType.ReadWrite<AvoidanceData>(),
                ComponentType.ReadWrite<MovementEffects>(),
                ComponentType.ReadWrite<CombatEffects>(),
                ComponentType.ReadWrite<HealthEffects>(),
                ComponentType.ReadOnly<UnitStatsInitialized>(),
                ComponentType.Exclude<EffectsInitialized>()
            );

            // Query for units without avoidance that need effect initialization
            unitsWithoutAvoidanceQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadWrite<FlowFieldFollower>(),
                ComponentType.ReadWrite<MovementEffects>(),
                ComponentType.ReadWrite<CombatEffects>(),
                ComponentType.ReadWrite<HealthEffects>(),
                ComponentType.ReadOnly<UnitStatsInitialized>(),
                ComponentType.Exclude<AvoidanceData>(),
                ComponentType.Exclude<EffectsInitialized>()
            );

            // Query for units that need effect synchronization (when stats change)
            unitsNeedingEffectSync = state.GetEntityQuery(
                ComponentType.ReadWrite<UnitStats>(),
                ComponentType.ReadOnly<MovementEffects>(),
                ComponentType.ReadWrite<HealthEffects>(),
                ComponentType.ReadOnly<EffectsInitialized>()
            );

            // Only run when there are units that need effect initialization
            state.RequireAnyForUpdate(unitsWithAvoidanceQuery, unitsWithoutAvoidanceQuery, unitsNeedingEffectSync);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var jobHandle = state.Dependency;

            // Initialize effect components for units with avoidance
            if (!unitsWithAvoidanceQuery.IsEmpty)
            {
                var initJobWithAvoidance = new InitializeEffectComponentsWithAvoidanceJob();
                jobHandle = initJobWithAvoidance.ScheduleParallel(unitsWithAvoidanceQuery, jobHandle);
            }

            // Initialize effect components for units without avoidance
            if (!unitsWithoutAvoidanceQuery.IsEmpty)
            {
                var initJobWithoutAvoidance = new InitializeEffectComponentsJob();
                jobHandle = initJobWithoutAvoidance.ScheduleParallel(unitsWithoutAvoidanceQuery, jobHandle);
            }

            // Synchronize health stats with health effects
            if (!unitsNeedingEffectSync.IsEmpty)
            {
                var syncJob = new SynchronizeHealthStatsJob();
                jobHandle = syncJob.ScheduleParallel(unitsNeedingEffectSync, jobHandle);
            }

            // Set dependency without completing jobs to avoid crashes
            state.Dependency = jobHandle;

            // Mark units as having effects initialized using ECB
            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);

            // Get entities that were just initialized and mark them
            var entitiesWithAvoidance = unitsWithAvoidanceQuery.ToEntityArray(Unity.Collections.Allocator.Temp);
            var entitiesWithoutAvoidance = unitsWithoutAvoidanceQuery.ToEntityArray(Unity.Collections.Allocator.Temp);

            // Mark units with avoidance as having effects initialized
            for (int i = 0; i < entitiesWithAvoidance.Length; i++)
            {
                ecb.AddComponent<EffectsInitialized>(entitiesWithAvoidance[i]);
            }

            // Mark units without avoidance as having effects initialized
            for (int i = 0; i < entitiesWithoutAvoidance.Length; i++)
            {
                ecb.AddComponent<EffectsInitialized>(entitiesWithoutAvoidance[i]);
            }

            // Dispose arrays
            entitiesWithAvoidance.Dispose();
            entitiesWithoutAvoidance.Dispose();
        }
    }

    /// <summary>
    /// Tag component to mark entities as having their effect components initialized
    /// </summary>
    public struct EffectsInitialized : IComponentData
    {
    }
}
