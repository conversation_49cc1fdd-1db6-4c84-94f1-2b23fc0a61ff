using Avalon.Simulation;
using Avalon.Simulation.Effects;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Main system that coordinates processing of all effect categories
    /// Handles fixed timestep integration and job scheduling for the generic effect system
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateBefore(typeof(MovementSystemGroup))]
    public partial struct GameEffectSystem : ISystem
    {
        private EntityQuery fixedTimestepQuery;
        private EntityQuery movementEffectsQuery;
        private EntityQuery combatEffectsQuery;
        private EntityQuery healthEffectsQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Require fixed timestep for deterministic processing
            state.RequireForUpdate<FixedTimestep>();

            // Create queries for different effect categories
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            movementEffectsQuery = state.GetEntityQuery(
                typeof(MovementEffects),
                typeof(GameEffect)
            );
            
            combatEffectsQuery = state.GetEntityQuery(
                typeof(CombatEffects),
                typeof(GameEffect)
            );
            
            healthEffectsQuery = state.GetEntityQuery(
                typeof(HealthEffects),
                typeof(GameEffect)
            );
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Only process on fixed ticks for deterministic behavior
            if (!fixedTimestep.shouldProcessTick) return;

            var currentTime = fixedTimestep.currentTime;
            var deltaTime = fixedTimestep.tickDuration;

            // Schedule effect processing jobs in dependency chain
            var dependency = state.Dependency;

            // First, clean up expired effects across all entities
            var cleanupJob = new CleanupExpiredEffectsJob
            {
                currentTime = currentTime
            };
            dependency = cleanupJob.ScheduleParallel(dependency);

            // Process movement effects
            if (!movementEffectsQuery.IsEmpty)
            {
                var movementJob = new UpdateMovementEffectsJob
                {
                    currentTime = currentTime,
                    deltaTime = deltaTime
                };
                dependency = movementJob.ScheduleParallel(dependency);
            }

            // Process combat effects
            if (!combatEffectsQuery.IsEmpty)
            {
                var combatJob = new UpdateCombatEffectsJob
                {
                    currentTime = currentTime,
                    deltaTime = deltaTime
                };
                dependency = combatJob.ScheduleParallel(dependency);
            }

            // Process health effects (including DOT/HOT ticking)
            if (!healthEffectsQuery.IsEmpty)
            {
                var healthJob = new UpdateHealthEffectsJob
                {
                    currentTime = currentTime,
                    deltaTime = deltaTime
                };
                dependency = healthJob.ScheduleParallel(dependency);
            }

            // Update the system dependency
            state.Dependency = dependency;
        }
    }

    /// <summary>
    /// Job to clean up expired effects from all entities
    /// </summary>
    [BurstCompile]
    public partial struct CleanupExpiredEffectsJob : IJobEntity
    {
        public dfloat currentTime;

        public void Execute(DynamicBuffer<GameEffect> effects)
        {
            EffectStackingUtils.RemoveExpiredEffects(effects, currentTime);
        }
    }

    /// <summary>
    /// Job to update movement effects and apply them to FlowFieldFollower
    /// </summary>
    [BurstCompile]
    public partial struct UpdateMovementEffectsJob : IJobEntity
    {
        public dfloat currentTime;
        public dfloat deltaTime;

        public void Execute(ref MovementEffects movementEffects, ref FlowFieldFollower follower,
            DynamicBuffer<GameEffect> effects)
        {
            // Reset modifiers to base values
            movementEffects.ResetModifiers();

            // Process all active movement effects
            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                
                // Skip non-movement effects or expired effects
                if (effect.category != GameEffect.EffectCategory.Movement || 
                    effect.IsExpired(currentTime))
                    continue;

                // Apply effect based on type
                ApplyMovementEffect(ref movementEffects, effect, currentTime);
            }

            // Update status flags based on current modifiers
            UpdateMovementStatusFlags(ref movementEffects);

            // Apply final values to FlowFieldFollower for movement systems
            ApplyMovementEffectsToFollower(ref follower, movementEffects);
        }

        private void ApplyMovementEffect(ref MovementEffects effects, GameEffect effect, dfloat currentTime)
        {
            switch (effect.effectType)
            {
                case GameEffect.EffectType.MovementSpeed:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.speedMultiplier *= effect.primaryValue;
                    else
                        effects.additiveSpeedBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.Acceleration:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.accelerationMultiplier *= effect.primaryValue;
                    else
                        effects.additiveAccelerationBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.Deceleration:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.decelerationMultiplier *= effect.primaryValue;
                    else
                        effects.additiveDecelerationBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.Stun:
                    effects.isStunned = true;
                    effects.stunDuration = effect.GetRemainingDuration(currentTime);
                    break;

                case GameEffect.EffectType.Root:
                    effects.isRooted = true;
                    effects.rootDuration = effect.GetRemainingDuration(currentTime);
                    break;

                case GameEffect.EffectType.Slow:
                    effects.speedMultiplier *= effect.primaryValue;
                    effects.slowIntensity = dfloat.One - effect.primaryValue;
                    break;

                case GameEffect.EffectType.Haste:
                    effects.speedMultiplier *= effect.primaryValue;
                    effects.hasteIntensity = effect.primaryValue - dfloat.One;
                    break;

                case GameEffect.EffectType.TerrainModifier:
                    effects.terrainSpeedMultiplier *= effect.primaryValue;
                    break;
            }
        }

        private void UpdateMovementStatusFlags(ref MovementEffects effects)
        {
            effects.isSlowed = effects.speedMultiplier < dfloat.One || effects.additiveSpeedBonus < dfloat.Zero;
            effects.isHasted = effects.speedMultiplier > dfloat.One || effects.additiveSpeedBonus > dfloat.Zero;
        }

        private void ApplyMovementEffectsToFollower(ref FlowFieldFollower follower, MovementEffects effects)
        {
            // Apply effective values to FlowFieldFollower for movement systems
            follower.maxSpeed = effects.GetEffectiveMaxSpeed();
            follower.minSpeed = effects.GetEffectiveMinSpeed();
            follower.acceleration = effects.GetEffectiveAcceleration();
            follower.deceleration = effects.GetEffectiveDeceleration();

            // Ensure minimum values
            follower.maxSpeed = dmath.max(follower.maxSpeed, dfloat.Zero);
            follower.minSpeed = dmath.max(follower.minSpeed, dfloat.Zero);
            follower.acceleration = dmath.max(follower.acceleration, dfloat.Zero);
            follower.deceleration = dmath.max(follower.deceleration, dfloat.Zero);
        }
    }

    /// <summary>
    /// Job to update combat effects
    /// </summary>
    [BurstCompile]
    public partial struct UpdateCombatEffectsJob : IJobEntity
    {
        public dfloat currentTime;
        public dfloat deltaTime;

        public void Execute(ref CombatEffects combatEffects, DynamicBuffer<GameEffect> effects)
        {
            // Reset modifiers to base values
            combatEffects.ResetModifiers();

            // Process all active combat effects
            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                
                // Skip non-combat effects or expired effects
                if (effect.category != GameEffect.EffectCategory.Combat || 
                    effect.IsExpired(currentTime))
                    continue;

                // Apply effect based on type
                ApplyCombatEffect(ref combatEffects, effect);
            }

            // Update status flags based on current modifiers
            UpdateCombatStatusFlags(ref combatEffects);
        }

        private void ApplyCombatEffect(ref CombatEffects effects, GameEffect effect)
        {
            switch (effect.effectType)
            {
                case GameEffect.EffectType.AttackDamage:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.damageMultiplier *= effect.primaryValue;
                    else
                        effects.additiveDamageBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.AttackSpeed:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.attackSpeedMultiplier *= effect.primaryValue;
                    else
                        effects.additiveSpeedBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.AttackRange:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.rangeMultiplier *= effect.primaryValue;
                    else
                        effects.additiveRangeBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.CriticalChance:
                    effects.criticalChanceBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.CriticalDamage:
                    effects.criticalDamageBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.ArmorPenetration:
                    effects.armorPenetration += effect.primaryValue;
                    break;

                case GameEffect.EffectType.LifeSteal:
                    effects.lifeStealPercentage += effect.primaryValue;
                    effects.attacksLifeSteal = effects.lifeStealPercentage > dfloat.Zero;
                    break;
            }
        }

        private void UpdateCombatStatusFlags(ref CombatEffects effects)
        {
            effects.hasAttackBuff = effects.HasCombatBuffs();
            effects.hasAttackDebuff = effects.HasCombatDebuffs();
        }
    }

    /// <summary>
    /// Job to update health effects including DOT/HOT processing
    /// </summary>
    [BurstCompile]
    public partial struct UpdateHealthEffectsJob : IJobEntity
    {
        public dfloat currentTime;
        public dfloat deltaTime;

        public void Execute(ref HealthEffects healthEffects, ref UnitStats unitStats,
            DynamicBuffer<GameEffect> effects)
        {
            // Reset modifiers to base values
            healthEffects.ResetModifiers();

            // Process all active health effects
            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                
                // Skip non-health effects or expired effects
                if (effect.category != GameEffect.EffectCategory.Health || 
                    effect.IsExpired(currentTime))
                    continue;

                // Apply effect based on type
                ApplyHealthEffect(ref healthEffects, ref unitStats, ref effects.ElementAt(i), currentTime, deltaTime);
            }

            // Update status flags and apply accumulated effects
            UpdateHealthStatusFlags(ref healthEffects);
            ApplyAccumulatedHealthEffects(ref healthEffects, ref unitStats, deltaTime);
        }

        private void ApplyHealthEffect(ref HealthEffects effects, ref UnitStats stats, 
            ref GameEffect effect, dfloat currentTime, dfloat deltaTime)
        {
            switch (effect.effectType)
            {
                case GameEffect.EffectType.DamageOverTime:
                    if (effect.ShouldTick(currentTime))
                    {
                        effects.pendingDotDamage += effect.primaryValue;
                        effect.lastTickTime = currentTime;
                    }
                    effects.dotDamagePerSecond += effect.primaryValue / effect.tickInterval;
                    effects.activeDotEffects++;
                    break;

                case GameEffect.EffectType.HealOverTime:
                    if (effect.ShouldTick(currentTime))
                    {
                        effects.pendingHotHealing += effect.primaryValue;
                        effect.lastTickTime = currentTime;
                    }
                    effects.hotHealingPerSecond += effect.primaryValue / effect.tickInterval;
                    effects.activeHotEffects++;
                    break;

                case GameEffect.EffectType.Shield:
                    effects.currentShieldAmount = dmath.max(effects.currentShieldAmount, effect.primaryValue);
                    effects.maxShieldAmount = dmath.max(effects.maxShieldAmount, effect.primaryValue);
                    effects.hasShield = true;
                    break;

                case GameEffect.EffectType.Regeneration:
                    effects.healthRegenerationBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.MaxHealthModifier:
                    if (effect.stackingType == GameEffect.StackingType.Multiplicative)
                        effects.maxHealthMultiplier *= effect.primaryValue;
                    else
                        effects.maxHealthBonus += effect.primaryValue;
                    break;

                case GameEffect.EffectType.DamageReduction:
                    effects.damageReduction += effect.primaryValue;
                    break;

                case GameEffect.EffectType.Vulnerability:
                    effects.damageVulnerability += effect.primaryValue;
                    break;
            }
        }

        private void UpdateHealthStatusFlags(ref HealthEffects effects)
        {
            effects.hasHealthBuff = effects.HasHealthBuffs();
            effects.hasHealthDebuff = effects.HasHealthDebuffs();
            effects.hasDamageOverTime = effects.activeDotEffects > 0;
            effects.hasHealOverTime = effects.activeHotEffects > 0;
            effects.isRegenerating = effects.GetEffectiveHealthRegeneration() > dfloat.Zero;
        }

        private void ApplyAccumulatedHealthEffects(ref HealthEffects effects, ref UnitStats stats, dfloat deltaTime)
        {
            // Apply pending DOT damage
            if (effects.pendingDotDamage > dfloat.Zero)
            {
                var damageAfterShields = effects.ApplyDamageToShields(effects.pendingDotDamage, dfloat.Zero);
                stats.currentHealth = dmath.max(stats.currentHealth - damageAfterShields, dfloat.Zero);
                effects.pendingDotDamage = dfloat.Zero;
            }

            // Apply pending HOT healing
            if (effects.pendingHotHealing > dfloat.Zero && effects.CanBeHealed())
            {
                var maxHealth = effects.GetEffectiveMaxHealth();
                stats.currentHealth = dmath.min(stats.currentHealth + effects.pendingHotHealing, maxHealth);
                effects.pendingHotHealing = dfloat.Zero;
            }

            // Apply natural regeneration
            var regenAmount = effects.GetEffectiveHealthRegeneration() * deltaTime;
            if (regenAmount > dfloat.Zero && effects.CanBeHealed())
            {
                var maxHealth = effects.GetEffectiveMaxHealth();
                stats.currentHealth = dmath.min(stats.currentHealth + regenAmount, maxHealth);
            }

            // Update shield regeneration
            effects.UpdateShieldRegeneration(deltaTime, dfloat.Zero);
        }
    }
}
