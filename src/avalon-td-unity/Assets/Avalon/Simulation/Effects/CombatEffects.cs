using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Component that manages combat-related effects and modifiers
    /// Handles attack damage, speed, range, and other combat modifications
    /// </summary>
    public struct CombatEffects : IComponentData
    {
        // Base values from UnitStats (original values before any modifiers)
        public dfloat baseAttackDamage;
        public dfloat baseAttackSpeed;      // Attacks per second
        public dfloat baseAttackRange;
        public dfloat baseAttackCooldown;   // Time between attacks

        // Current effective multipliers (calculated from active effects)
        public dfloat damageMultiplier;
        public dfloat attackSpeedMultiplier;
        public dfloat rangeMultiplier;
        public dfloat cooldownMultiplier;   // Lower = faster attacks

        // Critical hit modifiers
        public dfloat baseCriticalChance;   // Base crit chance (0-1)
        public dfloat baseCriticalDamage;   // Base crit damage multiplier
        public dfloat criticalChanceBonus;  // Additive crit chance bonus
        public dfloat criticalDamageBonus;  // Additive crit damage bonus

        // Additive modifiers (flat bonuses/penalties)
        public dfloat additiveDamageBonus;
        public dfloat additiveRangeBonus;
        public dfloat additiveSpeedBonus;

        // Special combat modifiers
        public dfloat armorPenetration;     // Percentage of armor ignored (0-1)
        public dfloat lifeStealPercentage;  // Percentage of damage healed (0-1)
        public dfloat spellVampPercentage;  // Percentage of spell damage healed (0-1)
        public dfloat accuracyModifier;     // Hit chance modifier
        public dfloat dodgeChance;          // Chance to avoid attacks (0-1)

        // Combat state flags
        public bool hasAttackBuff;          // Has beneficial attack effects
        public bool hasAttackDebuff;        // Has detrimental attack effects
        public bool cannotAttack;           // Completely unable to attack
        public bool cannotCrit;             // Critical hits disabled
        public bool ignoresArmor;           // Attacks ignore all armor
        public bool attacksLifeSteal;       // Attacks heal the attacker
        public bool attacksCauseBleed;      // Attacks apply bleeding
        public bool attacksCausePoison;     // Attacks apply poison
        public bool attacksCauseBurn;       // Attacks apply burning

        // Damage type modifiers
        public dfloat physicalDamageMultiplier;
        public dfloat magicalDamageMultiplier;
        public dfloat trueDamageMultiplier;
        public dfloat elementalDamageMultiplier;

        // Attack behavior modifiers
        public bool canAttackWhileMoving;
        public bool canAttackAir;
        public bool canAttackGround;
        public bool splashDamage;           // Attacks hit multiple targets
        public dfloat splashRadius;         // Radius of splash damage
        public dfloat splashDamagePercent;  // Percentage of damage for splash

        /// <summary>
        /// Get the final effective attack damage after all modifiers
        /// </summary>
        public dfloat GetEffectiveAttackDamage()
        {
            if (cannotAttack) return dfloat.Zero;
            
            var damage = baseAttackDamage;
            damage += additiveDamageBonus;
            damage *= damageMultiplier;
            damage *= physicalDamageMultiplier; // Assuming physical by default
            
            return dmath.max(damage, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective attack speed after all modifiers
        /// </summary>
        public dfloat GetEffectiveAttackSpeed()
        {
            if (cannotAttack) return dfloat.Zero;
            
            var speed = baseAttackSpeed;
            speed += additiveSpeedBonus;
            speed *= attackSpeedMultiplier;
            
            return dmath.max(speed, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective attack cooldown after all modifiers
        /// </summary>
        public dfloat GetEffectiveAttackCooldown()
        {
            if (cannotAttack) return dfloat.MAX_VALUE;
            
            var cooldown = baseAttackCooldown;
            cooldown *= cooldownMultiplier;
            
            return dmath.max(cooldown, new dfloat(0.1f)); // Minimum cooldown
        }

        /// <summary>
        /// Get the final effective attack range after all modifiers
        /// </summary>
        public dfloat GetEffectiveAttackRange()
        {
            if (cannotAttack) return dfloat.Zero;
            
            var range = baseAttackRange;
            range += additiveRangeBonus;
            range *= rangeMultiplier;
            
            return dmath.max(range, dfloat.Zero);
        }

        /// <summary>
        /// Get the final effective critical chance after all modifiers
        /// </summary>
        public dfloat GetEffectiveCriticalChance()
        {
            if (cannotAttack || cannotCrit) return dfloat.Zero;
            
            var critChance = baseCriticalChance + criticalChanceBonus;
            return dmath.clamp(critChance, dfloat.Zero, dfloat.One);
        }

        /// <summary>
        /// Get the final effective critical damage multiplier after all modifiers
        /// </summary>
        public dfloat GetEffectiveCriticalDamage()
        {
            if (cannotCrit) return dfloat.One;
            
            var critDamage = baseCriticalDamage + criticalDamageBonus;
            return dmath.max(critDamage, dfloat.One);
        }

        /// <summary>
        /// Check if the unit can attack at all
        /// </summary>
        public bool CanAttack()
        {
            return !cannotAttack;
        }

        /// <summary>
        /// Check if the unit has any combat buffs
        /// </summary>
        public bool HasCombatBuffs()
        {
            return hasAttackBuff || damageMultiplier > dfloat.One || 
                   attackSpeedMultiplier > dfloat.One || additiveDamageBonus > dfloat.Zero;
        }

        /// <summary>
        /// Check if the unit has any combat debuffs
        /// </summary>
        public bool HasCombatDebuffs()
        {
            return hasAttackDebuff || damageMultiplier < dfloat.One || 
                   attackSpeedMultiplier < dfloat.One || additiveDamageBonus < dfloat.Zero;
        }

        /// <summary>
        /// Reset all modifiers to default values
        /// </summary>
        public void ResetModifiers()
        {
            damageMultiplier = dfloat.One;
            attackSpeedMultiplier = dfloat.One;
            rangeMultiplier = dfloat.One;
            cooldownMultiplier = dfloat.One;
            
            criticalChanceBonus = dfloat.Zero;
            criticalDamageBonus = dfloat.Zero;
            
            additiveDamageBonus = dfloat.Zero;
            additiveRangeBonus = dfloat.Zero;
            additiveSpeedBonus = dfloat.Zero;
            
            armorPenetration = dfloat.Zero;
            lifeStealPercentage = dfloat.Zero;
            spellVampPercentage = dfloat.Zero;
            accuracyModifier = dfloat.Zero;
            dodgeChance = dfloat.Zero;
            
            hasAttackBuff = false;
            hasAttackDebuff = false;
            cannotAttack = false;
            cannotCrit = false;
            ignoresArmor = false;
            attacksLifeSteal = false;
            attacksCauseBleed = false;
            attacksCausePoison = false;
            attacksCauseBurn = false;
            
            physicalDamageMultiplier = dfloat.One;
            magicalDamageMultiplier = dfloat.One;
            trueDamageMultiplier = dfloat.One;
            elementalDamageMultiplier = dfloat.One;
        }

        /// <summary>
        /// Initialize from UnitStats
        /// </summary>
        public static CombatEffects CreateFromUnitStats(UnitStats stats)
        {
            return new CombatEffects
            {
                baseAttackDamage = stats.attackDamage,
                baseAttackSpeed = dfloat.One / stats.attackCooldown, // Convert cooldown to attacks per second
                baseAttackRange = stats.attackRange,
                baseAttackCooldown = stats.attackCooldown,
                
                damageMultiplier = dfloat.One,
                attackSpeedMultiplier = dfloat.One,
                rangeMultiplier = dfloat.One,
                cooldownMultiplier = dfloat.One,
                
                baseCriticalChance = new dfloat(0.05f), // 5% base crit
                baseCriticalDamage = new dfloat(1.5f),  // 150% crit damage
                criticalChanceBonus = dfloat.Zero,
                criticalDamageBonus = dfloat.Zero,
                
                additiveDamageBonus = dfloat.Zero,
                additiveRangeBonus = dfloat.Zero,
                additiveSpeedBonus = dfloat.Zero,
                
                armorPenetration = dfloat.Zero,
                lifeStealPercentage = dfloat.Zero,
                spellVampPercentage = dfloat.Zero,
                accuracyModifier = dfloat.Zero,
                dodgeChance = dfloat.Zero,
                
                hasAttackBuff = false,
                hasAttackDebuff = false,
                cannotAttack = false,
                cannotCrit = false,
                ignoresArmor = false,
                attacksLifeSteal = false,
                attacksCauseBleed = false,
                attacksCausePoison = false,
                attacksCauseBurn = false,
                
                physicalDamageMultiplier = dfloat.One,
                magicalDamageMultiplier = dfloat.One,
                trueDamageMultiplier = dfloat.One,
                elementalDamageMultiplier = dfloat.One,
                
                canAttackWhileMoving = false,
                canAttackAir = true,
                canAttackGround = true,
                splashDamage = false,
                splashRadius = dfloat.Zero,
                splashDamagePercent = dfloat.Zero
            };
        }
    }
}
