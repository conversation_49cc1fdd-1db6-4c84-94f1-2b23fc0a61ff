using Avalon.Simulation.Effects;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Utility class for applying, removing, and managing game effects
    /// Provides convenience methods for common effect types and operations
    /// </summary>
    public static class GameEffectUtils
    {
        /// <summary>
        /// Apply a generic effect to an entity
        /// </summary>
        public static void ApplyEffect(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            EffectStackingUtils.ApplyStackingLogic(effects, newEffect);
        }

        /// <summary>
        /// Create a basic effect with common parameters
        /// </summary>
        public static GameEffect CreateEffect(
            GameEffect.EffectCategory category,
            GameEffect.EffectType effectType,
            dfloat primaryValue,
            dfloat duration,
            dfloat currentTime,
            int sourceId = 0,
            GameEffect.StackingType stackingType = GameEffect.StackingType.None,
            byte maxStacks = 1,
            byte flags = GameEffect.EffectFlags.None)
        {
            return new GameEffect
            {
                category = category,
                effectType = effectType,
                stackingType = stackingType,
                primaryValue = primaryValue,
                secondaryValue = dfloat.Zero,
                tertiaryValue = dfloat.Zero,
                duration = duration,
                startTime = currentTime,
                tickInterval = dfloat.Zero,
                lastTickTime = dfloat.Zero,
                sourceId = sourceId,
                stackCount = 1,
                maxStacks = maxStacks,
                priority = 0,
                flags = flags
            };
        }

        // ===== MOVEMENT EFFECTS =====

        /// <summary>
        /// Apply a speed modifier effect
        /// </summary>
        public static void ApplySpeedEffect(DynamicBuffer<GameEffect> effects, dfloat speedMultiplier,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Movement,
                GameEffect.EffectType.MovementSpeed,
                speedMultiplier,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Multiplicative
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a slow effect
        /// </summary>
        public static void ApplySlowEffect(DynamicBuffer<GameEffect> effects, dfloat slowAmount,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Movement,
                GameEffect.EffectType.Slow,
                slowAmount,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Multiplicative,
                flags: GameEffect.EffectFlags.Harmful | GameEffect.EffectFlags.Dispellable
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a haste effect
        /// </summary>
        public static void ApplyHasteEffect(DynamicBuffer<GameEffect> effects, dfloat hasteAmount,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Movement,
                GameEffect.EffectType.Haste,
                hasteAmount,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Multiplicative,
                flags: GameEffect.EffectFlags.Beneficial | GameEffect.EffectFlags.Dispellable
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a stun effect
        /// </summary>
        public static void ApplyStunEffect(DynamicBuffer<GameEffect> effects, dfloat duration,
            dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Movement,
                GameEffect.EffectType.Stun,
                dfloat.Zero,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Refresh,
                flags: GameEffect.EffectFlags.Harmful
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a root effect
        /// </summary>
        public static void ApplyRootEffect(DynamicBuffer<GameEffect> effects, dfloat duration,
            dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Movement,
                GameEffect.EffectType.Root,
                dfloat.Zero,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Refresh,
                flags: GameEffect.EffectFlags.Harmful | GameEffect.EffectFlags.Dispellable
            );
            ApplyEffect(effects, effect);
        }

        // ===== COMBAT EFFECTS =====

        /// <summary>
        /// Apply an attack damage modifier effect
        /// </summary>
        public static void ApplyAttackDamageEffect(DynamicBuffer<GameEffect> effects, dfloat damageMultiplier,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Combat,
                GameEffect.EffectType.AttackDamage,
                damageMultiplier,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Multiplicative,
                flags: damageMultiplier > dfloat.One ? GameEffect.EffectFlags.Beneficial : GameEffect.EffectFlags.Harmful
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply an attack speed modifier effect
        /// </summary>
        public static void ApplyAttackSpeedEffect(DynamicBuffer<GameEffect> effects, dfloat speedMultiplier,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Combat,
                GameEffect.EffectType.AttackSpeed,
                speedMultiplier,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Multiplicative,
                flags: speedMultiplier > dfloat.One ? GameEffect.EffectFlags.Beneficial : GameEffect.EffectFlags.Harmful
            );
            ApplyEffect(effects, effect);
        }

        // ===== HEALTH EFFECTS =====

        /// <summary>
        /// Apply a damage over time effect
        /// </summary>
        public static void ApplyDamageOverTimeEffect(DynamicBuffer<GameEffect> effects, dfloat damagePerTick,
            dfloat duration, dfloat tickInterval, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.DamageOverTime,
                damagePerTick,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Independent,
                flags: GameEffect.EffectFlags.Harmful | GameEffect.EffectFlags.TickOnApplication
            );
            effect.tickInterval = tickInterval;
            effect.lastTickTime = currentTime;
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a heal over time effect
        /// </summary>
        public static void ApplyHealOverTimeEffect(DynamicBuffer<GameEffect> effects, dfloat healingPerTick,
            dfloat duration, dfloat tickInterval, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.HealOverTime,
                healingPerTick,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Independent,
                flags: GameEffect.EffectFlags.Beneficial | GameEffect.EffectFlags.TickOnApplication
            );
            effect.tickInterval = tickInterval;
            effect.lastTickTime = currentTime;
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a shield effect
        /// </summary>
        public static void ApplyShieldEffect(DynamicBuffer<GameEffect> effects, dfloat shieldAmount,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var effect = CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.Shield,
                shieldAmount,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Refresh,
                flags: GameEffect.EffectFlags.Beneficial
            );
            ApplyEffect(effects, effect);
        }

        /// <summary>
        /// Apply a poison effect (DOT with resistance check)
        /// </summary>
        public static void ApplyPoisonEffect(DynamicBuffer<GameEffect> effects, dfloat damagePerTick,
            dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            var tickInterval = new dfloat(1.0f); // 1 second ticks
            var effect = CreateEffect(
                GameEffect.EffectCategory.Health,
                GameEffect.EffectType.Poison,
                damagePerTick,
                duration,
                currentTime,
                sourceId,
                GameEffect.StackingType.Additive,
                maxStacks: 5,
                flags: GameEffect.EffectFlags.Harmful | GameEffect.EffectFlags.Dispellable
            );
            effect.tickInterval = tickInterval;
            effect.lastTickTime = currentTime;
            ApplyEffect(effects, effect);
        }

        // ===== UTILITY METHODS =====

        /// <summary>
        /// Remove all effects from a specific source
        /// </summary>
        public static void RemoveEffectsFromSource(DynamicBuffer<GameEffect> effects, int sourceId)
        {
            EffectStackingUtils.RemoveEffectsFromSource(effects, sourceId);
        }

        /// <summary>
        /// Remove all effects of a specific type
        /// </summary>
        public static void RemoveEffectsOfType(DynamicBuffer<GameEffect> effects, GameEffect.EffectType effectType)
        {
            EffectStackingUtils.RemoveEffectsOfType(effects, effectType);
        }

        /// <summary>
        /// Remove all effects of a specific category
        /// </summary>
        public static void RemoveEffectsOfCategory(DynamicBuffer<GameEffect> effects, GameEffect.EffectCategory category)
        {
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                if (effects[i].category == category)
                {
                    effects.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Remove all dispellable effects
        /// </summary>
        public static void DispelEffects(DynamicBuffer<GameEffect> effects, bool beneficialOnly = false, bool harmfulOnly = false)
        {
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                var effect = effects[i];
                if (!effect.HasFlag(GameEffect.EffectFlags.Dispellable)) continue;

                if (beneficialOnly && !effect.HasFlag(GameEffect.EffectFlags.Beneficial)) continue;
                if (harmfulOnly && !effect.HasFlag(GameEffect.EffectFlags.Harmful)) continue;

                effects.RemoveAt(i);
            }
        }

        /// <summary>
        /// Get the total number of active effects
        /// </summary>
        public static int GetActiveEffectCount(DynamicBuffer<GameEffect> effects, dfloat currentTime)
        {
            int count = 0;
            for (int i = 0; i < effects.Length; i++)
            {
                if (!effects[i].IsExpired(currentTime))
                    count++;
            }
            return count;
        }

        /// <summary>
        /// Check if entity has a specific effect type
        /// </summary>
        public static bool HasEffectType(DynamicBuffer<GameEffect> effects, GameEffect.EffectType effectType, dfloat currentTime)
        {
            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                if (effect.effectType == effectType && !effect.IsExpired(currentTime))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// Get the strongest effect of a specific type
        /// </summary>
        public static GameEffect? GetStrongestEffect(DynamicBuffer<GameEffect> effects, GameEffect.EffectType effectType, dfloat currentTime)
        {
            GameEffect? strongest = null;
            dfloat strongestValue = dfloat.Zero;

            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                if (effect.effectType == effectType && !effect.IsExpired(currentTime))
                {
                    if (!strongest.HasValue || dmath.abs(effect.primaryValue) > strongestValue)
                    {
                        strongest = effect;
                        strongestValue = dmath.abs(effect.primaryValue);
                    }
                }
            }

            return strongest;
        }
    }
}
