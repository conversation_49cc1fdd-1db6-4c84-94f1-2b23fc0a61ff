using FlowField;
using Unity.Entities;
using Unity.Deterministic.Mathematics;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Utility class for adding effect components to entities
    /// Provides migration support and convenience methods for unit creation
    /// </summary>
    public static class EffectComponentUtils
    {
        /// <summary>
        /// Add all effect components to a unit entity
        /// This replaces the old SpeedModifier system setup
        /// </summary>
        public static void AddEffectComponents(EntityManager entityManager, Entity unit, UnitStats stats)
        {
            // Create effect components from UnitStats
            var movementEffects = MovementEffects.CreateFromUnitStats(stats);
            var combatEffects = CombatEffects.CreateFromUnitStats(stats);
            var healthEffects = HealthEffects.CreateFromUnitStats(stats);

            // Add effect components
            entityManager.AddComponentData(unit, movementEffects);
            entityManager.AddComponentData(unit, combatEffects);
            entityManager.AddComponentData(unit, healthEffects);

            // Add the GameEffect buffer for storing active effects
            entityManager.AddBuffer<GameEffect>(unit);
        }



        /// <summary>
        /// Check if an entity has the new effect system components
        /// </summary>
        public static bool HasEffectComponents(EntityManager entityManager, Entity unit)
        {
            return entityManager.HasComponent<MovementEffects>(unit) &&
                   entityManager.HasComponent<CombatEffects>(unit) &&
                   entityManager.HasComponent<HealthEffects>(unit) &&
                   entityManager.HasBuffer<GameEffect>(unit);
        }



        /// <summary>
        /// Apply a quick effect to a unit using the new system
        /// Convenience method for common effect applications
        /// </summary>
        public static void ApplyQuickEffect(EntityManager entityManager, Entity unit, 
            GameEffect.EffectType effectType, dfloat value, dfloat duration, dfloat currentTime, int sourceId = 0)
        {
            if (!entityManager.HasBuffer<GameEffect>(unit)) return;

            var effectBuffer = entityManager.GetBuffer<GameEffect>(unit);
            
            var effect = GameEffectUtils.CreateEffect(
                GetCategoryForEffectType(effectType),
                effectType,
                value,
                duration,
                currentTime,
                sourceId
            );

            GameEffectUtils.ApplyEffect(effectBuffer, effect);
        }

        /// <summary>
        /// Get the appropriate category for an effect type
        /// </summary>
        private static GameEffect.EffectCategory GetCategoryForEffectType(GameEffect.EffectType effectType)
        {
            return effectType switch
            {
                GameEffect.EffectType.MovementSpeed or 
                GameEffect.EffectType.Acceleration or 
                GameEffect.EffectType.Deceleration or 
                GameEffect.EffectType.Stun or 
                GameEffect.EffectType.Root or 
                GameEffect.EffectType.Slow or 
                GameEffect.EffectType.Haste or 
                GameEffect.EffectType.TerrainModifier => GameEffect.EffectCategory.Movement,

                GameEffect.EffectType.AttackDamage or 
                GameEffect.EffectType.AttackSpeed or 
                GameEffect.EffectType.AttackRange or 
                GameEffect.EffectType.CriticalChance or 
                GameEffect.EffectType.CriticalDamage or 
                GameEffect.EffectType.ArmorPenetration or 
                GameEffect.EffectType.LifeSteal or 
                GameEffect.EffectType.AttackBuff or 
                GameEffect.EffectType.AttackDebuff => GameEffect.EffectCategory.Combat,

                GameEffect.EffectType.DamageOverTime or 
                GameEffect.EffectType.HealOverTime or 
                GameEffect.EffectType.Shield or 
                GameEffect.EffectType.Regeneration or 
                GameEffect.EffectType.MaxHealthModifier or 
                GameEffect.EffectType.DamageReduction or 
                GameEffect.EffectType.Vulnerability or 
                GameEffect.EffectType.Poison or 
                GameEffect.EffectType.Burn or 
                GameEffect.EffectType.Bleed => GameEffect.EffectCategory.Health,

                GameEffect.EffectType.CooldownReduction or 
                GameEffect.EffectType.ManaCostReduction or 
                GameEffect.EffectType.AbilityRange or 
                GameEffect.EffectType.AbilityDamage or 
                GameEffect.EffectType.CastSpeed or 
                GameEffect.EffectType.ManaRegeneration => GameEffect.EffectCategory.Abilities,

                _ => GameEffect.EffectCategory.Visual
            };
        }
    }
}
