using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Effects
{
    /// <summary>
    /// Utilities for handling effect stacking behaviors
    /// </summary>
    public static class EffectStackingUtils
    {
        /// <summary>
        /// Apply stacking logic when adding a new effect
        /// </summary>
        public static void ApplyStackingLogic(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            switch (newEffect.stackingType)
            {
                case GameEffect.StackingType.None:
                    HandleNonStackingEffect(effects, newEffect);
                    break;
                case GameEffect.StackingType.Additive:
                    HandleAdditiveStacking(effects, newEffect);
                    break;
                case GameEffect.StackingType.Multiplicative:
                    HandleMultiplicativeStacking(effects, newEffect);
                    break;
                case GameEffect.StackingType.Refresh:
                    HandleRefreshStacking(effects, newEffect);
                    break;
                case GameEffect.StackingType.Independent:
                    HandleIndependentStacking(effects, newEffect);
                    break;
            }
        }

        /// <summary>
        /// Handle non-stacking effects (replace existing)
        /// </summary>
        private static void HandleNonStackingEffect(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            // Remove existing effects of the same type from the same source
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                var existing = effects[i];
                if (existing.effectType == newEffect.effectType && 
                    existing.sourceId == newEffect.sourceId)
                {
                    effects.RemoveAt(i);
                }
            }

            // Add the new effect
            effects.Add(newEffect);
        }

        /// <summary>
        /// Handle additive stacking effects
        /// </summary>
        private static void HandleAdditiveStacking(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            // Find existing effect of same type from same source
            for (int i = 0; i < effects.Length; i++)
            {
                var existing = effects[i];
                if (existing.effectType == newEffect.effectType && 
                    existing.sourceId == newEffect.sourceId)
                {
                    // Check if we can add more stacks
                    if (existing.stackCount < existing.maxStacks)
                    {
                        var updated = existing;
                        updated.stackCount++;
                        updated.primaryValue += newEffect.primaryValue;
                        updated.duration = dmath.max(existing.duration, newEffect.duration);
                        updated.startTime = newEffect.startTime; // Refresh start time
                        effects[i] = updated;
                        return;
                    }
                    else
                    {
                        // At max stacks, just refresh duration
                        var updated = existing;
                        updated.duration = dmath.max(existing.duration, newEffect.duration);
                        updated.startTime = newEffect.startTime;
                        effects[i] = updated;
                        return;
                    }
                }
            }

            // No existing effect found, add new one
            newEffect.stackCount = 1;
            effects.Add(newEffect);
        }

        /// <summary>
        /// Handle multiplicative stacking effects
        /// </summary>
        private static void HandleMultiplicativeStacking(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            // Find existing effect of same type from same source
            for (int i = 0; i < effects.Length; i++)
            {
                var existing = effects[i];
                if (existing.effectType == newEffect.effectType && 
                    existing.sourceId == newEffect.sourceId)
                {
                    // Check if we can add more stacks
                    if (existing.stackCount < existing.maxStacks)
                    {
                        var updated = existing;
                        updated.stackCount++;
                        updated.primaryValue *= newEffect.primaryValue;
                        updated.duration = dmath.max(existing.duration, newEffect.duration);
                        updated.startTime = newEffect.startTime;
                        effects[i] = updated;
                        return;
                    }
                    else
                    {
                        // At max stacks, just refresh duration
                        var updated = existing;
                        updated.duration = dmath.max(existing.duration, newEffect.duration);
                        updated.startTime = newEffect.startTime;
                        effects[i] = updated;
                        return;
                    }
                }
            }

            // No existing effect found, add new one
            newEffect.stackCount = 1;
            effects.Add(newEffect);
        }

        /// <summary>
        /// Handle refresh stacking effects (keep highest value, refresh duration)
        /// </summary>
        private static void HandleRefreshStacking(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            // Find existing effect of same type from same source
            for (int i = 0; i < effects.Length; i++)
            {
                var existing = effects[i];
                if (existing.effectType == newEffect.effectType && 
                    existing.sourceId == newEffect.sourceId)
                {
                    var updated = existing;
                    updated.primaryValue = dmath.max(existing.primaryValue, newEffect.primaryValue);
                    updated.secondaryValue = dmath.max(existing.secondaryValue, newEffect.secondaryValue);
                    updated.duration = newEffect.duration; // Always use new duration
                    updated.startTime = newEffect.startTime; // Refresh start time
                    effects[i] = updated;
                    return;
                }
            }

            // No existing effect found, add new one
            effects.Add(newEffect);
        }

        /// <summary>
        /// Handle independent stacking effects (each effect applies separately)
        /// </summary>
        private static void HandleIndependentStacking(DynamicBuffer<GameEffect> effects, GameEffect newEffect)
        {
            // Check if we have a unique effect that already exists
            if (newEffect.HasFlag(GameEffect.EffectFlags.Unique))
            {
                for (int i = effects.Length - 1; i >= 0; i--)
                {
                    var existing = effects[i];
                    if (existing.effectType == newEffect.effectType && 
                        existing.sourceId == newEffect.sourceId)
                    {
                        effects.RemoveAt(i);
                    }
                }
            }

            // Always add the new effect
            effects.Add(newEffect);
        }

        /// <summary>
        /// Calculate total effect value for a specific effect type
        /// </summary>
        public static dfloat CalculateTotalEffectValue(DynamicBuffer<GameEffect> effects, 
            GameEffect.EffectType effectType, dfloat currentTime)
        {
            dfloat totalValue = dfloat.Zero;
            dfloat multiplicativeValue = dfloat.One;
            bool hasMultiplicative = false;

            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                if (effect.effectType != effectType || effect.IsExpired(currentTime))
                    continue;

                switch (effect.stackingType)
                {
                    case GameEffect.StackingType.Additive:
                        totalValue += effect.primaryValue * effect.stackCount;
                        break;
                    case GameEffect.StackingType.Multiplicative:
                        multiplicativeValue *= dmath.pow(effect.primaryValue, (dfloat) effect.stackCount);
                        hasMultiplicative = true;
                        break;
                    case GameEffect.StackingType.None:
                    case GameEffect.StackingType.Refresh:
                    case GameEffect.StackingType.Independent:
                        totalValue += effect.primaryValue;
                        break;
                }
            }

            // Apply multiplicative effects to the total
            if (hasMultiplicative)
            {
                totalValue *= multiplicativeValue;
            }

            return totalValue;
        }

        /// <summary>
        /// Get all active effects of a specific type
        /// </summary>
        public static void GetActiveEffects(DynamicBuffer<GameEffect> effects, 
            GameEffect.EffectType effectType, dfloat currentTime, 
            NativeList<GameEffect> activeEffects)
        {
            activeEffects.Clear();
            
            for (int i = 0; i < effects.Length; i++)
            {
                var effect = effects[i];
                if (effect.effectType == effectType && !effect.IsExpired(currentTime))
                {
                    activeEffects.Add(effect);
                }
            }
        }

        /// <summary>
        /// Remove all effects from a specific source
        /// </summary>
        public static void RemoveEffectsFromSource(DynamicBuffer<GameEffect> effects, int sourceId)
        {
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                if (effects[i].sourceId == sourceId)
                {
                    effects.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Remove all effects of a specific type
        /// </summary>
        public static void RemoveEffectsOfType(DynamicBuffer<GameEffect> effects, 
            GameEffect.EffectType effectType)
        {
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                if (effects[i].effectType == effectType)
                {
                    effects.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Remove expired effects
        /// </summary>
        public static void RemoveExpiredEffects(DynamicBuffer<GameEffect> effects, dfloat currentTime)
        {
            for (int i = effects.Length - 1; i >= 0; i--)
            {
                if (effects[i].IsExpired(currentTime))
                {
                    effects.RemoveAt(i);
                }
            }
        }
    }
}
