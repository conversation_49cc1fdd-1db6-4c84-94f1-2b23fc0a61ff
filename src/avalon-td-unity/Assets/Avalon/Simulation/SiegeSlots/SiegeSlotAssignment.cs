using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// Status of an enemy's siege slot assignment
    /// </summary>
    public enum SiegeSlotStatus : byte
    {
        /// <summary>No assignment - enemy is not targeting a siege slot entity</summary>
        None = 0,

        /// <summary>Enemy has reserved a slot but is not yet assigned to move to it</summary>
        SlotReserved = 1,

        /// <summary>Enemy is moving to an assigned siege slot</summary>
        MovingToSlot = 2,

        /// <summary>Enemy has reached and is occupying a siege slot</summary>
        OccupyingSlot = 3
    }
    
    /// <summary>
    /// Component that tracks an enemy's siege slot assignment and status.
    /// This supplements the MovementTarget component for enemies targeting entities with siege slots.
    /// No queuing system - enemies either have a slot or continue with normal movement.
    /// </summary>
    public struct SiegeSlotAssignment : IComponentData
    {
        /// <summary>
        /// The target entity that has siege slots
        /// </summary>
        public Entity targetEntity;

        /// <summary>
        /// The assigned siege slot index (-1 if no slot assigned)
        /// </summary>
        public int assignedSlotIndex;

        /// <summary>
        /// Current status of the siege slot assignment
        /// </summary>
        public SiegeSlotStatus status;

        /// <summary>
        /// The calculated position this enemy should move to (slot position)
        /// </summary>
        public dfloat3 SlotPosition;

        public dfloat3 TargetPosition;

        /// <summary>
        /// Time when this assignment was created or last updated
        /// Used for deterministic ordering and timeout handling
        /// </summary>
        public dfloat assignmentTime;

        /// <summary>
        /// Enemy's rank/priority for trampling calculations
        /// Higher values indicate higher priority
        /// </summary>
        public dfloat enemyRank;

        /// <summary>
        /// Distance threshold to consider the enemy has reached their target position
        /// </summary>
        public dfloat arrivalThreshold;
        
        /// <summary>
        /// Create a new siege slot assignment
        /// </summary>
        public static SiegeSlotAssignment Create(Entity target, dfloat currentTime, dfloat rank)
        {
            return new SiegeSlotAssignment
            {
                targetEntity = target,
                assignedSlotIndex = -1,
                status = SiegeSlotStatus.None,
                TargetPosition = dfloat3.zero,
                SlotPosition = dfloat3.zero,
                assignmentTime = currentTime,
                enemyRank = rank,
                arrivalThreshold = new dfloat(0.5f) // Default arrival threshold
            };
        }

        /// <summary>
        /// Reserve a specific siege slot for this enemy
        /// </summary>
        public void ReserveSlot(int slotIndex, dfloat3 targetPosition, dfloat currentTime)
        {
            assignedSlotIndex = slotIndex;
            TargetPosition = targetPosition;
            SlotPosition = dfloat3.zero; // Will be set when assigned to move
            status = SiegeSlotStatus.SlotReserved;
            assignmentTime = currentTime;
        }

        /// <summary>
        /// Assign this enemy to move to their reserved slot
        /// </summary>
        public void AssignToSlot(int slotIndex, dfloat3 targetPosition, dfloat3 slotPosition, dfloat currentTime)
        {
            assignedSlotIndex = slotIndex;
            TargetPosition = targetPosition;
            SlotPosition = slotPosition;
            status = SiegeSlotStatus.MovingToSlot;
            assignmentTime = currentTime;
        }
        
        /// <summary>
        /// Mark this enemy as having reached their slot
        /// </summary>
        public void MarkSlotOccupied()
        {
            if (status == SiegeSlotStatus.MovingToSlot)
            {
                status = SiegeSlotStatus.OccupyingSlot;
            }
        }
        
        /// <summary>
        /// Check if this enemy has reached their target position
        /// </summary>
        public bool HasReachedTarget(dfloat3 currentPosition)
        {
            dfloat distance = dmath.distance(currentPosition, SlotPosition);
            return distance <= arrivalThreshold;
        }
        
        /// <summary>
        /// Check if this assignment is valid (has a valid target)
        /// </summary>
        public bool IsValid()
        {
            return targetEntity != Entity.Null && status != SiegeSlotStatus.None;
        }
        
        /// <summary>
        /// Check if this enemy is currently occupying a slot
        /// </summary>
        public bool IsOccupyingSlot()
        {
            return status == SiegeSlotStatus.OccupyingSlot && assignedSlotIndex >= 0;
        }
        
        /// <summary>
        /// Check if this enemy is moving to slot
        /// </summary>
        public bool IsMoving()
        {
            return status == SiegeSlotStatus.MovingToSlot;
        }

        /// <summary>
        /// Check if this enemy has a slot reserved
        /// </summary>
        public bool HasSlotReserved()
        {
            return status == SiegeSlotStatus.SlotReserved && assignedSlotIndex >= 0;
        }

        /// <summary>
        /// Check if this enemy has any slot assignment (reserved, moving, or occupying)
        /// </summary>
        public bool HasAnySlotAssignment()
        {
            return status != SiegeSlotStatus.None && assignedSlotIndex >= 0;
        }

        /// <summary>
        /// Clear the assignment (enemy is no longer targeting this siege slot entity)
        /// </summary>
        public void Clear()
        {
            targetEntity = Entity.Null;
            assignedSlotIndex = -1;
            status = SiegeSlotStatus.None;
            SlotPosition = dfloat3.zero;
            TargetPosition = dfloat3.zero;
        }
    }
}
