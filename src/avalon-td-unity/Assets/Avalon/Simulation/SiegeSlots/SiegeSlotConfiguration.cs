using Unity.Deterministic.Mathematics;
using Unity.Entities;
using UnityEngine;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// ScriptableObject definition for siege slot configurations.
    /// This allows designers to configure different siege slot setups for different target types.
    /// </summary>
    [CreateAssetMenu(fileName = "New Siege Slot Config", menuName = "Avalon/Siege Slots/Configuration")]
    public class SiegeSlotConfiguration : ScriptableObject
    {
        [Header("Basic Settings")]
        [Tooltip("Number of siege slots around the target")]
        [Range(1, 32)]
        public int slotCount = 8;
        
        [Tooltip("Distance from target center where slots are positioned")]
        [Range(0.5f, 10.0f)]
        public float slotRadius = 2.0f;
        
        [Tooltip("Angle offset for the first slot (in degrees)")]
        [Range(0f, 360f)]
        public float angleOffset = 0f;
        
        [Header("Queue Settings")]
        [Tooltip("Formation pattern for queued enemies")]
        public SiegeQueuePositioning.QueueFormation queueFormation = SiegeQueuePositioning.QueueFormation.SingleLine;
        
        [Tooltip("Base distance from siege slots where queue starts")]
        [Range(0.5f, 5.0f)]
        public float queueBaseDistance = 1.5f;
        
        [Tooltip("Spacing between queued enemies")]
        [Range(0.5f, 3.0f)]
        public float queueSpacing = 1.0f;
        
        [Tooltip("Spacing between queue rows (for multi-row formations)")]
        [Range(0.5f, 3.0f)]
        public float queueRowSpacing = 1.2f;
        
        [Tooltip("Maximum enemies per row in queue formations")]
        [Range(1, 10)]
        public int maxEnemiesPerRow = 4;
        
        [Header("Distance Settings")]
        [Tooltip("Distance at which enemies reserve slots (should be larger than assignment distance)")]
        [Range(2.0f, 15.0f)]
        public float slotReservationDistance = 6.0f;

        [Tooltip("Distance at which enemies are assigned to move to their reserved slots")]
        [Range(1.0f, 10.0f)]
        public float slotAssignmentDistance = 3.0f;

        [Header("Trampling Settings")]
        [Tooltip("Minimum rank difference required for trampling")]
        [Range(0.01f, 1.0f)]
        public float trampleThreshold = 0.1f;

        [Tooltip("Whether trampling is enabled for this target type")]
        public bool allowTrampling = true;
        
        [Header("Advanced Settings")]
        [Tooltip("Whether siege slots are active by default")]
        public bool activeByDefault = true;
        
        [Tooltip("Custom weights for enemy ranking calculations")]
        public EnemyRankingWeights rankingWeights = EnemyRankingWeights.Default;
        
        /// <summary>
        /// Convert this configuration to a SiegeSlotData component
        /// </summary>
        public SiegeSlotData ToSiegeSlotData()
        {
            return new SiegeSlotData
            {
                slotCount = slotCount,
                slotRadius = new dfloat(slotRadius),
                angleOffset = new dfloat(angleOffset * Mathf.Deg2Rad), // Convert to radians
                isActive = activeByDefault,
                occupiedSlotsMask = 0L,
                reservedSlotsMask = 0L
            };
        }
        
        /// <summary>
        /// Get the queue configuration from this siege slot configuration
        /// </summary>
        public SiegeQueuePositioning.QueueConfig GetQueueConfig()
        {
            return new SiegeQueuePositioning.QueueConfig
            {
                formation = queueFormation,
                baseDistance = new dfloat(queueBaseDistance),
                spacing = new dfloat(queueSpacing),
                rowSpacing = new dfloat(queueRowSpacing),
                maxPerRow = maxEnemiesPerRow
            };
        }
        
        /// <summary>
        /// Get the enemy ranking weights from this configuration
        /// </summary>
        public EnemyRanking.RankingWeights GetRankingWeights()
        {
            return new EnemyRanking.RankingWeights
            {
                healthWeight = new dfloat(rankingWeights.healthWeight),
                damageWeight = new dfloat(rankingWeights.damageWeight),
                speedWeight = new dfloat(rankingWeights.speedWeight),
                massWeight = new dfloat(rankingWeights.massWeight)
            };
        }
    }
    
    /// <summary>
    /// Serializable struct for enemy ranking weights in the inspector
    /// </summary>
    [System.Serializable]
    public struct EnemyRankingWeights
    {
        [Tooltip("Weight for health in ranking calculations")]
        [Range(0f, 5f)]
        public float healthWeight;
        
        [Tooltip("Weight for damage in ranking calculations")]
        [Range(0f, 5f)]
        public float damageWeight;
        
        [Tooltip("Weight for speed in ranking calculations")]
        [Range(0f, 5f)]
        public float speedWeight;
        
        [Tooltip("Weight for mass in ranking calculations")]
        [Range(0f, 5f)]
        public float massWeight;

        public static EnemyRankingWeights Default => new EnemyRankingWeights
        {
            healthWeight = 1.0f,
            damageWeight = 1.5f,
            speedWeight = 0.5f,
            massWeight = 0.8f
        };
    }

    /// <summary>
    /// Component that stores siege slot behavior settings for a target entity.
    /// This is derived from SiegeSlotConfiguration and used by the SiegeSlotManager.
    /// </summary>
    public struct SiegeSlotSettings : IComponentData
    {
        /// <summary>
        /// Distance at which enemies reserve slots
        /// </summary>
        public dfloat slotReservationDistance;

        /// <summary>
        /// Distance at which enemies are assigned to move to their reserved slots
        /// </summary>
        public dfloat slotAssignmentDistance;

        /// <summary>
        /// Minimum rank difference required for trampling
        /// </summary>
        public dfloat trampleThreshold;

        /// <summary>
        /// Whether trampling is enabled
        /// </summary>
        public bool allowTrampling;

        /// <summary>
        /// Create default settings
        /// </summary>
        public static SiegeSlotSettings CreateDefault()
        {
            return new SiegeSlotSettings
            {
                slotReservationDistance = new dfloat(6.0f),
                slotAssignmentDistance = new dfloat(3.0f),
                trampleThreshold = new dfloat(0.1f),
                allowTrampling = true
            };
        }
    }

    /// <summary>
    /// Component that references a siege slot configuration.
    /// Add this to target entities to specify their siege slot behavior.
    /// </summary>
    public struct SiegeSlotConfigurationRef : IComponentData
    {
        /// <summary>
        /// Reference to the siege slot configuration asset
        /// </summary>
        public int configurationId;
        
        /// <summary>
        /// Whether the configuration has been applied to the entity
        /// </summary>
        public bool isInitialized;
    }
    
    /// <summary>
    /// Authoring component for setting up siege slots in the editor
    /// </summary>
    public class SiegeSlotAuthoring : MonoBehaviour
    {
        [Header("Siege Slot Configuration")]
        public SiegeSlotConfiguration configuration;
        
        [Header("Override Settings")]
        [Tooltip("Override the slot count from the configuration")]
        public bool overrideSlotCount = false;
        
        [Range(1, 32)]
        public int customSlotCount = 8;
        
        [Tooltip("Override the slot radius from the configuration")]
        public bool overrideSlotRadius = false;
        
        [Range(0.5f, 10.0f)]
        public float customSlotRadius = 2.0f;
        
        [Header("Debug Visualization")]
        [Tooltip("Show siege slot positions in the scene view")]
        public bool showSlotPositions = true;
        
        [Tooltip("Show queue area in the scene view")]
        public bool showQueueArea = true;
        
        // public class Baker : Baker<SiegeSlotAuthoring>
        // {
        //     public override void Bake(SiegeSlotAuthoring authoring)
        //     {
        //         var entity = GetEntity(TransformUsageFlags.Dynamic);
        //         
        //         if (authoring.configuration != null)
        //         {
        //             // Create siege slot data from configuration
        //             var siegeSlotData = authoring.configuration.ToSiegeSlotData();
        //             
        //             // Apply overrides if specified
        //             if (authoring.overrideSlotCount)
        //             {
        //                 siegeSlotData.slotCount = authoring.customSlotCount;
        //             }
        //             
        //             if (authoring.overrideSlotRadius)
        //             {
        //                 siegeSlotData.slotRadius = new dfloat(authoring.customSlotRadius);
        //             }
        //             
        //             AddComponent(entity, siegeSlotData);
        //             
        //             // Add configuration reference for runtime access
        //             AddComponent(entity, new SiegeSlotConfigurationRef
        //             {
        //                 configurationId = authoring.configuration.GetInstanceID(),
        //                 isInitialized = true
        //             });
        //         }
        //         else
        //         {
        //             // Create default siege slot data
        //             AddComponent(entity, SiegeSlotData.CreateDefault());
        //         }
        //     }
        // }
        
        private void OnDrawGizmosSelected()
        {
            if (configuration == null) return;
            
            var position = transform.position;
            var siegeSlotData = configuration.ToSiegeSlotData();
            
            // Apply overrides for visualization
            if (overrideSlotCount)
                siegeSlotData.slotCount = customSlotCount;
            if (overrideSlotRadius)
                siegeSlotData.slotRadius = new dfloat(customSlotRadius);
            
            // Draw siege slot positions
            if (showSlotPositions)
            {
                Gizmos.color = Color.red;
                for (int i = 0; i < siegeSlotData.slotCount; i++)
                {
                    var slotPos = siegeSlotData.GetSlotPosition(i, new dfloat3((dfloat)position.x, (dfloat)position.y, (dfloat)position.z));
                    Gizmos.DrawWireSphere(new Vector3((float)slotPos.x, (float)slotPos.y, (float)slotPos.z), 0.3f);
                    
                    // Draw line from center to slot
                    Gizmos.color = Color.yellow;
                    Gizmos.DrawLine(position, new Vector3((float)slotPos.x, (float)slotPos.y, (float)slotPos.z));
                }
            }
            
            // Draw queue area
            if (showQueueArea)
            {
                var queueConfig = configuration.GetQueueConfig();
                var queueArea = SiegeQueuePositioning.CalculateQueueArea(8, queueConfig); // Assume 8 queued enemies for visualization
                
                Gizmos.color = Color.blue;
                var queueCenter = position + Vector3.back * ((float)siegeSlotData.slotRadius + (float)queueConfig.baseDistance);
                Gizmos.DrawWireCube(queueCenter, new Vector3((float)queueArea.x, 0.1f, (float)queueArea.y));
            }
        }
    }
}
