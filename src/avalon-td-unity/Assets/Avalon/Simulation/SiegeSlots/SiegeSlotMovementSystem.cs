using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Deterministic.Mathematics;
using FlowField;
using Avalon.Simulation.Movement;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// This system has been replaced by UnifiedMovementSystem.
    /// The old SiegeSlotMovementSystem functionality is now integrated into the unified system.
    /// </summary>
    
    /// <summary>
    /// System that initializes siege slot assignments for enemies targeting entities with siege slots.
    /// This system runs once when enemies are spawned or change targets.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(SiegeSlotManager))]
    public partial struct SiegeSlotInitializationSystem : ISystem
    {
        private EntityQuery newEnemiesQuery;
        private EntityQuery fixedTimestepQuery;
        private ComponentLookup<SiegeSlotData> siegeSlotLookup;
        private ComponentLookup<SimulationTransform> transformLookup;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for enemies that have MovementTarget but no SiegeSlotAssignment yet
            newEnemiesQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<MovementTarget>(),
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.Exclude<SiegeSlotAssignment>()
            );

            // Query for fixed timestep singleton
            fixedTimestepQuery = state.GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());

            siegeSlotLookup = state.GetComponentLookup<SiegeSlotData>(true);
            transformLookup = state.GetComponentLookup<SimulationTransform>(true);

            // Require fixed timestep to exist
            state.RequireForUpdate<FixedTimestep>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;

            if (newEnemiesQuery.IsEmpty)
                return;

            siegeSlotLookup.Update(ref state);
            transformLookup.Update(ref state);

            dfloat currentTime = fixedTimestep.currentTime;
            var ecb = new EntityCommandBuffer(Allocator.TempJob);
            
            // Process new enemies and check if their targets have siege slots
            foreach (var (movementTarget, unitStats, entity) in 
                SystemAPI.Query<RefRO<MovementTarget>, RefRO<UnitStats>>()
                .WithEntityAccess()
                .WithNone<SiegeSlotAssignment>())
            {
                if (movementTarget.ValueRO.targetId == null)
                    continue;
                
                // Find the target entity with this targetId
                Entity targetEntity = FindTargetEntity(ref state, movementTarget.ValueRO.targetId.Value);
                
                if (targetEntity != Entity.Null && siegeSlotLookup.HasComponent(targetEntity))
                {
                    var siegeSlotData = siegeSlotLookup[targetEntity];
                    
                    if (siegeSlotData.isActive)
                    {
                        // Create siege slot assignment for this enemy
                        dfloat enemyRank = EnemyRanking.CalculateEnemyRank(unitStats.ValueRO);
                        var assignment = SiegeSlotAssignment.Create(targetEntity, currentTime, enemyRank);
                        
                        ecb.AddComponent(entity, assignment);
                    }
                }
            }
            
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }
        
        private Entity FindTargetEntity(ref SystemState state, int targetId)
        {
            // This is a simplified approach - in a real implementation, you might want to maintain
            // a lookup table of targetId -> Entity for better performance
            foreach (var (flowFieldGrid, entity) in 
                SystemAPI.Query<RefRO<FlowFieldGrid>>().WithEntityAccess())
            {
                if (flowFieldGrid.ValueRO.targetId == targetId)
                {
                    return flowFieldGrid.ValueRO.targetEntity;
                }
            }
            
            return Entity.Null;
        }
    }
}
