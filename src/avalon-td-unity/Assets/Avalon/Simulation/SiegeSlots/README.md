# Siege Slot System

The Siege Slot System provides a deterministic way for enemies to position themselves around targets in organized formations rather than crowding at the exact target position. This system includes slot reservation, assignment, and trampling mechanics where higher-ranked enemies can displace lower-ranked ones.

## Overview

When enemies target an entity with siege slots, instead of all moving to the exact target position, they:
1. Reserve specific slots when they reach the reservation distance
2. Get assigned to move to their reserved slots when they reach the assignment distance
3. Can "trample" (displace) lower-ranked enemies to take their slots when all slots are occupied or reserved
4. Move at constant speed in a deterministic, organized manner

## Core Components

### SiegeSlotData
Defines siege slots for a target entity:
- `slotCount`: Number of slots around the target
- `slotRadius`: Distance from target center where slots are positioned
- `angleOffset`: Rotation offset for the slot arrangement
- `occupiedSlotsMask`: Bitmask tracking which slots are occupied
- `reservedSlotsMask`: Bitmask tracking which slots are reserved

### SiegeSlotAssignment
Tracks an enemy's siege slot assignment:
- `targetEntity`: The target with siege slots
- `assignedSlotIndex`: Which slot is assigned (-1 if none)
- `status`: Current status (none, reserved, moving to slot, occupying)
- `targetPosition`: Where the enemy should move to
- `enemyRank`: Priority for trampling calculations

### SiegeSlotSettings
Configurable parameters for siege slot behavior:
- `slotReservationDistance`: Distance at which enemies reserve slots
- `slotAssignmentDistance`: Distance at which enemies are assigned to move to slots
- `trampleThreshold`: Minimum rank difference required for trampling
- `allowTrampling`: Whether trampling is enabled

### SiegeSlotConfiguration
ScriptableObject for configuring siege slot behavior:
- Basic slot settings (count, radius, angle offset)
- Distance settings (reservation distance, assignment distance)
- Queue formation settings (pattern, spacing, rows)
- Trampling settings (threshold, weights, enable/disable)

## Key Systems

### SiegeSlotManager
Core system that:
- Reserves slots for enemies at reservation distance
- Assigns enemies to move to reserved slots at assignment distance
- Handles trampling logic when all slots are occupied or reserved
- Uses configurable distances and settings for deterministic behavior
- Updates slot occupancy and reservation states

### UnifiedMovementSystem
Movement system that:
- Makes enemies move to their assigned slot positions at constant speed
- Uses regular flow field movement for enemies with reserved slots
- Integrates siege slot movement with flow field and avoidance systems
- Ensures constant speed movement without acceleration/deceleration

### SiegeSlotInitializationSystem
Initialization system that:
- Detects when enemies target entities with siege slots
- Creates SiegeSlotAssignment components for eligible enemies
- Calculates initial enemy rankings

## Enemy Ranking

Enemies are ranked based on their UnitStats using configurable weights:
- **Health Weight**: Contribution of max health to rank
- **Damage Weight**: Contribution of attack damage to rank  
- **Speed Weight**: Contribution of movement speed to rank
- **Mass Weight**: Contribution of mass to rank

Higher-ranked enemies can "trample" lower-ranked ones when all slots are occupied or reserved, killing them and taking their slot.

## Slot Reservation System

The system now uses a two-phase approach:

1. **Reservation Phase**: When enemies reach the reservation distance (configurable, default 6.0f), they reserve the closest available slot
2. **Assignment Phase**: When enemies with reserved slots reach the assignment distance (configurable, default 3.0f), they are assigned to move to their slot
3. **Trampling Phase**: When all slots are occupied or reserved, higher-ranked enemies can trample lower-ranked ones

This ensures more organized movement patterns and prevents conflicts over slots.

## Movement Behavior

- **Constant Speed**: All enemies move at constant `maxSpeed` without acceleration or deceleration
- **Reserved Slot Movement**: Enemies with reserved slots use normal flow field movement until assigned
- **Assigned Slot Movement**: Enemies assigned to move to slots move directly toward their slot position
- **Deterministic**: All calculations use deterministic fixed-point math for consistent simulation results

## Legacy Queue System (Deprecated)

The previous queue system has been replaced with the reservation system. When all siege slots were occupied, enemies would wait in organized formations:
- **Single Line**: Enemies line up behind the target
- **Double Line**: Two parallel lines
- **Semicircle**: Arc formation behind the target
- **Grid**: Rectangular grid formation

## Usage

### Setting Up Siege Slots

1. **Using SiegeSlotAuthoring Component:**
   ```csharp
   // Add SiegeSlotAuthoring to a GameObject
   // Assign a SiegeSlotConfiguration asset
   // Configure overrides if needed
   ```

2. **Programmatically:**
   ```csharp
   // Add SiegeSlotData component to target entity
   var siegeSlotData = SiegeSlotData.CreateDefault(8, new dfloat(2.5f));
   entityManager.AddComponentData(targetEntity, siegeSlotData);
   ```

### Creating Siege Slot Configurations

1. Create a new SiegeSlotConfiguration asset:
   - Right-click in Project → Create → Avalon → Siege Slots → Configuration
   - Configure slot count, radius, queue settings, etc.
   - Assign to SiegeSlotAuthoring components

### Debug Visualization

Enable debug visualization in the SiegeSlotDebugSettings:
- **Show Slot Positions**: Visualize siege slot locations
- **Show Enemy Assignments**: Show lines from enemies to their targets
- **Show Statistics**: Display runtime statistics

## Integration with Existing Systems

The siege slot system integrates with:
- **Flow Field System**: Uses existing pathfinding for movement
- **Enemy System**: Works with existing enemy spawning and stats
- **Movement System**: Extends existing movement with slot positioning
- **Combat System**: Compatible with existing targeting and combat

## Performance Considerations

- Slot reservations and assignments are processed per-target, scaling well with distributed enemies
- Trampling checks only occur when all slots are occupied or reserved
- Balanced trampling distribution prevents excessive computation
- All calculations use deterministic fixed-point math for consistent results
- Constant speed movement eliminates complex acceleration calculations

## Testing

Run the unit tests in `SiegeSlotSystemTests.cs` to verify:
- Slot position calculations
- Occupancy and reservation tracking
- Enemy ranking and trampling logic
- Reservation and assignment logic
- Constant speed movement behavior

## Configuration Examples

### Basic Tower Defense Target
```csharp
slotCount = 8
slotRadius = 2.0f
slotReservationDistance = 6.0f
slotAssignmentDistance = 3.0f
allowTrampling = true
trampleThreshold = 0.1f
```

### Large Boss Enemy
```csharp
slotCount = 16
slotRadius = 4.0f
slotReservationDistance = 10.0f
slotAssignmentDistance = 5.0f
allowTrampling = true
trampleThreshold = 0.2f
```

### Fast-Moving Target
```csharp
slotCount = 6
slotRadius = 1.5f
slotReservationDistance = 8.0f
slotAssignmentDistance = 2.0f
allowTrampling = false
```

### Narrow Chokepoint
```csharp
slotCount = 4
slotRadius = 1.5f
queueFormation = DoubleLine
queueSpacing = 0.8f
```

## Future Enhancements

Potential improvements to consider:
- Dynamic slot count based on target size
- Formation-specific movement patterns
- Slot reservation system for incoming enemies
- Integration with special abilities and effects
- Performance optimizations for large-scale battles
