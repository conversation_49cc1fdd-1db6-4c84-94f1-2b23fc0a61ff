using Unity.Deterministic.Mathematics;
using FlowField;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// Utility class for calculating enemy rankings used in siege slot trampling logic.
    /// Higher rank values indicate higher priority enemies that can displace lower priority ones.
    /// </summary>
    public static class EnemyRanking
    {
        /// <summary>
        /// Weights for different stats in rank calculation
        /// These values determine the relative importance of each stat
        /// </summary>
        public struct RankingWeights
        {
            public dfloat healthWeight;
            public dfloat damageWeight;
            public dfloat speedWeight;
            public dfloat massWeight;
            
            public static RankingWeights Default => new RankingWeights
            {
                healthWeight = new dfloat(1.0f),
                damageWeight = new dfloat(1.5f), // Damage is slightly more important
                speedWeight = new dfloat(0.5f),  // Speed is less important for trampling
                massWeight = new dfloat(0.8f)    // Mass contributes to trampling ability
            };
        }
        
        /// <summary>
        /// Calculate the rank of an enemy based on their stats.
        /// This is used to determine trampling priority in siege slots.
        /// </summary>
        /// <param name="stats">The enemy's unit stats</param>
        /// <param name="weights">Weights for different stats (optional, uses default if null)</param>
        /// <returns>A rank value where higher values indicate higher priority</returns>
        public static dfloat CalculateEnemyRank(UnitStats stats, RankingWeights? weights = null)
        {
            var w = weights ?? RankingWeights.Default;
            
            // Normalize stats to prevent any single stat from dominating
            // We use reasonable max values for normalization
            dfloat maxHealth = new dfloat(1000.0f);
            dfloat maxDamage = new dfloat(100.0f);
            dfloat maxSpeed = new dfloat(20.0f);
            dfloat maxMass = new dfloat(10.0f);
            
            // Normalize each stat to 0-1 range
            dfloat normalizedHealth = dmath.min(stats.maxHealth / maxHealth, dfloat.One);
            dfloat normalizedDamage = dmath.min(stats.attackDamage / maxDamage, dfloat.One);
            dfloat normalizedSpeed = dmath.min(stats.maxSpeed / maxSpeed, dfloat.One);
            dfloat normalizedMass = dmath.min(stats.mass / maxMass, dfloat.One);
            
            // Calculate weighted rank
            dfloat rank = normalizedHealth * w.healthWeight +
                         normalizedDamage * w.damageWeight +
                         normalizedSpeed * w.speedWeight +
                         normalizedMass * w.massWeight;
            
            // Ensure rank is always positive
            return dmath.max(rank, new dfloat(0.01f));
        }
        
        /// <summary>
        /// Calculate a simple rank based on total combat power (health + damage)
        /// This is a simpler alternative to the full stat-based ranking
        /// </summary>
        public static dfloat CalculateSimpleRank(UnitStats stats)
        {
            return stats.maxHealth + stats.attackDamage * new dfloat(2.0f);
        }
        
        /// <summary>
        /// Compare two enemies to determine which has higher rank
        /// </summary>
        /// <param name="stats1">First enemy's stats</param>
        /// <param name="stats2">Second enemy's stats</param>
        /// <param name="weights">Ranking weights (optional)</param>
        /// <returns>Positive if enemy1 has higher rank, negative if enemy2 has higher rank, zero if equal</returns>
        public static dfloat CompareEnemyRanks(UnitStats stats1, UnitStats stats2, RankingWeights? weights = null)
        {
            dfloat rank1 = CalculateEnemyRank(stats1, weights);
            dfloat rank2 = CalculateEnemyRank(stats2, weights);
            return rank1 - rank2;
        }
        
        /// <summary>
        /// Determine if enemy1 can trample enemy2 based on their ranks
        /// </summary>
        /// <param name="stats1">Trampling enemy's stats</param>
        /// <param name="stats2">Enemy being trampled's stats</param>
        /// <param name="trampleThreshold">Minimum rank difference required for trampling</param>
        /// <param name="weights">Ranking weights (optional)</param>
        /// <returns>True if enemy1 can trample enemy2</returns>
        public static bool CanTrampel(UnitStats stats1, UnitStats stats2, dfloat? trampleThreshold = null, RankingWeights? weights = null)
        {
            dfloat threshold = trampleThreshold ?? new dfloat(0.1f); // Default 10% rank difference required
            dfloat rankDifference = CompareEnemyRanks(stats1, stats2, weights);
            return rankDifference > threshold;
        }
        
        /// <summary>
        /// Calculate rank with time-based tiebreaker for deterministic ordering
        /// Earlier arrivals get slight priority boost to ensure consistent behavior
        /// </summary>
        public static dfloat CalculateRankWithTimeBreaker(UnitStats stats, dfloat arrivalTime, RankingWeights? weights = null)
        {
            dfloat baseRank = CalculateEnemyRank(stats, weights);
            
            // Add small time-based component (earlier arrival = higher priority)
            // The time component is scaled to be much smaller than stat differences
            dfloat timeBonus = new dfloat(1.0f) / (arrivalTime + dfloat.One) * new dfloat(0.01f);
            
            return baseRank + timeBonus;
        }
        
        /// <summary>
        /// Get a rank category for debugging/visualization purposes
        /// </summary>
        public static string GetRankCategory(dfloat rank)
        {
            if (rank < new dfloat(0.5f)) return "Weak";
            if (rank < new dfloat(1.0f)) return "Normal";
            if (rank < new dfloat(1.5f)) return "Strong";
            if (rank < new dfloat(2.0f)) return "Elite";
            return "Boss";
        }
    }
}
