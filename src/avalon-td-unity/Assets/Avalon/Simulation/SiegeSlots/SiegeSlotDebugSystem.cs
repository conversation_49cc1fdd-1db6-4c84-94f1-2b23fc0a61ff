using FlowField;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// Debug system for visualizing siege slots and enemy assignments.
    /// This system provides runtime debugging and visualization tools for the siege slot system.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct SiegeSlotDebugSystem : ISystem
    {
        private EntityQuery targetsQuery;
        private EntityQuery enemiesQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            targetsQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<SiegeSlotData>(),
                ComponentType.ReadOnly<SimulationTransform>()
            );
            
            enemiesQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<SiegeSlotAssignment>(),
                ComponentType.ReadOnly<SimulationTransform>()
            );
        }
        
        public void OnUpdate(ref SystemState state)
        {
            // Only run debug visualization in development builds
            #if UNITY_EDITOR || DEVELOPMENT_BUILD

            var settings = SiegeSlotDebugSettings.Instance;
            if (!settings.enableDebugVisualization)
                return;

            // Early exit if no debug features are enabled
            if (!settings.showSlotPositions && !settings.showEnemyAssignments && !settings.showStatistics)
                return;

            // Visualize siege slot targets
            if (settings.showSlotPositions)
            {
                VisualizeSiegeSlots(ref state);
            }

            // Visualize enemy assignments
            if (settings.showEnemyAssignments)
            {
                VisualizeEnemyAssignments(ref state);
            }

            // Show debug statistics
            if (settings.showStatistics)
            {
                ShowDebugStatistics(ref state);
            }

            #endif
        }
        
        #if UNITY_EDITOR || DEVELOPMENT_BUILD
        
        private void VisualizeSiegeSlots(ref SystemState state)
        {
            var settings = SiegeSlotDebugSettings.Instance;

            foreach (var (siegeSlotData, transform, entity) in
                SystemAPI.Query<RefRO<SiegeSlotData>, RefRO<SimulationTransform>>().WithEntityAccess())
            {
                if (!siegeSlotData.ValueRO.isActive)
                    continue;

                var targetPos = transform.ValueRO.position;
                var slotData = siegeSlotData.ValueRO;

                // Draw siege slot positions
                for (int i = 0; i < slotData.slotCount; i++)
                {
                    var slotPos = slotData.GetSlotPosition(i, targetPos);

                    // Choose color based on slot status - prioritize occupied over reserved
                    Color color;
                    if (slotData.IsSlotOccupied(i))
                    {
                        color = settings.occupiedSlotColor;
                    }
                    else if (slotData.IsSlotReserved(i))
                    {
                        color = settings.queuedEnemyColor; // Use queued color for reserved slots
                    }
                    else
                    {
                        color = settings.freeSlotColor;
                    }

                    DebugDrawUtility.DrawWireSphere(
                        new Vector3((float)slotPos.x, (float)slotPos.y, (float)slotPos.z),
                        0.3f, color, 0.1f);

                    // Draw line from center to slot
                    DebugDrawUtility.DrawLine(
                        new Vector3((float)targetPos.x, (float)targetPos.y, (float)targetPos.z),
                        new Vector3((float)slotPos.x, (float)slotPos.y, (float)slotPos.z),
                        Color.yellow, 0.1f);
                }

                // Draw slot radius circle
                DebugDrawUtility.DrawWireCircle(
                    new Vector3((float)targetPos.x, (float)targetPos.y, (float)targetPos.z),
                    (float)slotData.slotRadius, Color.cyan, 0.1f);
            }
        }
        
        private void VisualizeEnemyAssignments(ref SystemState state)
        {
            var settings = SiegeSlotDebugSettings.Instance;

            foreach (var (assignment, transform, entity) in
                SystemAPI.Query<RefRO<SiegeSlotAssignment>, RefRO<SimulationTransform>>().WithEntityAccess())
            {
                if (!assignment.ValueRO.IsValid())
                    continue;

                var enemyPos = transform.ValueRO.position;
                var targetPos = assignment.ValueRO.SlotPosition;
                var status = assignment.ValueRO.status;

                // Choose color based on status using configurable colors
                Color lineColor = status switch
                {
                    SiegeSlotStatus.SlotReserved => settings.queuedEnemyColor,
                    SiegeSlotStatus.MovingToSlot => settings.movingEnemyColor,
                    SiegeSlotStatus.OccupyingSlot => settings.occupiedSlotColor,
                    _ => Color.gray
                };

                // Only draw line if we have a valid slot position (not zero)
                if (targetPos.x != dfloat.Zero || targetPos.y != dfloat.Zero || targetPos.z != dfloat.Zero)
                {
                    // Draw line from enemy to their target position
                    DebugDrawUtility.DrawLine(
                        new Vector3((float)enemyPos.x, (float)enemyPos.y, (float)enemyPos.z),
                        new Vector3((float)targetPos.x, (float)targetPos.y, (float)targetPos.z),
                        lineColor, 0.1f);
                }

                // Draw enemy position with status indicator
                DebugDrawUtility.DrawWireSphere(
                    new Vector3((float)enemyPos.x, (float)enemyPos.y + 0.5f, (float)enemyPos.z),
                    0.2f, lineColor, 0.1f);

                // Draw rank indicator (height based on rank)
                float rankHeight = (float)assignment.ValueRO.enemyRank * 0.5f;
                DebugDrawUtility.DrawLine(
                    new Vector3((float)enemyPos.x, (float)enemyPos.y, (float)enemyPos.z),
                    new Vector3((float)enemyPos.x, (float)enemyPos.y + rankHeight, (float)enemyPos.z),
                    Color.white, 0.1f);
            }
        }
        
        private void ShowDebugStatistics(ref SystemState state)
        {
            int totalTargets = targetsQuery.CalculateEntityCount();
            int totalEnemies = enemiesQuery.CalculateEntityCount();

            int occupiedSlots = 0;
            int reservedSlots = 0;
            int movingEnemies = 0;
            int totalSlots = 0;

            // Count slot statistics from targets
            foreach (var siegeSlotData in SystemAPI.Query<RefRO<SiegeSlotData>>())
            {
                var slotData = siegeSlotData.ValueRO;
                if (!slotData.isActive) continue;

                totalSlots += slotData.slotCount;
                occupiedSlots += slotData.GetOccupiedSlotCount();
                reservedSlots += slotData.GetReservedSlotCount();
            }

            // Count enemy assignment statistics
            foreach (var assignment in SystemAPI.Query<RefRO<SiegeSlotAssignment>>())
            {
                switch (assignment.ValueRO.status)
                {
                    case SiegeSlotStatus.MovingToSlot:
                        movingEnemies++;
                        break;
                }
            }

            int availableSlots = totalSlots - occupiedSlots - reservedSlots;

            // Display statistics in the console (in editor) or on screen (in builds)
            string stats = $"Siege Slot Statistics:\n" +
                          $"Targets: {totalTargets}\n" +
                          $"Total Slots: {totalSlots}\n" +
                          $"Occupied Slots: {occupiedSlots}\n" +
                          $"Reserved Slots: {reservedSlots}\n" +
                          $"Available Slots: {availableSlots}\n" +
                          $"Total Enemies: {totalEnemies}\n" +
                          $"Moving Enemies: {movingEnemies}";

            #if UNITY_EDITOR
            if (Time.frameCount % 60 == 0) // Update every second
            {
                Debug.Log(stats);
            }
            #endif
        }
        
        #endif
    }
    
    /// <summary>
    /// Settings for siege slot debug visualization
    /// </summary>
    public class SiegeSlotDebugSettings : ScriptableObject
    {
        private static SiegeSlotDebugSettings _instance;
        public static SiegeSlotDebugSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = CreateInstance<SiegeSlotDebugSettings>();
                }
                return _instance;
            }
        }
        
        [Header("Visualization Settings")]
        public bool enableDebugVisualization = true;
        public bool showSlotPositions = true;
        public bool showEnemyAssignments = true;
        public bool showQueuePositions = true;
        public bool showStatistics = false;
        
        [Header("Colors")]
        public Color occupiedSlotColor = Color.red;
        public Color freeSlotColor = Color.green;
        public Color queuedEnemyColor = Color.orange;
        public Color movingEnemyColor = Color.blue;
    }
    
    /// <summary>
    /// Utility class for debug drawing (works in both editor and runtime)
    /// </summary>
    public static class DebugDrawUtility
    {
        public static void DrawLine(Vector3 start, Vector3 end, Color color, float duration = 0f)
        {
            #if UNITY_EDITOR
            if (Application.isPlaying)
            {
                Debug.DrawLine(start, end, color, duration);
            }
            else
            {
                UnityEditor.Handles.color = color;
                UnityEditor.Handles.DrawLine(start, end);
            }
            #else
            Debug.DrawLine(start, end, color, duration);
            #endif
        }
        
        public static void DrawWireSphere(Vector3 center, float radius, Color color, float duration = 0f)
        {
            #if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                UnityEditor.Handles.color = color;
                UnityEditor.Handles.DrawWireDisc(center, Vector3.up, radius);
                return;
            }
            #endif
            
            // Draw wire sphere using lines (simplified version for runtime)
            int segments = 16;
            float angleStep = 360f / segments;
            
            for (int i = 0; i < segments; i++)
            {
                float angle1 = i * angleStep * Mathf.Deg2Rad;
                float angle2 = (i + 1) * angleStep * Mathf.Deg2Rad;
                
                Vector3 point1 = center + new Vector3(Mathf.Cos(angle1) * radius, 0, Mathf.Sin(angle1) * radius);
                Vector3 point2 = center + new Vector3(Mathf.Cos(angle2) * radius, 0, Mathf.Sin(angle2) * radius);
                
                DrawLine(point1, point2, color, duration);
            }
        }
        
        public static void DrawWireCircle(Vector3 center, float radius, Color color, float duration = 0f)
        {
            DrawWireSphere(center, radius, color, duration);
        }
    }
}
