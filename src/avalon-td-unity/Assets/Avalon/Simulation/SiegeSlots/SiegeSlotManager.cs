using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Deterministic.Mathematics;
using FlowField;
using Avalon.Simulation.Movement;
using Avalon.Simulation;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// System that manages siege slot assignments and trampling logic.
    /// Enemies only get assigned to slots when they are close to the target.
    /// Higher-ranked enemies can trample lower-ranked enemies to take their slots.
    /// No queuing system - enemies without slots continue normal movement.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(UnifiedMovementSystem))]
    public partial struct SiegeSlotManager : ISystem
    {
        private EntityQuery enemiesQuery;
        private EntityQuery targetsQuery;
        private EntityQuery fixedTimestepQuery;
        private ComponentLookup<SiegeSlotData> siegeSlotLookup;
        private ComponentLookup<SiegeSlotSettings> siegeSlotSettingsLookup;
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<UnitStats> unitStatsLookup;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for enemies that have siege slot assignments
            enemiesQuery = state.GetEntityQuery(
                ComponentType.ReadWrite<SiegeSlotAssignment>(),
                ComponentType.ReadOnly<SimulationTransform>(),
                ComponentType.ReadOnly<UnitStats>(),
                ComponentType.ReadWrite<MovementTarget>()
            );

            // Query for targets that have siege slots
            targetsQuery = state.GetEntityQuery(
                ComponentType.ReadWrite<SiegeSlotData>(),
                ComponentType.ReadOnly<SimulationTransform>()
            );

            // Query for fixed timestep singleton
            fixedTimestepQuery = state.GetEntityQuery(ComponentType.ReadOnly<FixedTimestep>());

            siegeSlotLookup = state.GetComponentLookup<SiegeSlotData>();
            siegeSlotSettingsLookup = state.GetComponentLookup<SiegeSlotSettings>(true);
            transformLookup = state.GetComponentLookup<SimulationTransform>(true);
            unitStatsLookup = state.GetComponentLookup<UnitStats>(true);

            // Require fixed timestep to exist
            state.RequireForUpdate<FixedTimestep>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Only process on fixed timestep ticks
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            if (!fixedTimestep.shouldProcessTick) return;

            // Update component lookups
            siegeSlotLookup.Update(ref state);
            siegeSlotSettingsLookup.Update(ref state);
            transformLookup.Update(ref state);
            unitStatsLookup.Update(ref state);

            dfloat currentTime = fixedTimestep.currentTime;
            var ecb = new EntityCommandBuffer(Allocator.TempJob);

            // Process each target with siege slots
            var targetEntities = targetsQuery.ToEntityArray(Allocator.TempJob);
            var targetTransforms = targetsQuery.ToComponentDataArray<SimulationTransform>(Allocator.TempJob);
            var targetSiegeSlots = targetsQuery.ToComponentDataArray<SiegeSlotData>(Allocator.TempJob);

            for (int targetIndex = 0; targetIndex < targetEntities.Length; targetIndex++)
            {
                var targetEntity = targetEntities[targetIndex];
                var targetTransform = targetTransforms[targetIndex];
                var siegeSlotData = targetSiegeSlots[targetIndex];

                if (!siegeSlotData.isActive)
                    continue;

                // Process all enemies targeting this siege slot entity
                ProcessEnemiesForTarget(ref state, targetEntity, targetTransform.position, ref siegeSlotData, currentTime, ecb);

                // Update the siege slot data back to the entity
                state.EntityManager.SetComponentData(targetEntity, siegeSlotData);
            }

            // Execute entity commands and dispose
            ecb.Playback(state.EntityManager);
            ecb.Dispose();

            targetEntities.Dispose();
            targetTransforms.Dispose();
            targetSiegeSlots.Dispose();
        }
        
        private void ProcessEnemiesForTarget(ref SystemState state, Entity targetEntity, dfloat3 targetPosition,
            ref SiegeSlotData siegeSlotData, dfloat currentTime, EntityCommandBuffer ecb)
        {
            // Get settings for this target, or use defaults if not found
            var settings = siegeSlotSettingsLookup.HasComponent(targetEntity)
                ? siegeSlotSettingsLookup[targetEntity]
                : SiegeSlotSettings.CreateDefault();

            // Get all enemies targeting this specific entity
            var enemyEntities = new NativeList<Entity>(Allocator.Temp);
            var enemyAssignments = new NativeList<SiegeSlotAssignment>(Allocator.Temp);
            var enemyTransforms = new NativeList<SimulationTransform>(Allocator.Temp);
            var enemyStats = new NativeList<UnitStats>(Allocator.Temp);

            // Collect enemies targeting this entity
            foreach (var (assignment, transform, stats, entity) in
                SystemAPI.Query<RefRW<SiegeSlotAssignment>, RefRO<SimulationTransform>, RefRO<UnitStats>>()
                .WithEntityAccess())
            {
                if (assignment.ValueRO.targetEntity == targetEntity)
                {
                    enemyEntities.Add(entity);
                    enemyAssignments.Add(assignment.ValueRO);
                    enemyTransforms.Add(transform.ValueRO);
                    enemyStats.Add(stats.ValueRO);
                }
            }

            if (enemyEntities.Length == 0)
            {
                enemyEntities.Dispose();
                enemyAssignments.Dispose();
                enemyTransforms.Dispose();
                enemyStats.Dispose();
                return;
            }

            // Sort enemies by rank (highest rank first) for deterministic processing
            SortEnemiesByRank(enemyEntities, enemyAssignments, enemyTransforms, enemyStats);

            // Process slot reservations for enemies at reservation distance
            ProcessSlotReservations(ref siegeSlotData, targetPosition, enemyEntities, enemyAssignments,
                enemyTransforms, enemyStats, currentTime, settings);

            // Process trampling - higher ranked enemies can kill lower ranked ones for their slots
            ProcessTrampling(ref siegeSlotData, targetPosition, enemyEntities, enemyAssignments,
                enemyTransforms, enemyStats, currentTime, ecb, settings);

            // Assign slots to eligible enemies (those close enough)
            AssignSlotsToEligibleEnemies(ref siegeSlotData, targetPosition, enemyEntities, enemyAssignments,
                enemyTransforms, enemyStats, currentTime, settings);

            // Apply assignments back to entities
            for (int i = 0; i < enemyEntities.Length; i++)
            {
                var entity = enemyEntities[i];
                var assignment = enemyAssignments[i];

                state.EntityManager.SetComponentData(entity, assignment);
            }

            enemyEntities.Dispose();
            enemyAssignments.Dispose();
            enemyTransforms.Dispose();
            enemyStats.Dispose();
        }
        
        private void SortEnemiesByRank(NativeList<Entity> entities, NativeList<SiegeSlotAssignment> assignments,
            NativeList<SimulationTransform> transforms, NativeList<UnitStats> stats)
        {
            // Simple bubble sort for deterministic ordering (fine for small numbers of enemies per target)
            for (int i = 0; i < entities.Length - 1; i++)
            {
                for (int j = 0; j < entities.Length - i - 1; j++)
                {
                    dfloat rank1 = EnemyRanking.CalculateRankWithTimeBreaker(stats[j], assignments[j].assignmentTime);
                    dfloat rank2 = EnemyRanking.CalculateRankWithTimeBreaker(stats[j + 1], assignments[j + 1].assignmentTime);
                    
                    if (rank1 < rank2) // Higher rank should come first
                    {
                        // Swap all arrays
                        (entities[j], entities[j + 1]) = (entities[j + 1], entities[j]);
                        (assignments[j], assignments[j + 1]) = (assignments[j + 1], assignments[j]);
                        (transforms[j], transforms[j + 1]) = (transforms[j + 1], transforms[j]);
                        (stats[j], stats[j + 1]) = (stats[j + 1], stats[j]);
                    }
                }
            }
        }
        
        private void ProcessTrampling(ref SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            NativeList<Entity> entities, NativeList<SiegeSlotAssignment> assignments,
            NativeList<SimulationTransform> transforms, NativeList<UnitStats> stats, dfloat currentTime, EntityCommandBuffer ecb, SiegeSlotSettings settings)
        {
            // Only allow trampling when all slots are occupied or reserved and trampling is enabled
            if (!settings.allowTrampling || !siegeSlotData.AreAllSlotsOccupiedOrReserved())
                return;

            // Check for trampling opportunities - higher ranked enemies can kill lower ranked ones
            for (int i = 0; i < entities.Length; i++)
            {
                var assignment = assignments[i];
                var transform = transforms[i];

                // Skip if this enemy already has a slot assignment
                if (assignment.HasAnySlotAssignment())
                    continue;

                // Only enemies close enough to the target OR close to any occupied slot can attempt trampling
                if (!CanAttemptTrampling(transform.position, targetPosition, ref siegeSlotData, entities, assignments, settings))
                    continue;

                // Find the best slot to trample using balanced distribution
                var trampleResult = FindBestSlotToTrampel(i, entities, assignments, transforms, stats,
                    ref siegeSlotData, targetPosition);
                int bestSlotToTrampel = trampleResult.slotIndex;
                int bestEnemyToTrampel = trampleResult.enemyIndex;

                // If we found a slot to trample, do it
                if (bestSlotToTrampel >= 0 && bestEnemyToTrampel >= 0)
                {
                    var trampledAssignment = assignments[bestEnemyToTrampel];
                    bool wasOccupied = trampledAssignment.IsOccupyingSlot();
                    bool wasReserved = trampledAssignment.HasSlotReserved();

                    trampledAssignment.Clear();

                    // Add Destroyed component to the trampled unit
                    ecb.AddComponent<Destroyed>(entities[bestEnemyToTrampel]);

                    // Free the appropriate slot state
                    if (wasOccupied)
                    {
                        siegeSlotData.FreeSlot(bestSlotToTrampel);
                    }
                    else if (wasReserved)
                    {
                        siegeSlotData.UnreserveSlot(bestSlotToTrampel);
                    }

                    // Assign the slot to the trampling enemy based on their distance
                    dfloat distanceToTarget = dmath.distance(transform.position, targetPosition);
                    if (distanceToTarget <= settings.slotAssignmentDistance)
                    {
                        // Close enough to move to slot immediately
                        assignment.AssignToSlot(bestSlotToTrampel, targetPosition,
                            siegeSlotData.GetSlotPosition(bestSlotToTrampel, targetPosition), currentTime);
                        siegeSlotData.OccupySlot(bestSlotToTrampel);
                    }
                    else
                    {
                        // Reserve the slot for later assignment
                        assignment.ReserveSlot(bestSlotToTrampel, targetPosition, currentTime);
                        assignment.enemyRank = EnemyRanking.CalculateEnemyRank(stats[i]);
                        siegeSlotData.ReserveSlot(bestSlotToTrampel);
                    }

                    assignments[i] = assignment;
                    assignments[bestEnemyToTrampel] = trampledAssignment;
                    break; // Only trample one enemy per update
                }
            }
        }

        private void AssignSlotsToEligibleEnemies(ref SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            NativeList<Entity> entities, NativeList<SiegeSlotAssignment> assignments,
            NativeList<SimulationTransform> transforms, NativeList<UnitStats> stats, dfloat currentTime, SiegeSlotSettings settings)
        {
            // Process enemies that are close enough to be assigned to move to their slots
            for (int i = 0; i < entities.Length; i++)
            {
                var assignment = assignments[i];
                var transform = transforms[i];

                dfloat distanceToTarget;

                // Check if enemy has a reserved slot and is now close enough to be assigned to move
                if (assignment.status == SiegeSlotStatus.SlotReserved)
                {
                    distanceToTarget = dmath.distance(transform.position, targetPosition);
                    if (distanceToTarget <= settings.slotAssignmentDistance)
                    {
                        // Convert reservation to movement assignment
                        dfloat3 slotPosition = siegeSlotData.GetSlotPosition(assignment.assignedSlotIndex, targetPosition);
                        assignment.AssignToSlot(assignment.assignedSlotIndex, targetPosition, slotPosition, currentTime);

                        // Move slot from reserved to occupied
                        siegeSlotData.UnreserveSlot(assignment.assignedSlotIndex);
                        siegeSlotData.OccupySlot(assignment.assignedSlotIndex);
                        assignments[i] = assignment;
                    }
                    continue;
                }

                // Skip if this enemy already has a slot assignment (moving or occupying)
                if (assignment.status != SiegeSlotStatus.None)
                    continue;

                // Only assign slots to enemies that are close enough to the target
                distanceToTarget = dmath.distance(transform.position, targetPosition);
                if (distanceToTarget > settings.slotAssignmentDistance)
                    continue;

                // Try to find the closest available slot to this unit
                int availableSlot = siegeSlotData.FindClosestAvailableSlot(transform.position, targetPosition);
                if (availableSlot >= 0)
                {
                    // Assign this enemy to the closest available slot
                    dfloat3 slotPosition = siegeSlotData.GetSlotPosition(availableSlot, targetPosition);
                    assignment.AssignToSlot(availableSlot, targetPosition, slotPosition, currentTime);
                    assignment.enemyRank = EnemyRanking.CalculateEnemyRank(stats[i]);

                    siegeSlotData.OccupySlot(availableSlot);
                    assignments[i] = assignment;
                }
                // If no slots available, enemy continues with normal movement (no queuing)
            }
        }

        /// <summary>
        /// Check if a unit can attempt trampling based on its distance to the target center or any occupied slot
        /// </summary>
        private bool CanAttemptTrampling(dfloat3 unitPosition, dfloat3 targetPosition, ref SiegeSlotData siegeSlotData,
            NativeList<Entity> entities, NativeList<SiegeSlotAssignment> assignments, SiegeSlotSettings settings)
        {
            // First check if close enough to target center
            dfloat distanceToTarget = dmath.distance(unitPosition, targetPosition);
            if (distanceToTarget <= settings.slotAssignmentDistance)
                return true;

            // If not close to target center, check if close to any occupied slot
            for (int i = 0; i < entities.Length; i++)
            {
                var assignment = assignments[i];
                if (assignment.IsOccupyingSlot())
                {
                    dfloat3 slotPosition = siegeSlotData.GetSlotPosition(assignment.assignedSlotIndex, targetPosition);
                    dfloat distanceToSlot = dmath.distance(unitPosition, slotPosition);

                    if (distanceToSlot <= settings.slotAssignmentDistance)
                        return true;
                }
            }

            return false;
        }

        private struct TrampleResult
        {
            public int slotIndex;
            public int enemyIndex;

            public TrampleResult(int slot, int enemy)
            {
                slotIndex = slot;
                enemyIndex = enemy;
            }
        }

        private TrampleResult FindBestSlotToTrampel(int tramplerIndex, NativeList<Entity> entities,
            NativeList<SiegeSlotAssignment> assignments, NativeList<SimulationTransform> transforms,
            NativeList<UnitStats> stats, ref SiegeSlotData siegeSlotData, dfloat3 targetPosition)
        {
            var tramplerTransform = transforms[tramplerIndex];
            var tramplerStats = stats[tramplerIndex];

            // Collect all potential trampling targets
            var candidates = new NativeList<TrampleCandidate>(Allocator.Temp);

            for (int j = 0; j < entities.Length; j++)
            {
                if (tramplerIndex == j) continue;

                var targetAssignment = assignments[j];
                // Consider both occupied and reserved slots for trampling
                if (!targetAssignment.IsOccupyingSlot() && !targetAssignment.HasSlotReserved())
                    continue;

                // Check if this enemy can trample the target
                if (EnemyRanking.CanTrampel(tramplerStats, stats[j]))
                {
                    dfloat3 slotPosition = siegeSlotData.GetSlotPosition(targetAssignment.assignedSlotIndex, targetPosition);
                    dfloat distance = dmath.distance(tramplerTransform.position, slotPosition);

                    candidates.Add(new TrampleCandidate
                    {
                        enemyIndex = j,
                        slotIndex = targetAssignment.assignedSlotIndex,
                        distance = distance,
                        enemyRank = assignments[j].enemyRank
                    });
                }
            }

            var result = SelectBestTrampleTarget(candidates, ref siegeSlotData);
            candidates.Dispose();

            return result;
        }

        private struct TrampleCandidate
        {
            public int enemyIndex;
            public int slotIndex;
            public dfloat distance;
            public dfloat enemyRank;
        }

        private TrampleResult SelectBestTrampleTarget(NativeList<TrampleCandidate> candidates, ref SiegeSlotData siegeSlotData)
        {
            if (candidates.Length == 0)
                return new TrampleResult(-1, -1);

            // Sort candidates by multiple criteria for deterministic and balanced selection:
            // 1. Prefer slots with lower rank enemies (easier to trample)
            // 2. Among equal ranks, prefer closer distances
            // 3. Use slot index as final tie-breaker for determinism
            SortTrampleCandidates(candidates);

            // For even distribution, we could implement a round-robin approach here
            // For now, select the best candidate (lowest rank, closest distance)
            var best = candidates[0];
            return new TrampleResult(best.slotIndex, best.enemyIndex);
        }

        private void SortTrampleCandidates(NativeList<TrampleCandidate> candidates)
        {
            // Simple bubble sort for deterministic ordering (fine for small numbers)
            for (int i = 0; i < candidates.Length - 1; i++)
            {
                for (int j = 0; j < candidates.Length - 1 - i; j++)
                {
                    var a = candidates[j];
                    var b = candidates[j + 1];

                    // Primary: Lower enemy rank (easier to trample)
                    if (a.enemyRank > b.enemyRank)
                    {
                        candidates[j] = b;
                        candidates[j + 1] = a;
                    }
                    else if (dmath.abs(a.enemyRank - b.enemyRank) < new dfloat(0.001f)) // Equal ranks
                    {
                        // Secondary: Closer distance
                        if (a.distance > b.distance)
                        {
                            candidates[j] = b;
                            candidates[j + 1] = a;
                        }
                        else if (dmath.abs(a.distance - b.distance) < new dfloat(0.001f)) // Equal distances
                        {
                            // Tertiary: Lower slot index for determinism
                            if (a.slotIndex > b.slotIndex)
                            {
                                candidates[j] = b;
                                candidates[j + 1] = a;
                            }
                        }
                    }
                }
            }
        }

        private void ProcessSlotReservations(ref SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            NativeList<Entity> entities, NativeList<SiegeSlotAssignment> assignments,
            NativeList<SimulationTransform> transforms, NativeList<UnitStats> stats, dfloat currentTime, SiegeSlotSettings settings)
        {
            // Process enemies that are within reservation distance but don't have reservations yet
            for (int i = 0; i < entities.Length; i++)
            {
                var assignment = assignments[i];
                var transform = transforms[i];

                // Skip if enemy already has any slot assignment
                if (assignment.status != SiegeSlotStatus.None)
                    continue;

                // Check if enemy is within reservation distance
                dfloat distanceToTarget = dmath.distance(transform.position, targetPosition);
                if (distanceToTarget > settings.slotReservationDistance)
                    continue;

                // Try to reserve the closest available slot
                int availableSlot = siegeSlotData.FindClosestSlotForReservation(transform.position, targetPosition);
                if (availableSlot >= 0)
                {
                    // Reserve the slot
                    assignment.ReserveSlot(availableSlot, targetPosition, currentTime);
                    assignment.enemyRank = EnemyRanking.CalculateEnemyRank(stats[i]);

                    siegeSlotData.ReserveSlot(availableSlot);
                    assignments[i] = assignment;
                }
            }
        }


    }
}
