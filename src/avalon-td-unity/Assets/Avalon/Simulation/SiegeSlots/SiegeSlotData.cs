using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// Component that defines siege slots around a target entity.
    /// Siege slots are positions evenly distributed around the target where enemies can position themselves.
    /// </summary>
    public struct SiegeSlotData : IComponentData
    {
        /// <summary>
        /// Number of siege slots available around this target
        /// </summary>
        public int slotCount;
        
        /// <summary>
        /// Radius from the target center where siege slots are positioned
        /// </summary>
        public dfloat slotRadius;
        
        /// <summary>
        /// Angle offset for the first slot (in radians)
        /// Used to rotate the entire slot arrangement
        /// </summary>
        public dfloat angleOffset;
        
        /// <summary>
        /// Whether siege slots are currently active for this target
        /// </summary>
        public bool isActive;
        
        /// <summary>
        /// Bitmask representing which slots are currently occupied
        /// Bit i is set if slot i is occupied
        /// </summary>
        public long occupiedSlotsMask;

        /// <summary>
        /// Bitmask representing which slots are currently reserved
        /// Bit i is set if slot i is reserved by an enemy approaching
        /// </summary>
        public long reservedSlotsMask;
        
        /// <summary>
        /// Create default siege slot data
        /// </summary>
        public static SiegeSlotData CreateDefault(int slots = 8, dfloat? radius = null)
        {
            return new SiegeSlotData
            {
                slotCount = slots,
                slotRadius = radius ?? dfloat.Two,
                angleOffset = dfloat.Zero,
                isActive = true,
                occupiedSlotsMask = 0L,
                reservedSlotsMask = 0L
            };
        }
        
        /// <summary>
        /// Calculate the world position of a specific siege slot
        /// </summary>
        public dfloat3 GetSlotPosition(int slotIndex, dfloat3 targetPosition)
        {
            if (slotIndex < 0 || slotIndex >= slotCount)
                return targetPosition;
                
            // Calculate angle for this slot (evenly distributed around the circle)
            dfloat angleStep = dmath.TwoPI / slotCount;
            dfloat slotAngle = angleOffset + angleStep * slotIndex;
            
            // Calculate position offset from target center
            dfloat x = dmath.cos(slotAngle) * slotRadius;
            dfloat z = dmath.sin(slotAngle) * slotRadius;
            
            return targetPosition + new dfloat3(x, dfloat.Zero, z);
        }
        
        /// <summary>
        /// Check if a specific slot is occupied
        /// </summary>
        public bool IsSlotOccupied(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= slotCount)
                return true; // Invalid slots are considered occupied
                
            return (occupiedSlotsMask & (1L << slotIndex)) != 0;
        }
        
        /// <summary>
        /// Mark a slot as occupied
        /// </summary>
        public void OccupySlot(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < slotCount)
            {
                occupiedSlotsMask |= (1L << slotIndex);
            }
        }
        
        /// <summary>
        /// Mark a slot as free
        /// </summary>
        public void FreeSlot(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < slotCount)
            {
                occupiedSlotsMask &= ~(1L << slotIndex);
            }
        }

        /// <summary>
        /// Check if a specific slot is reserved
        /// </summary>
        public bool IsSlotReserved(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= slotCount)
                return true; // Invalid slots are considered reserved

            return (reservedSlotsMask & (1L << slotIndex)) != 0;
        }

        /// <summary>
        /// Mark a slot as reserved
        /// </summary>
        public void ReserveSlot(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < slotCount)
            {
                reservedSlotsMask |= (1L << slotIndex);
            }
        }

        /// <summary>
        /// Mark a slot as unreserved
        /// </summary>
        public void UnreserveSlot(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < slotCount)
            {
                reservedSlotsMask &= ~(1L << slotIndex);
            }
        }

        /// <summary>
        /// Check if a slot is available for reservation (not occupied and not reserved)
        /// </summary>
        public bool IsSlotAvailableForReservation(int slotIndex)
        {
            return !IsSlotOccupied(slotIndex) && !IsSlotReserved(slotIndex);
        }
        
        /// <summary>
        /// Find the first available (unoccupied) slot
        /// </summary>
        public int FindFirstAvailableSlot()
        {
            for (int i = 0; i < slotCount; i++)
            {
                if (!IsSlotOccupied(i))
                    return i;
            }
            return -1; // No available slots
        }

        /// <summary>
        /// Find the first slot available for reservation (not occupied and not reserved)
        /// </summary>
        public int FindFirstSlotForReservation()
        {
            for (int i = 0; i < slotCount; i++)
            {
                if (IsSlotAvailableForReservation(i))
                    return i;
            }
            return -1; // No slots available for reservation
        }

        /// <summary>
        /// Find the closest available (unoccupied) slot to a given position
        /// </summary>
        public int FindClosestAvailableSlot(dfloat3 unitPosition, dfloat3 targetPosition)
        {
            int closestSlot = -1;
            dfloat closestDistance = dfloat.MAX_VALUE;

            for (int i = 0; i < slotCount; i++)
            {
                if (!IsSlotOccupied(i))
                {
                    dfloat3 slotPosition = GetSlotPosition(i, targetPosition);
                    dfloat distance = dmath.distance(unitPosition, slotPosition);

                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestSlot = i;
                    }
                }
            }

            return closestSlot; // Returns -1 if no available slots
        }

        /// <summary>
        /// Find the closest slot available for reservation (not occupied and not reserved) to a given position
        /// </summary>
        public int FindClosestSlotForReservation(dfloat3 unitPosition, dfloat3 targetPosition)
        {
            int closestSlot = -1;
            dfloat closestDistance = dfloat.MAX_VALUE;

            for (int i = 0; i < slotCount; i++)
            {
                if (IsSlotAvailableForReservation(i))
                {
                    dfloat3 slotPosition = GetSlotPosition(i, targetPosition);
                    dfloat distance = dmath.distance(unitPosition, slotPosition);

                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestSlot = i;
                    }
                }
            }

            return closestSlot; // Returns -1 if no slots available for reservation
        }

        /// <summary>
        /// Find the closest occupied slot to a given position
        /// </summary>
        public int FindClosestOccupiedSlot(dfloat3 unitPosition, dfloat3 targetPosition)
        {
            int closestSlot = -1;
            dfloat closestDistance = dfloat.MAX_VALUE;

            for (int i = 0; i < slotCount; i++)
            {
                if (IsSlotOccupied(i))
                {
                    dfloat3 slotPosition = GetSlotPosition(i, targetPosition);
                    dfloat distance = dmath.distance(unitPosition, slotPosition);

                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestSlot = i;
                    }
                }
            }

            return closestSlot; // Returns -1 if no occupied slots
        }
        
        /// <summary>
        /// Get the number of occupied slots
        /// </summary>
        public int GetOccupiedSlotCount()
        {
            int count = 0;
            long mask = occupiedSlotsMask;
            
            // Count set bits using Brian Kernighan's algorithm
            while (mask != 0)
            {
                mask &= mask - 1; // Clear the lowest set bit
                count++;
            }
            
            return count;
        }
        
        /// <summary>
        /// Check if all slots are occupied
        /// </summary>
        public bool AreAllSlotsOccupied()
        {
            return GetOccupiedSlotCount() >= slotCount;
        }
        
        /// <summary>
        /// Get all occupied slot indices
        /// </summary>
        public void GetOccupiedSlots(ref NativeList<int> occupiedSlots)
        {
            occupiedSlots.Clear();

            for (int i = 0; i < slotCount; i++)
            {
                if (IsSlotOccupied(i))
                {
                    occupiedSlots.Add(i);
                }
            }
        }

        /// <summary>
        /// Get the number of reserved slots
        /// </summary>
        public int GetReservedSlotCount()
        {
            int count = 0;
            long mask = reservedSlotsMask;

            // Count set bits using Brian Kernighan's algorithm
            while (mask != 0)
            {
                mask &= mask - 1; // Clear the lowest set bit
                count++;
            }

            return count;
        }

        /// <summary>
        /// Check if all slots are reserved
        /// </summary>
        public bool AreAllSlotsReserved()
        {
            return GetReservedSlotCount() >= slotCount;
        }

        /// <summary>
        /// Check if all slots are either occupied or reserved
        /// </summary>
        public bool AreAllSlotsOccupiedOrReserved()
        {
            return (GetOccupiedSlotCount() + GetReservedSlotCount()) >= slotCount;
        }
    }
}
