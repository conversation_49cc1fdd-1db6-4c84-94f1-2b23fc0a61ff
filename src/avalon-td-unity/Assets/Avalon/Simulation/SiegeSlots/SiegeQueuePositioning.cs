using Unity.Deterministic.Mathematics;
using Unity.Collections;
using Unity.Mathematics;

namespace Avalon.Simulation.SiegeSlots
{
    /// <summary>
    /// Utility class for calculating queue positions when all siege slots are occupied.
    /// Provides different queue formation patterns for organizing waiting enemies.
    /// </summary>
    public static class SiegeQueuePositioning
    {
        /// <summary>
        /// Different queue formation patterns
        /// </summary>
        public enum QueueFormation : byte
        {
            /// <summary>Single line behind the target</summary>
            SingleLine = 0,
            
            /// <summary>Double line formation</summary>
            DoubleLine = 1,
            
            /// <summary>Semicircle formation around the back of the target</summary>
            Semicircle = 2,
            
            /// <summary>Grid formation behind the target</summary>
            Grid = 3
        }
        
        /// <summary>
        /// Configuration for queue positioning
        /// </summary>
        public struct QueueConfig
        {
            public QueueFormation formation;
            public dfloat baseDistance;      // Base distance from siege slots
            public dfloat spacing;           // Spacing between queued enemies
            public dfloat rowSpacing;        // Spacing between rows (for multi-row formations)
            public int maxPerRow;            // Maximum enemies per row
            
            public static QueueConfig Default => new QueueConfig
            {
                formation = QueueFormation.SingleLine,
                baseDistance = new dfloat(1.5f),
                spacing = new dfloat(1.0f),
                rowSpacing = new dfloat(1.2f),
                maxPerRow = 4
            };
        }
        
        /// <summary>
        /// Calculate the position for a queued enemy based on their queue position
        /// </summary>
        public static dfloat3 CalculateQueuePosition(SiegeSlotData siegeSlotData, dfloat3 targetPosition, 
            int queuePosition, QueueConfig config)
        {
            switch (config.formation)
            {
                case QueueFormation.SingleLine:
                    return CalculateSingleLinePosition(siegeSlotData, targetPosition, queuePosition, config);
                    
                case QueueFormation.DoubleLine:
                    return CalculateDoubleLinePosition(siegeSlotData, targetPosition, queuePosition, config);
                    
                case QueueFormation.Semicircle:
                    return CalculateSemicirclePosition(siegeSlotData, targetPosition, queuePosition, config);
                    
                case QueueFormation.Grid:
                    return CalculateGridPosition(siegeSlotData, targetPosition, queuePosition, config);
                    
                default:
                    return CalculateSingleLinePosition(siegeSlotData, targetPosition, queuePosition, config);
            }
        }
        
        /// <summary>
        /// Calculate position for single line formation
        /// </summary>
        private static dfloat3 CalculateSingleLinePosition(SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            int queuePosition, QueueConfig config)
        {
            dfloat distance = siegeSlotData.slotRadius + config.baseDistance + config.spacing * queuePosition;
            
            // Position enemies in a line behind the target (negative Z direction)
            return targetPosition + new dfloat3(dfloat.Zero, dfloat.Zero, -distance);
        }
        
        /// <summary>
        /// Calculate position for double line formation
        /// </summary>
        private static dfloat3 CalculateDoubleLinePosition(SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            int queuePosition, QueueConfig config)
        {
            int row = queuePosition / 2;
            int side = queuePosition % 2; // 0 = left, 1 = right
            
            dfloat distance = siegeSlotData.slotRadius + config.baseDistance + config.rowSpacing * row;
            dfloat sideOffset = (side == 0 ? -config.spacing : config.spacing) * new dfloat(0.5f);
            
            return targetPosition + new dfloat3(sideOffset, dfloat.Zero, -distance);
        }
        
        /// <summary>
        /// Calculate position for semicircle formation
        /// </summary>
        private static dfloat3 CalculateSemicirclePosition(SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            int queuePosition, QueueConfig config)
        {
            dfloat queueRadius = siegeSlotData.slotRadius + config.baseDistance;
            
            // Distribute enemies in a semicircle behind the target (180 degrees)
            dfloat angleRange = dmath.PI; // 180 degrees
            dfloat angleStep = angleRange / math.max(config.maxPerRow - 1, 1);
            dfloat startAngle = dmath.PI + angleRange * new dfloat(0.5f); // Start from back-left
            
            int row = queuePosition / config.maxPerRow;
            int posInRow = queuePosition % config.maxPerRow;
            
            dfloat currentRadius = queueRadius + config.rowSpacing * row;
            dfloat angle = startAngle - angleStep * posInRow;
            
            dfloat x = dmath.cos(angle) * currentRadius;
            dfloat z = dmath.sin(angle) * currentRadius;
            
            return targetPosition + new dfloat3(x, dfloat.Zero, z);
        }
        
        /// <summary>
        /// Calculate position for grid formation
        /// </summary>
        private static dfloat3 CalculateGridPosition(SiegeSlotData siegeSlotData, dfloat3 targetPosition,
            int queuePosition, QueueConfig config)
        {
            int row = queuePosition / config.maxPerRow;
            int col = queuePosition % config.maxPerRow;
            
            dfloat distance = siegeSlotData.slotRadius + config.baseDistance + config.rowSpacing * row;
            
            // Center the grid
            dfloat totalWidth = (config.maxPerRow - 1) * config.spacing;
            dfloat startX = -totalWidth * new dfloat(0.5f);
            dfloat x = startX + col * config.spacing;
            
            return targetPosition + new dfloat3(x, dfloat.Zero, -distance);
        }
        
        /// <summary>
        /// Get the optimal queue formation based on the number of siege slots and available space
        /// </summary>
        public static QueueFormation GetOptimalFormation(SiegeSlotData siegeSlotData, int expectedQueueSize)
        {
            // Simple heuristics for choosing formation
            if (expectedQueueSize <= 4)
                return QueueFormation.SingleLine;
            else if (expectedQueueSize <= 8)
                return QueueFormation.DoubleLine;
            else if (siegeSlotData.slotCount >= 6) // Larger targets can support semicircle
                return QueueFormation.Semicircle;
            else
                return QueueFormation.Grid;
        }
        
        /// <summary>
        /// Calculate the total area required for a queue formation
        /// </summary>
        public static dfloat2 CalculateQueueArea(int queueSize, QueueConfig config)
        {
            switch (config.formation)
            {
                case QueueFormation.SingleLine:
                    return new dfloat2(config.spacing, config.baseDistance + config.spacing * queueSize);
                    
                case QueueFormation.DoubleLine:
                    int rows = (queueSize + 1) / 2;
                    return new dfloat2(config.spacing, config.baseDistance + config.rowSpacing * rows);
                    
                case QueueFormation.Grid:
                    int gridRows = (queueSize + config.maxPerRow - 1) / config.maxPerRow;
                    dfloat width = (config.maxPerRow - 1) * config.spacing;
                    dfloat depth = config.baseDistance + config.rowSpacing * gridRows;
                    return new dfloat2(width, depth);
                    
                case QueueFormation.Semicircle:
                    int semicircleRows = (queueSize + config.maxPerRow - 1) / config.maxPerRow;
                    dfloat radius = config.baseDistance + config.rowSpacing * semicircleRows;
                    return new dfloat2(radius * new dfloat(2.0f), radius);
                    
                default:
                    return new dfloat2(config.spacing, config.baseDistance + config.spacing * queueSize);
            }
        }
        
        /// <summary>
        /// Check if a queue position would cause overlap with siege slots
        /// </summary>
        public static bool WouldOverlapWithSlots(dfloat3 queuePosition, SiegeSlotData siegeSlotData, 
            dfloat3 targetPosition, dfloat? minDistanceParam = null)
        {
            var minDistance = minDistanceParam ?? dfloat.Half;
            for (int i = 0; i < siegeSlotData.slotCount; i++)
            {
                dfloat3 slotPosition = siegeSlotData.GetSlotPosition(i, targetPosition);
                dfloat distance = dmath.distance(queuePosition, slotPosition);
                
                if (distance < minDistance)
                    return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Adjust queue position to avoid obstacles or other constraints
        /// </summary>
        public static dfloat3 AdjustQueuePosition(dfloat3 basePosition, SiegeSlotData siegeSlotData,
            dfloat3 targetPosition, dfloat? adjustmentRadiusParam = null)
        {
            var adjustmentRadius = adjustmentRadiusParam ?? dfloat.One;
            // Simple adjustment: if position overlaps with slots, move it further back
            if (WouldOverlapWithSlots(basePosition, siegeSlotData, targetPosition))
            {
                dfloat3 direction = dmath.normalize(basePosition - targetPosition);
                return basePosition + direction * adjustmentRadius;
            }
            
            return basePosition;
        }
    }
}
