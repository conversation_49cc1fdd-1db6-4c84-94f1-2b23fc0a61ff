using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;
using Unity.Mathematics;
using FlowField;
using Avalon.Simulation.SiegeSlots;

namespace Avalon.Simulation.Movement
{
    /// <summary>
    /// Unified movement system that handles siege slot movement, regular flow field movement, and optional local avoidance.
    /// This replaces the separate SiegeSlotMovementSystem, FlowFieldSimpleMovementSystem, and LocalAvoidanceSystem.
    /// All units will move to siege spots when available, and local avoidance is enabled on a per-unit basis.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementSystemGroup))]
    [UpdateAfter(typeof(SiegeSlotManager))]
    public partial struct UnifiedMovementSystem : ISystem
    {
        // Flow field data
        private BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
        private ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
        private EntityQuery flowFieldQuery;
        
        // Siege slot data
        private ComponentLookup<SiegeSlotData> siegeSlotLookup;
        
        // Avoidance data
        private ComponentLookup<SimulationTransform> transformLookup;
        private ComponentLookup<AvoidanceData> avoidanceDataLookup;
        private ComponentLookup<FlowFieldFollower> followerLookup;
        private EntityQuery unitQuery;
        
        // System queries
        private EntityQuery fixedTimestepQuery;
        private EntityQuery movementQuery;
        
        // Neighbor data pool for avoidance
        private NativeArray<NeighborData> neighborDataPool;
        private bool poolInitialized;
        private const int MAX_UNITS = 10000;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Initialize flow field lookups
            flowFieldLookup = SystemAPI.GetBufferLookup<FlowFieldCellBuffer>(true);
            flowFieldGridLookup = SystemAPI.GetComponentLookup<FlowFieldGrid>(true);

            // Initialize siege slot lookups
            siegeSlotLookup = SystemAPI.GetComponentLookup<SiegeSlotData>(true);
            
            // Initialize avoidance lookups
            transformLookup = SystemAPI.GetComponentLookup<SimulationTransform>(true);
            avoidanceDataLookup = SystemAPI.GetComponentLookup<AvoidanceData>(true);
            followerLookup = SystemAPI.GetComponentLookup<FlowFieldFollower>(true);
            
            // Create queries
            flowFieldQuery = state.GetEntityQuery(typeof(FlowFieldGrid));
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            // Single movement query - all units have both SiegeSlotAssignment and AvoidanceData
            // AvoidanceData will always exist on an entity, but should only be used if its enabled
            // An enemy should always have a SeigeSlotAssignment as that determines where it should move to
            movementQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    ComponentType.ReadWrite<SimulationTransform>(),
                    ComponentType.ReadWrite<FlowFieldFollower>(),
                    ComponentType.ReadWrite<MovementTarget>(),
                    ComponentType.ReadWrite<Movement>(),
                    ComponentType.ReadWrite<SiegeSlotAssignment>(),
                    ComponentType.ReadWrite<AvoidanceData>()
                }
            });
            
            // Query for all units (for neighbor detection)
            unitQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    ComponentType.ReadOnly<SimulationTransform>(),
                    ComponentType.ReadOnly<FlowFieldFollower>(),
                    ComponentType.ReadOnly<AvoidanceData>()
                }
            });
            
            // Require fixed timestep
            state.RequireForUpdate<FixedTimestep>();
            
            // Initialize memory pool
            poolInitialized = false;
        }
        
        [BurstCompile]
        public void OnDestroy(ref SystemState state)
        {
            if (poolInitialized && neighborDataPool.IsCreated)
            {
                neighborDataPool.Dispose();
            }
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Skip if no units to process
            if (unitQuery.IsEmpty && movementQuery.IsEmpty) return;
            
            // Initialize neighbor data pool if needed
            if (!poolInitialized)
            {
                neighborDataPool = new NativeArray<NeighborData>(MAX_UNITS, Allocator.Persistent);
                poolInitialized = true;
            }
            
            // Update all lookups
            flowFieldLookup.Update(ref state);
            flowFieldGridLookup.Update(ref state);
            siegeSlotLookup.Update(ref state);
            transformLookup.Update(ref state);
            avoidanceDataLookup.Update(ref state);
            followerLookup.Update(ref state);
            
            // Get flow field entities and create target data
            var flowFieldEntities = flowFieldQuery.ToEntityArray(Allocator.TempJob);
            var targetData = CreateTargetDataArray(ref state, flowFieldEntities);
            
            // Get all units for neighbor detection
            var allUnits = unitQuery.ToEntityArray(Allocator.TempJob);
            
            // Ensure we don't exceed pool capacity
            var unitCount = math.min(allUnits.Length, MAX_UNITS);
            var neighborData = neighborDataPool.GetSubArray(0, unitCount);
            
            // Populate neighbor data
            var populateNeighborJob = new PopulateNeighborDataJob
            {
                entities = allUnits.GetSubArray(0, unitCount),
                transformLookup = transformLookup,
                followerLookup = followerLookup,
                avoidanceDataLookup = avoidanceDataLookup,
                neighborData = neighborData
            };

            // Schedule the job and ensure proper dependency tracking
            // The job reads SimulationTransform components, so we need to ensure proper dependency tracking
            state.Dependency = populateNeighborJob.Schedule(unitCount, 64, state.Dependency);

            // Schedule single unified movement job for all units
            if (!movementQuery.IsEmpty)
            {
                var movementJob = new UnifiedMovementJob
                {
                    deltaTime = fixedTimestep.tickDuration,
                    currentTime = fixedTimestep.currentTime,
                    flowFieldLookup = flowFieldLookup,
                    flowFieldGridLookup = flowFieldGridLookup,
                    flowFieldEntities = flowFieldEntities,
                    targetData = targetData,
                    siegeSlotLookup = siegeSlotLookup,
                    neighborData = neighborData
                };
                state.Dependency = movementJob.ScheduleParallel(movementQuery, state.Dependency);
            }
            
            // Dispose temporary arrays
            state.Dependency = flowFieldEntities.Dispose(state.Dependency);
            state.Dependency = targetData.Dispose(state.Dependency);
            state.Dependency = allUnits.Dispose(state.Dependency);
        }
        
        private NativeArray<TargetData> CreateTargetDataArray(ref SystemState state, NativeArray<Entity> flowFieldEntities)
        {
            var targetData = new NativeArray<TargetData>(flowFieldEntities.Length, Allocator.TempJob);
            
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                var target = new TargetData { isValid = false };
                
                if (flowFieldGridLookup.HasComponent(entity))
                {
                    var gridData = flowFieldGridLookup[entity];
                    if (transformLookup.HasComponent(gridData.targetEntity))
                    {
                        var targetTransform = transformLookup[gridData.targetEntity];
                        target.targetId = gridData.targetId;
                        target.targetPosition = targetTransform.position;
                        target.isValid = true;
                    }
                }
                
                targetData[i] = target;
            }
            
            return targetData;
        }
    }
}
