# Unified Movement System

## Overview

The Unified Movement System replaces the previous separate movement systems with a single, comprehensive system that handles:

1. **Siege Slot Movement** - Units move to assigned siege positions around targets
2. **Regular Flow Field Movement** - Standard pathfinding using flow fields
3. **Optional Local Avoidance** - Boids-based collision avoidance (enabled per unit)

## Key Components

### UnifiedMovementSystem
- **Location**: `Assets/Avalon/Simulation/Movement/UnifiedMovementSystem.cs`
- **Purpose**: Main system that orchestrates all movement processing using a single job
- **Update Group**: `MovementSystemGroup`
- **Dependencies**: Runs after `SiegeSlotManager`

### UnifiedMovementJob
- **Location**: `Assets/Avalon/Simulation/Movement/UnifiedMovementJob.cs`
- **Purpose**: Single burst-compiled job that handles all movement scenarios
- **Features**: Processes all units with both SiegeSlotAssignment and AvoidanceData components

## Replaced Systems

The following systems have been **replaced** by the unified system:

1. ~~`SiegeSlotMovementSystem`~~ → Now integrated into `UnifiedMovementSystem`
2. ~~`FlowFieldSimpleMovementSystem`~~ → Now integrated into `UnifiedMovementSystem`
3. ~~`LocalAvoidanceSystem`~~ → Now integrated into `UnifiedMovementSystem`

## Component Requirements

### Required Components (All Units)
- `SimulationTransform` - Unit position and rotation
- `FlowFieldFollower` - Movement parameters and settings
- `MovementTarget` - Target information for pathfinding
- `Movement` - Current movement vector
- `SiegeSlotAssignment` - Always present, determines where unit should move to
- `AvoidanceData` - Always present, but only used if `FlowFieldFollower.useAvoidance` is enabled

## Movement Priority

The system processes movement in the following priority order:

1. **Siege Slot Movement** (if `SiegeSlotAssignment` exists and is valid)
   - Units move to their assigned siege positions
   - Different speeds based on siege status (moving, queued, occupying)

2. **Regular Flow Field Movement** (fallback)
   - Standard pathfinding using flow field directions
   - Blends flow field with direct target direction when close

3. **Local Avoidance** (if enabled via `FlowFieldFollower.useAvoidance`)
   - Separation: Avoid crowding with nearby units
   - Cohesion: Move toward average position of neighbors
   - Alignment: Match velocity with neighbors
   - Obstacle Avoidance: Avoid unwalkable terrain

## Configuration

### Per-Unit Avoidance Control
Set `FlowFieldFollower.useAvoidance = true/false` to enable/disable avoidance per unit.

### Avoidance Parameters (in FlowFieldFollower)
- `avoidanceRadius` - Detection radius for neighbors
- `separationStrength` - How strongly to avoid crowding
- `cohesionStrength` - How strongly to group with neighbors
- `alignmentStrength` - How strongly to match neighbor velocities
- `obstacleAvoidanceStrength` - How strongly to avoid obstacles
- `avoidanceLayer` - Layer for grouping units (units only avoid same layer)

### Siege Slot Parameters
- Automatically handled by `SiegeSlotManager` and `SiegeSlotAssignment`
- Different movement speeds based on siege status
- Automatic status updates when reaching targets

## Performance Features

- **Burst Compilation**: All movement calculations are burst-compiled
- **Job System**: Parallel processing of unit movement
- **Memory Pooling**: Reuses neighbor data arrays to reduce allocations
- **Deterministic**: Uses deterministic math for network consistency

## Integration Notes

### Existing Code Compatibility
- All existing `FlowFieldFollower` configurations work unchanged
- Siege slot functionality remains the same
- Avoidance behavior is identical to the previous system

### System Dependencies
- Requires `SiegeSlotManager` to run first (for siege slot assignments)
- Integrates with existing flow field infrastructure
- Works with existing combat and effect systems

## Migration Guide

### For Developers
No code changes required - the unified system is a drop-in replacement.

### For Content Creators
- Use `FlowFieldFollower.useAvoidance` to control avoidance per unit type
- Siege slot behavior is automatic for units targeting siege slot entities
- All existing unit configurations continue to work

## Future Enhancements

The unified architecture makes it easy to add:
- Additional movement modes (formations, patrol routes, etc.)
- More sophisticated avoidance algorithms
- Dynamic movement parameter adjustments
- Performance optimizations through better batching
