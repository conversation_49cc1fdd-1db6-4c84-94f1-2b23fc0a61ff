using FlowField;
using Unity.Burst;
using Unity.Entities;

namespace Avalon.Simulation.Movement
{
    [BurstCompile]
    public partial struct StorePreviousTransformJob : IJobEntity
    {
        public void Execute(in SimulationTransform simulationTransform, ref PreviousSimulationTransform previousSimulationTransform)
        {
            // Store deterministic simulation state as previous state (DETERMINISTIC)
            previousSimulationTransform.Position = simulationTransform.position;
            previousSimulationTransform.Rotation = simulationTransform.rotation;
            previousSimulationTransform.Scale = simulationTransform.scale;
        }
    }
}