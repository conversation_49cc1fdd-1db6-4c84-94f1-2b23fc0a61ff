using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.Movement
{
    [System.Serializable]
    public struct FlowFieldFollower : IComponentData
    {
        // Group dfloat fields together for better cache alignment (8 bytes each)
        public dfloat maxSpeed;
        public dfloat minSpeed; // Minimum speed to prevent getting stuck
        public dfloat acceleration;
        public dfloat deceleration;
        public dfloat flowFieldStrength;

        // Avoidance parameters (only used if useAvoidance is true)
        public dfloat avoidanceRadius;
        public dfloat separationStrength;
        public dfloat cohesionStrength;
        public dfloat alignmentStrength;
        public dfloat obstacleAvoidanceStrength;

        // Group smaller fields together (4 bytes each for int, 1 byte for enum/bool)
        //public int? targetId; // Which flow field to follow
        public int avoidanceLayer;
        public MovementType movementType;
        public bool useAvoidance;

        // Distance threshold to disable avoidance near target (prevents circling)
        public dfloat targetProximityThreshold;
    }
}