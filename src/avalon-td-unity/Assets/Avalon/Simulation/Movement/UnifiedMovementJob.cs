using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using FlowField;
using Avalon.Simulation.Movement;
using Avalon.Simulation.SiegeSlots;
using Unity.Entities.UniversalDelegates;

namespace Avalon.Simulation.Movement
{
    /// <summary>
    /// Unified movement job that handles all movement scenarios.
    /// AvoidanceData will always exist on an entity, but should only be used if its enabled.
    /// An enemy should always have a SeigeSlotAssignment as that determines where it should move to.
    /// </summary>
    [BurstCompile]
    public partial struct UnifiedMovementJob : IJobEntity
    {
        public dfloat deltaTime;
        public dfloat currentTime;

        // Flow field data
        [ReadOnly] public BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
        [ReadOnly] public ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
        [ReadOnly] public NativeArray<Entity> flowFieldEntities;
        [ReadOnly] public NativeArray<TargetData> targetData;

        // Siege slot data
        [ReadOnly] public ComponentLookup<SiegeSlotData> siegeSlotLookup;

        // Avoidance data
        [ReadOnly] public NativeArray<NeighborData> neighborData;

        public void Execute(ref SimulationTransform transform, ref FlowFieldFollower follower,
            ref MovementTarget movementTarget, ref Movement movement,
            ref SiegeSlotAssignment siegeAssignment, ref AvoidanceData avoidance)
        {
            // Skip if no target
            if (movementTarget.targetId == null) return;

            dfloat2 currentPosition = new dfloat2(transform.position.x, transform.position.z);
            int targetId = movementTarget.targetId.Value;
            dfloat proximityThreshold = follower.targetProximityThreshold;

            // Early return for units occupying siege slots - they don't need movement calculations
            if (siegeAssignment.IsValid() && siegeAssignment.status == SiegeSlotStatus.OccupyingSlot)
            {
                // Set movement to zero and only handle rotation
                movement.Vector = dfloat2.zero;
                UpdateRotation(ref transform, dfloat2.zero, ref siegeAssignment);
                return;
            }

            // Determine movement mode and calculate desired direction
            dfloat2 desiredDirection;
            dfloat desiredSpeed;

            // Siege slot movement takes priority if assigned to move to slot
            if (siegeAssignment.IsValid() && siegeAssignment.status == SiegeSlotStatus.MovingToSlot)
            {
                var result = CalculateSiegeSlotMovement(currentPosition, ref siegeAssignment, follower);
                desiredDirection = result.direction;
                desiredSpeed = result.speed;
            }
            else
            {
                // Regular flow field movement
                var result = CalculateFlowFieldMovement(currentPosition, targetId, follower, proximityThreshold);
                desiredDirection = result.direction;
                desiredSpeed = result.speed;
            }

            // Apply avoidance if enabled (AvoidanceData always exists but only used if enabled)
            if (follower.useAvoidance)
            {
                var avoidanceResult = CalculateAvoidanceForces(currentPosition, desiredDirection,
                    follower, avoidance, targetId, proximityThreshold);
                desiredDirection = avoidanceResult.direction;
                desiredSpeed = avoidanceResult.speed;

                // Update avoidance data
                avoidance.desiredDirection = desiredDirection;
                avoidance.velocity = desiredDirection * desiredSpeed;
                avoidance.lastAvoidanceTime = currentTime;
            }

            // Calculate final movement (no smoothing for constant speed)
            dfloat2 desiredVelocity = desiredDirection * desiredSpeed;
            dfloat2 movementVector = desiredVelocity * deltaTime;

            // Prevent target overshoot - use different logic for siege slot movement
            if (siegeAssignment.IsValid())
            {
                movementVector = PreventSiegeSlotOvershoot(currentPosition, movementVector, ref siegeAssignment);
            }
            else
            {
                movementVector = PreventTargetOvershoot(currentPosition, movementVector, targetId, proximityThreshold);
            }

            // Update siege slot status if applicable
            if (siegeAssignment.IsValid())
            {
                UpdateSiegeSlotStatus(ref siegeAssignment, transform.position);
            }

            // Apply movement
            ApplyMovement(ref transform, ref movement, movementVector, follower.movementType, ref siegeAssignment);
        }
        
        private (dfloat2 direction, dfloat speed) CalculateSiegeSlotMovement(dfloat2 currentPosition,
            ref SiegeSlotAssignment siegeAssignment, FlowFieldFollower follower)
        {
            // Note: Units with OccupyingSlot status are handled by early return in ExecuteMovement
            // This method only handles MovingToSlot status
            dfloat3 targetPosition3D = siegeAssignment.SlotPosition;
            dfloat2 targetPosition = new dfloat2(targetPosition3D.x, targetPosition3D.z);

            dfloat2 toTarget = targetPosition - currentPosition;
            dfloat distance = dmath.length(toTarget);

            // Stop movement if very close to target
            if (distance < new dfloat(0.01f))
            {
                return (dfloat2.zero, dfloat.Zero);
            }

            dfloat2 direction = toTarget / distance;
            dfloat speed = CalculateSpeedForSiegeMovement(follower, siegeAssignment);

            return (direction, speed);
        }

        private (dfloat2 direction, dfloat speed) CalculateFlowFieldMovement(dfloat2 currentPosition,
            int targetId, FlowFieldFollower follower, dfloat proximityThreshold)
        {
            // Get flow field direction
            dfloat2 flowDirection = GetFlowFieldDirection(currentPosition, targetId, follower.movementType);

            // Use direct target direction when very close, but with smoother blending
            dfloat distanceToTarget = GetDistanceToTarget(currentPosition, targetId);
            dfloat2 directTargetDirection = GetDirectTargetDirection(currentPosition, targetId, proximityThreshold);
            dfloat directTargetMagnitude = dmath.length(directTargetDirection);

            if (directTargetMagnitude > dfloat.Zero && distanceToTarget < proximityThreshold * new dfloat(2.0f))
            {
                // Use smoother blending with hysteresis to prevent rapid switching
                dfloat blendDistance = proximityThreshold * new dfloat(1.5f);
                dfloat blendFactor = dmath.smoothstep(blendDistance, proximityThreshold * new dfloat(0.5f), distanceToTarget);
                blendFactor = dmath.clamp(blendFactor, dfloat.Zero, dfloat.One);

                // Only blend if the directions are significantly different to avoid micro-adjustments
                dfloat directionDifference = dmath.length(dmath.normalize(flowDirection) - directTargetDirection);
                if (directionDifference > new dfloat(0.1f))
                {
                    flowDirection = flowDirection * (dfloat.One - blendFactor) + directTargetDirection * blendFactor;
                }
            }

            dfloat speed = CalculateDesiredSpeed(follower, dmath.length(flowDirection));
            dfloat2 direction = dmath.length(flowDirection) > new dfloat(0.01f) ? dmath.normalize(flowDirection) : dfloat2.zero;

            return (direction, speed);
        }
        
        private (dfloat2 direction, dfloat speed) CalculateAvoidanceForces(dfloat2 currentPosition, 
            dfloat2 desiredDirection, FlowFieldFollower follower, AvoidanceData avoidance, 
            int targetId, dfloat proximityThreshold)
        {
            // Check proximity to target to disable avoidance when close (prevents circling)
            dfloat distanceToTarget = GetDistanceToTarget(currentPosition, targetId);
            bool isNearTarget = distanceToTarget < proximityThreshold;
            
            // Calculate avoidance forces (but scale them down when near target)
            dfloat avoidanceScale = isNearTarget ? dfloat.Zero : dfloat.One;
            
            dfloat2 separationForce = CalculateSeparation(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) * avoidanceScale;
            dfloat2 cohesionForce = CalculateCohesion(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) * avoidanceScale;
            dfloat2 alignmentForce = CalculateAlignment(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) * avoidanceScale;
            dfloat2 obstacleForce = CalculateObstacleAvoidance(currentPosition, avoidance.velocity, follower.avoidanceRadius, follower.movementType);
            
            // Combine forces
            dfloat2 totalForce = desiredDirection * follower.flowFieldStrength +
                                separationForce * follower.separationStrength +
                                cohesionForce * follower.cohesionStrength +
                                alignmentForce * follower.alignmentStrength +
                                obstacleForce * follower.obstacleAvoidanceStrength;
            
            // Use constant speed movement - no acceleration/deceleration
            dfloat2 finalDirection = dmath.length(totalForce) > new dfloat(0.01f) ?
                dmath.normalize(totalForce) : dfloat2.zero;

            // Always use max speed when moving (constant speed)
            dfloat finalSpeed = dmath.length(finalDirection) > dfloat.Zero ? follower.maxSpeed : dfloat.Zero;

            return (finalDirection, finalSpeed);
        }
        
        private dfloat CalculateSpeedForSiegeMovement(FlowFieldFollower follower, SiegeSlotAssignment siegeAssignment)
        {
            return siegeAssignment.status switch
            {
                SiegeSlotStatus.MovingToSlot => follower.maxSpeed,
                SiegeSlotStatus.OccupyingSlot => dfloat.Zero, // No movement when occupying slot
                _ => follower.maxSpeed
            };
        }
        
        private dfloat CalculateDesiredSpeed(FlowFieldFollower follower, dfloat directionMagnitude)
        {
            return directionMagnitude > new dfloat(0.01f) ? follower.maxSpeed : dfloat.Zero;
        }



        private void UpdateSiegeSlotStatus(ref SiegeSlotAssignment siegeAssignment, dfloat3 currentPosition)
        {
            if (siegeAssignment.HasReachedTarget(currentPosition) && siegeAssignment.IsMoving())
            {
                siegeAssignment.MarkSlotOccupied();
            }
        }
        
        private void ApplyMovement(ref SimulationTransform transform, ref Movement movement,
            dfloat2 movementVector, MovementType movementType, ref SiegeSlotAssignment siegeAssignment)
        {
            movement.Vector = movementVector;

            dfloat3 newPosition = transform.position + new dfloat3(movementVector.x, dfloat.Zero, movementVector.y);

            if (IsPositionWalkable(new float2((float)newPosition.x, (float)newPosition.z), movementType))
            {
                transform.position = newPosition;
                UpdateRotation(ref transform, movementVector, ref siegeAssignment);
            }
            else
            {
                TryPartialMovement(ref transform, movementVector, movementType, ref siegeAssignment);
            }
        }
        
        private void UpdateRotation(ref SimulationTransform transform, dfloat2 movementVector, ref SiegeSlotAssignment siegeAssignment)
        {
            // If unit is occupying a siege slot, rotate towards the target entity instead of movement direction
            if (siegeAssignment.IsValid() && siegeAssignment.status == SiegeSlotStatus.OccupyingSlot)
            {
                UpdateSiegeSlotRotation(ref transform, ref siegeAssignment);
                return;
            }

            // Default behavior: Update rotation to face movement direction if moving (DETERMINISTIC)
            if (dmath.lengthsq(movementVector) > new dfloat(0.001f))
            {
                // Calculate Y-axis rotation angle from 2D movement vector (much safer than complex angle calculations)
                // This avoids the complex matrix operations that can cause crashes
                dfloat angle = dmath.atan2(movementVector.x, movementVector.y);

                // Validate the angle is finite before creating rotation
                if (dmath.isfinite(angle))
                {
                    transform.rotation = dquaternion.RotateY(angle);
                }
            }
        }

        private void UpdateSiegeSlotRotation(ref SimulationTransform transform, ref SiegeSlotAssignment siegeAssignment)
        {
            // For siege slot rotation, face towards the TargetPosition from SiegeSlotAssignment
            // This is much simpler and more reliable than calculating the center position
            dfloat3 currentPosition = transform.position;
            dfloat3 targetPosition = siegeAssignment.TargetPosition;

            // Calculate direction to target (only use X and Z components for Y-axis rotation)
            dfloat2 currentPos2D = new dfloat2(currentPosition.x, currentPosition.z);
            dfloat2 targetPos2D = new dfloat2(targetPosition.x, targetPosition.z);
            dfloat2 toTarget2D = targetPos2D - currentPos2D;

            // Only rotate if there's a meaningful distance to the target
            if (dmath.lengthsq(toTarget2D) > new dfloat(0.001f))
            {
                // Calculate Y-axis rotation angle to face the target
                dfloat angle = dmath.atan2(toTarget2D.x, toTarget2D.y);

                // Validate the angle is finite before creating rotation
                if (dmath.isfinite(angle))
                {
                    transform.rotation = dquaternion.RotateY(angle);
                }
            }
        }

        private void TryPartialMovement(ref SimulationTransform transform, dfloat2 movementVector, MovementType movementType, ref SiegeSlotAssignment siegeAssignment)
        {
            dfloat2 partialMovement = movementVector * new dfloat(0.5f);
            dfloat3 partialPosition = transform.position + new dfloat3(partialMovement.x, dfloat.Zero, partialMovement.y);

            if (IsPositionWalkable(new float2((float)partialPosition.x, (float)partialPosition.z), movementType))
            {
                transform.position = partialPosition;
                UpdateRotation(ref transform, partialMovement, ref siegeAssignment);
            }
        }

        // Flow field helper methods
        private dfloat2 GetFlowFieldDirection(dfloat2 worldPos, int targetId, MovementType movementType)
        {
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                if (!flowFieldGridLookup.HasComponent(entity)) continue;

                var gridData = flowFieldGridLookup[entity];
                if (gridData.targetId != targetId) continue;

                if (flowFieldLookup.HasBuffer(entity))
                {
                    var buffer = flowFieldLookup[entity];
                    return FlowFieldUtils.SampleFlowField(buffer, worldPos, gridData.worldOrigin,
                        gridData.cellSize, gridData.gridSize, movementType);
                }
            }

            return dfloat2.zero;
        }

        private bool IsPositionWalkable(float2 position, MovementType movementType)
        {
            // Check all flow fields to see if this position is walkable
            for (int i = 0; i < flowFieldEntities.Length; i++)
            {
                var entity = flowFieldEntities[i];
                if (!flowFieldGridLookup.HasComponent(entity)) continue;
                if (!flowFieldLookup.HasBuffer(entity)) continue;

                var gridData = flowFieldGridLookup[entity];
                var buffer = flowFieldLookup[entity];

                var relativePos = new dfloat2((dfloat)position.x, (dfloat)position.y) - gridData.worldOrigin;
                var gridPos = new int2((int)(relativePos.x / gridData.cellSize.x),
                    (int)(relativePos.y / gridData.cellSize.y));

                if (FlowFieldGridUtils.IsValidGridPosition(gridPos, gridData.gridSize))
                {
                    var index = FlowFieldGridUtils.GridToIndex(gridPos, gridData.gridSize);
                    if (index < buffer.Length)
                    {
                        var cell = buffer[index].cell;
                        return cell.IsWalkableFor(movementType);
                    }
                }

                // If position is outside grid, consider it unwalkable
                return false;
            }

            // If no flow field found, assume walkable (fallback)
            return true;
        }

        private dfloat2 PreventTargetOvershoot(dfloat2 currentPos, dfloat2 movement, int targetId, dfloat proximityThreshold)
        {
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
                var toTarget = targetPos - currentPos;
                var distanceToTarget = dmath.length(toTarget);
                var movementMagnitude = dmath.length(movement);

                // Only prevent overshoot, no deceleration - maintain constant speed
                if (movementMagnitude > distanceToTarget && distanceToTarget > new dfloat(0.01f))
                {
                    var normalizedToTarget = toTarget / distanceToTarget;
                    movement = normalizedToTarget * distanceToTarget;
                }

                break;
            }

            return movement;
        }

        private dfloat2 PreventSiegeSlotOvershoot(dfloat2 currentPos, dfloat2 movement, ref SiegeSlotAssignment siegeAssignment)
        {
            dfloat3 targetPosition3D = siegeAssignment.SlotPosition;
            dfloat2 targetPos = new dfloat2(targetPosition3D.x, targetPosition3D.z);
            dfloat2 toTarget = targetPos - currentPos;
            dfloat distanceToTarget = dmath.length(toTarget);
            dfloat movementMagnitude = dmath.length(movement);

            // Only prevent overshoot, no deceleration - maintain constant speed
            if (movementMagnitude > distanceToTarget && distanceToTarget > new dfloat(0.01f))
            {
                dfloat2 normalizedToTarget = toTarget / distanceToTarget;
                movement = normalizedToTarget * distanceToTarget;
            }

            return movement;
        }

        private dfloat GetDistanceToTarget(dfloat2 worldPos, int targetId)
        {
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
                var diff = targetPos - worldPos;
                return dmath.length(diff);
            }

            return new dfloat(1000.0f);
        }

        private dfloat2 GetDirectTargetDirection(dfloat2 currentPos, int targetId, dfloat proximityThreshold)
        {
            for (int i = 0; i < targetData.Length; i++)
            {
                var target = targetData[i];
                if (!target.isValid || target.targetId != targetId) continue;

                var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
                var toTarget = targetPos - currentPos;
                var distance = dmath.length(toTarget);

                var directTargetRange = proximityThreshold * new dfloat(2.0f);
                if (distance < directTargetRange && distance > new dfloat(0.01f))
                {
                    return toTarget / distance;
                }

                break;
            }

            return dfloat2.zero;
        }

        // Avoidance calculation methods
        private dfloat2 CalculateSeparation(dfloat2 position, dfloat radius, int avoidanceLayer)
        {
            var separationForce = dfloat2.zero;
            var neighborCount = 0;

            for (int i = 0; i < neighborData.Length; i++)
            {
                var neighbor = neighborData[i];
                if (!neighbor.isValid) continue;

                // Skip units on different avoidance layers
                if (neighbor.avoidanceLayer != avoidanceLayer) continue;

                var offset = position - new dfloat2(neighbor.position.x, neighbor.position.z);
                var distance = dmath.length(offset);

                // Skip self and units outside radius
                if (distance < new dfloat(0.01f) || distance > radius) continue;

                // Calculate separation force (stronger when closer)
                var separationStrength = (radius - distance) / radius;
                var normalizedOffset = offset / distance;
                separationForce += normalizedOffset * separationStrength;
                neighborCount++;
            }

            return neighborCount > 0 ? separationForce / neighborCount : dfloat2.zero;
        }

        private dfloat2 CalculateCohesion(dfloat2 position, dfloat radius, int avoidanceLayer)
        {
            var averagePosition = dfloat2.zero;
            var neighborCount = 0;

            for (int i = 0; i < neighborData.Length; i++)
            {
                var neighbor = neighborData[i];
                if (!neighbor.isValid) continue;

                // Skip units on different avoidance layers
                if (neighbor.avoidanceLayer != avoidanceLayer) continue;

                var neighborPos = new dfloat2(neighbor.position.x, neighbor.position.z);
                var offset = neighborPos - position;
                var distance = dmath.length(offset);

                // Skip self and units outside radius
                if (distance < new dfloat(0.01f) || distance > radius) continue;

                averagePosition += neighborPos;
                neighborCount++;
            }

            if (neighborCount > 0)
            {
                averagePosition /= neighborCount;
                var cohesionDirection = averagePosition - position;
                var distance = dmath.length(cohesionDirection);
                return distance > new dfloat(0.01f) ? cohesionDirection / distance : dfloat2.zero;
            }

            return dfloat2.zero;
        }

        private dfloat2 CalculateAlignment(dfloat2 position, dfloat radius, int avoidanceLayer)
        {
            var averageVelocity = dfloat2.zero;
            var neighborCount = 0;

            for (int i = 0; i < neighborData.Length; i++)
            {
                var neighbor = neighborData[i];
                if (!neighbor.isValid) continue;

                // Skip units on different avoidance layers
                if (neighbor.avoidanceLayer != avoidanceLayer) continue;

                // Map 3D positions (x,y,z) to flow field coordinates (x,z)
                var offset = new dfloat2(neighbor.position.x, neighbor.position.z) - position;
                var distance = dmath.length(offset);

                // Skip self and units outside radius
                if (distance < new dfloat(0.01f) || distance > radius) continue;

                averageVelocity += neighbor.velocity;
                neighborCount++;
            }

            if (neighborCount > 0)
            {
                averageVelocity /= neighborCount;
                var magnitude = dmath.length(averageVelocity);
                return magnitude > new dfloat(0.01f) ? averageVelocity / magnitude : dfloat2.zero;
            }

            return dfloat2.zero;
        }

        private dfloat2 CalculateObstacleAvoidance(dfloat2 position, dfloat2 velocity, dfloat radius, MovementType movementType)
        {
            var avoidanceForce = dfloat2.zero;
            var lookAheadDistance = radius * new dfloat(2.0f);
            var velocityMagnitude = dmath.length(velocity);

            if (velocityMagnitude < new dfloat(0.01f)) return avoidanceForce;

            var normalizedVelocity = velocity / velocityMagnitude;
            var lookAheadPosition = position + normalizedVelocity * lookAheadDistance;

            // Check if look-ahead position is walkable
            if (!IsPositionWalkable(new float2((float)lookAheadPosition.x, (float)lookAheadPosition.y), movementType))
            {
                // Find a perpendicular direction to avoid the obstacle
                var perpendicular = new dfloat2(-normalizedVelocity.y, normalizedVelocity.x);

                // Test both perpendicular directions and choose the better one
                var leftAvoidance = position + perpendicular * radius;
                var rightAvoidance = position - perpendicular * radius;

                bool leftWalkable = IsPositionWalkable(new float2((float)leftAvoidance.x, (float)leftAvoidance.y), movementType);
                bool rightWalkable = IsPositionWalkable(new float2((float)rightAvoidance.x, (float)rightAvoidance.y), movementType);

                if (leftWalkable && !rightWalkable)
                {
                    avoidanceForce = perpendicular;
                }
                else if (rightWalkable && !leftWalkable)
                {
                    avoidanceForce = -perpendicular;
                }
                else if (leftWalkable && rightWalkable)
                {
                    // Choose the direction that's more aligned with the desired direction
                    avoidanceForce = perpendicular; // Default to left
                }
            }

            return avoidanceForce;
        }
    }






}
