﻿using Unity.Entities;
using Unity.Mathematics;
using Unity.Deterministic.Mathematics;

namespace FlowField
{
    public struct FlowFieldGrid : IComponentData
    {
        public int2 gridSize;
        public dfloat2 cellSize;
        public dfloat2 worldOrigin;
        public Entity targetEntity;
        public int targetId;
        public MovementType movementType;
        public bool needsUpdate;
        public dfloat lastUpdateTime;
        public dfloat updateInterval; // How often to recalculate
    }
}