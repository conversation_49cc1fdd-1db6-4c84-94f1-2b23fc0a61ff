using Unity.Deterministic.Mathematics;

namespace FlowField
{
    public struct FlowFieldCell
    {
        public dfloat2 direction;
        public dfloat cost;
        public dfloat distance;
        public byte walkabilityMask; // Bitmask for different movement types
        public bool isTarget;

        public bool IsWalkableFor(MovementType movementType)
        {
            return (walkabilityMask & (1 << (int)movementType)) != 0;
        }

        public void SetWalkable(MovementType movementType, bool walkable)
        {
            if (walkable)
                walkabilityMask |= (byte)(1 << (int)movementType);
            else
                walkabilityMask &= (byte)~(1 << (int)movementType);
        }
    }
}