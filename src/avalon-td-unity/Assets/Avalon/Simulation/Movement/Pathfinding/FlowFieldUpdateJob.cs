using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;

namespace FlowField
{
    [BurstCompile]
    public partial struct FlowFieldUpdateJob : IJobEntity
    {
        [ReadOnly] public ComponentLookup<SimulationTransform> transformLookup;
        [ReadOnly] public dfloat currentTime;
        public EntityCommandBuffer.ParallelWriter ecb;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref FlowFieldGrid flowField)
        {
            bool shouldUpdate = flowField.needsUpdate ||
                                (currentTime - flowField.lastUpdateTime) > flowField.updateInterval;

            if (!shouldUpdate) return;

            var gridData = flowField;
            var cellCount = gridData.gridSize.x * gridData.gridSize.y;

            // Allocate temporary arrays for calculation
            var cells = new NativeArray<FlowFieldCell>(cellCount, Allocator.Temp);
            var distances = new NativeArray<dfloat>(cellCount, Allocator.Temp);
            var openSet = new NativeList<int2>(Allocator.Temp);

            try
            {
                // Initialize grid
                InitializeGrid(cells, distances, gridData);

                // Set target if valid
                if (transformLookup.HasComponent(gridData.targetEntity))
                {
                    var targetTransform = transformLookup[gridData.targetEntity];
                    var targetGridPos = FlowFieldGridUtils.WorldToGrid(
                        new float2((float)targetTransform.position.x, (float)targetTransform.position.y),
                        gridData.worldOrigin, gridData.cellSize);

                    if (FlowFieldGridUtils.IsValidGridPosition(targetGridPos, gridData.gridSize))
                    {
                        var targetIndex = FlowFieldGridUtils.GridToIndex(targetGridPos, gridData.gridSize);
                        var targetCell = cells[targetIndex];
                        targetCell.isTarget = true;
                        targetCell.distance = dfloat.Zero;
                        cells[targetIndex] = targetCell;
                        distances[targetIndex] = dfloat.Zero;
                        openSet.Add(targetGridPos);
                    }
                }

                // Calculate distances using optimized Dijkstra
                CalculateDistances(cells, distances, openSet, gridData);

                // Calculate flow directions
                CalculateFlowDirections(cells, distances, gridData.gridSize);

                // Store results in buffer
                var buffer = ecb.SetBuffer<FlowFieldCellBuffer>(entityInQueryIndex, entity);
                buffer.ResizeUninitialized(cellCount);
                for (int i = 0; i < cellCount; i++)
                {
                    buffer[i] = new FlowFieldCellBuffer { cell = cells[i] };
                }
            }
            finally
            {
                // Ensure cleanup even if exceptions occur
                if (cells.IsCreated) cells.Dispose();
                if (distances.IsCreated) distances.Dispose();
                if (openSet.IsCreated) openSet.Dispose();
            }

            flowField.needsUpdate = false;
            flowField.lastUpdateTime = currentTime;
        }

        private static void InitializeGrid(NativeArray<FlowFieldCell> cells,
            NativeArray<dfloat> distances, FlowFieldGrid gridData)
        {
            for (int i = 0; i < cells.Length; i++)
            {
                var cell = new FlowFieldCell
                {
                    direction = dfloat2.zero,
                    cost = GetMovementTypeCost(gridData.movementType),
                    distance = dfloat.MAX_VALUE,
                    walkabilityMask = 0xFF, // All movement types can walk by default
                    isTarget = false
                };

                cells[i] = cell;
                distances[i] = dfloat.MAX_VALUE;
            }
        }

        private static dfloat GetMovementTypeCost(MovementType movementType)
        {
            return movementType switch
            {
                MovementType.Heavy => new dfloat(1.5f),
                MovementType.Flying => new dfloat(0.8f),
                _ => dfloat.One
            };
        }

        private static void CalculateDistances(NativeArray<FlowFieldCell> cells,
            NativeArray<dfloat> distances,
            NativeList<int2> openSet,
            FlowFieldGrid gridData)
        {
            var neighbors = new NativeArray<int2>(8, Allocator.Temp);
            neighbors[0] = new int2(-1, 0);
            neighbors[1] = new int2(1, 0);
            neighbors[2] = new int2(0, -1);
            neighbors[3] = new int2(0, 1);
            neighbors[4] = new int2(-1, -1);
            neighbors[5] = new int2(1, -1);
            neighbors[6] = new int2(-1, 1);
            neighbors[7] = new int2(1, 1);

            var diagonalCost = new dfloat(1.414213562373095f);

            try
            {
                while (openSet.Length > 0)
                {
                    // Find minimum distance node
                    int minIndex = 0;
                    dfloat minDistance = dfloat.MAX_VALUE;

                    for (int i = 0; i < openSet.Length; i++)
                    {
                        var pos = openSet[i];
                        var index = FlowFieldGridUtils.GridToIndex(pos, gridData.gridSize);
                        if (distances[index] < minDistance)
                        {
                            minDistance = distances[index];
                            minIndex = i;
                        }
                    }

                    var currentPos = openSet[minIndex];
                    openSet.RemoveAtSwapBack(minIndex);

                    var currentIndex = FlowFieldGridUtils.GridToIndex(currentPos, gridData.gridSize);
                    var currentDistance = distances[currentIndex];

                    // Process neighbors
                    for (int i = 0; i < 8; i++)
                    {
                        var neighborPos = currentPos + neighbors[i];

                        if (!FlowFieldGridUtils.IsValidGridPosition(neighborPos, gridData.gridSize))
                            continue;

                        var neighborIndex = FlowFieldGridUtils.GridToIndex(neighborPos, gridData.gridSize);
                        var neighborCell = cells[neighborIndex];

                        if (!neighborCell.IsWalkableFor(gridData.movementType)) continue;

                        var moveCost = (i < 4) ? dfloat.One : diagonalCost;
                        var newDistance = currentDistance + moveCost * neighborCell.cost;

                        if (newDistance < distances[neighborIndex])
                        {
                            distances[neighborIndex] = newDistance;
                            neighborCell.distance = newDistance;
                            cells[neighborIndex] = neighborCell;

                            // Add to open set if not already there
                            bool alreadyInOpenSet = false;
                            for (int j = 0; j < openSet.Length; j++)
                            {
                                if (openSet[j].x == neighborPos.x && openSet[j].y == neighborPos.y)
                                {
                                    alreadyInOpenSet = true;
                                    break;
                                }
                            }

                            if (!alreadyInOpenSet)
                            {
                                openSet.Add(neighborPos);
                            }
                        }
                    }
                }
            }
            finally
            {
                neighbors.Dispose();
            }
        }

        private static void CalculateFlowDirections(NativeArray<FlowFieldCell> cells,
            NativeArray<dfloat> distances,
            int2 gridSize)
        {
            for (int index = 0; index < cells.Length; index++)
            {
                var gridPos = FlowFieldGridUtils.IndexToGrid(index, gridSize);
                var cell = cells[index];

                if (cell.isTarget)
                {
                    cell.direction = dfloat2.zero;
                    cells[index] = cell;
                    continue;
                }

                dfloat minDistance = dfloat.MAX_VALUE;
                dfloat2 bestDirection = dfloat2.zero;

                // Check all 8 neighbors
                for (int dx = -1; dx <= 1; dx++)
                {
                    for (int dy = -1; dy <= 1; dy++)
                    {
                        if (dx == 0 && dy == 0) continue;

                        var neighborPos = gridPos + new int2(dx, dy);

                        if (!FlowFieldGridUtils.IsValidGridPosition(neighborPos, gridSize))
                            continue;

                        var neighborIndex = FlowFieldGridUtils.GridToIndex(neighborPos, gridSize);
                        var neighborDistance = distances[neighborIndex];

                        if (neighborDistance < minDistance)
                        {
                            minDistance = neighborDistance;
                            bestDirection = new dfloat2((dfloat)dx, (dfloat)dy);
                        }
                    }
                }

                if (minDistance < dfloat.MAX_VALUE)
                {
                    // Normalize direction
                    var magnitude = dmath.Sqrt(bestDirection.x * bestDirection.x + bestDirection.y * bestDirection.y);
                    if (magnitude > dfloat.Zero)
                    {
                        cell.direction = bestDirection / magnitude;
                    }
                }

                cells[index] = cell;
            }
        }
    }
}