using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Mathematics;

namespace FlowField
{
    public static class FlowFieldGridUtils
    {
        [BurstCompile]
        public static int2 WorldToGrid(float2 worldPos, dfloat2 worldOrigin, dfloat2 cellSize)
        {
            var relativePos = new dfloat2((dfloat)worldPos.x, (dfloat)worldPos.y) - worldOrigin;
            return new int2((int)(relativePos.x / cellSize.x), (int)(relativePos.y / cellSize.y));
        }

        [BurstCompile]
        public static bool IsValidGridPosition(int2 gridPos, int2 gridSize)
        {
            return gridPos.x >= 0 && gridPos.x < gridSize.x && gridPos.y >= 0 && gridPos.y < gridSize.y;
        }

        [BurstCompile]
        public static int GridToIndex(int2 gridPos, int2 gridSize)
        {
            return gridPos.y * gridSize.x + gridPos.x;
        }

        [BurstCompile]
        public static int2 IndexToGrid(int index, int2 gridSize)
        {
            return new int2(index % gridSize.x, index / gridSize.x);
        }
    }
}