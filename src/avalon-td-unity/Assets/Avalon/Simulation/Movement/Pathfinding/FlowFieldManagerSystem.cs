using Avalon.Simulation;
using Avalon.Simulation.Movement;
using Unity.Burst;
using Unity.Entities;

namespace FlowField
{
    [BurstCompile]
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateBefore(typeof(MovementSystemGroup))]
    public partial struct FlowFieldManagerSystem : ISystem
    {
        private ComponentLookup<SimulationTransform> transformLookup;
        private EntityQuery flowFieldQuery;
        private EntityQuery fixedTimestepQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            transformLookup = state.GetComponentLookup<SimulationTransform>(true);

            // Create optimized query for flow fields that need updates
            flowFieldQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[] { typeof(FlowFieldGrid) },
                Options = EntityQueryOptions.FilterWriteGroup
            });

            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));

            // Require at least one flow field and fixed timestep to exist before running
            state.RequireForUpdate(flowFieldQuery);
            state.RequireForUpdate<FixedTimestep>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;

            // Update component lookups without completing dependencies
            transformLookup.Update(ref state);

            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);

            // Schedule flow field update job
            var updateJob = new FlowFieldUpdateJob
            {
                transformLookup = transformLookup,
                currentTime = fixedTimestep.currentTime,
                ecb = ecb.AsParallelWriter()
            };

            // Schedule job with proper dependency chaining
            var jobHandle = updateJob.ScheduleParallel(flowFieldQuery, state.Dependency);

            // Assign dependency before completing to ensure proper dependency tracking
            state.Dependency = jobHandle;

            // Complete the job handle to ensure FlowFieldGrid writes are synchronized
            // This is necessary because other systems (like FlowFieldAvoidanceSystem)
            // read from FlowFieldGrid and need the updates to be complete
            jobHandle.Complete();
        }

    }
}