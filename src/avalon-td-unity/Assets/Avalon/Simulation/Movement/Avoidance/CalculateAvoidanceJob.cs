// using Avalon.Simulation.Movement;
// using Unity.Burst;
// using Unity.Collections;
// using Unity.Deterministic.Mathematics;
// using Unity.Entities;
// using Unity.Mathematics;
//
// namespace FlowField
// {
//     [BurstCompile]
//     public partial struct CalculateAvoidanceJob : IJobEntity
//     {
//         public dfloat deltaTime;
//         public dfloat currentTime;
//         [ReadOnly] public BufferLookup<FlowFieldCellBuffer> flowFieldLookup;
//         [ReadOnly] public ComponentLookup<FlowFieldGrid> flowFieldGridLookup;
//         [ReadOnly] public NativeArray<Entity> flowFieldEntities;
//
//         // For neighbor detection - using separate data structure to avoid aliasing
//         [ReadOnly] public NativeArray<NeighborData> neighborData;
//
//         // For target proximity detection - using separate data structure to avoid aliasing
//         [ReadOnly] public NativeArray<TargetData> targetData;
//
//         public void Execute(
//             ref AvoidanceData avoidance, 
//             ref FlowFieldFollower follower,
//             ref MovementTarget movementTarget,
//             ref SimulationTransform transform,
//             ref Movement movement)
//         {
//             if (movementTarget.targetId == null)
//                 return;
//             
//             // Cache frequently used values for efficiency (DETERMINISTIC)
//             // Map 3D world position (x,y,z) to flow field coordinates (x,z)
//             var currentPosition = new dfloat2(transform.position.x, transform.position.z);
//             var targetId = movementTarget.targetId.Value;
//             var proximityThreshold = follower.targetProximityThreshold;
//
//             // Get flow field direction using proper lookup
//             var flowDirection = GetFlowFieldDirection(currentPosition, targetId, follower.movementType);
//
//             // Calculate distance to target once for efficiency
//             var distanceToTarget = GetDistanceToTarget(currentPosition, targetId);
//
//             // Use direct target direction when very close to ensure units reach the exact target
//             var directTargetDirection = GetDirectTargetDirection(currentPosition, targetId, proximityThreshold);
//             var directTargetMagnitude = dmath.Sqrt(directTargetDirection.x * directTargetDirection.x +
//                                                    directTargetDirection.y * directTargetDirection.y);
//             if (directTargetMagnitude > dfloat.Zero)
//             {
//                 // Blend between flow field and direct target direction based on distance
//                 var blendFactor = dmath.min(proximityThreshold / dmath.max(distanceToTarget, new dfloat(0.01f)),
//                     dfloat.One);
//                 flowDirection = flowDirection * (dfloat.One - blendFactor) + directTargetDirection * blendFactor;
//             }
//
//             // Check proximity to target to disable avoidance when close (prevents circling)
//             var isNearTarget = distanceToTarget < proximityThreshold;
//
//             // Calculate avoidance forces (but scale them down when near target)
//             var avoidanceScale = isNearTarget ? dfloat.Zero : dfloat.One;
//
//             var separationForce =
//                 CalculateSeparation(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) *
//                 avoidanceScale;
//             var cohesionForce = CalculateCohesion(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) *
//                                 avoidanceScale;
//             var alignmentForce =
//                 CalculateAlignment(currentPosition, follower.avoidanceRadius, follower.avoidanceLayer) * avoidanceScale;
//
//             // Keep obstacle avoidance even when near target (safety)
//             var obstacleForce = CalculateObstacleAvoidance(currentPosition, avoidance.velocity,
//                 follower.avoidanceRadius, follower.movementType);
//
//             // Combine forces - flow field strength increases when near target
//             var flowFieldScale = dfloat.One;
//             var totalForce = flowDirection * (follower.flowFieldStrength * flowFieldScale) +
//                              separationForce * follower.separationStrength +
//                              cohesionForce * follower.cohesionStrength +
//                              alignmentForce * follower.alignmentStrength +
//                              obstacleForce * follower.obstacleAvoidanceStrength;
//
//             // Normalize desired direction
//             var magnitude = dmath.Sqrt(totalForce.x * totalForce.x + totalForce.y * totalForce.y);
//             if (magnitude > dfloat.Zero)
//             {
//                 avoidance.desiredDirection = totalForce / magnitude;
//             }
//             else
//             {
//                 avoidance.desiredDirection = flowDirection;
//             }
//
//             // Update velocity with acceleration/deceleration
//             var desiredVelocity = avoidance.desiredDirection * follower.maxSpeed;
//             var velocityDiff = desiredVelocity - avoidance.velocity;
//             var velocityDiffMag = dmath.Sqrt(velocityDiff.x * velocityDiff.x + velocityDiff.y * velocityDiff.y);
//
//             if (velocityDiffMag > dfloat.Zero)
//             {
//                 var accelerationRate = follower.acceleration;
//                 var maxVelocityChange = accelerationRate * deltaTime;
//
//                 if (velocityDiffMag > maxVelocityChange)
//                 {
//                     avoidance.velocity += (velocityDiff / velocityDiffMag) * maxVelocityChange;
//                 }
//                 else
//                 {
//                     avoidance.velocity = desiredVelocity;
//                 }
//             }
//
//             // Apply minimum speed to prevent getting stuck
//             var currentSpeed = dmath.Sqrt(avoidance.velocity.x * avoidance.velocity.x +
//                                           avoidance.velocity.y * avoidance.velocity.y);
//
//             if (currentSpeed < follower.minSpeed && magnitude > dfloat.Zero)
//             {
//                 avoidance.velocity = avoidance.desiredDirection * follower.minSpeed;
//             }
//
//             // Clamp velocity to max speed
//             if (currentSpeed > follower.maxSpeed)
//             {
//                 avoidance.velocity = (avoidance.velocity / currentSpeed) * follower.maxSpeed;
//             }
//
//             // Apply movement with target overshoot prevention
//             var movementVector = avoidance.velocity * deltaTime;
//
//             // Prevent overshooting the target
//             movementVector = PreventTargetOvershoot(currentPosition, movementVector, targetId, proximityThreshold);
//             movement.Vector = movementVector;
//             
//             // Map flow field movement (x,y) to 3D world movement (x,0,z)
//             var newPosition = transform.position + new dfloat3(movementVector.x, dfloat.Zero, movementVector.y);
//
//             // Check if the new position is walkable (map 3D position to flow field coordinates for walkability check)
//             if (IsPositionWalkable(new float2((float)newPosition.x, (float)newPosition.z), follower.movementType))
//             {
//                 transform.position = newPosition;
//
//                 // Update rotation to face movement direction if moving (DETERMINISTIC)
//                 if (dmath.lengthsq(movementVector) > new dfloat(0.001f))
//                 {
//                     // Calculate Y-axis rotation angle from 2D movement vector (much safer than LookRotationSafe)
//                     // This avoids the complex matrix operations that can cause crashes
//                     var angle = dmath.atan2(movementVector.x, movementVector.y);
//
//                     // Validate the angle is finite before creating rotation
//                     if (dmath.isfinite(angle))
//                     {
//                         transform.rotation = dquaternion.RotateY(angle);
//                     }
//                 }
//             }
//             else
//             {
//                 // Try to slide along obstacles by testing partial movement
//                 var partialMovement = movementVector * new dfloat(0.5f);
//                 var partialPosition =
//                     transform.position + new dfloat3(partialMovement.x, dfloat.Zero, partialMovement.y);
//
//                 if (IsPositionWalkable(new float2((float)partialPosition.x, (float)partialPosition.z),
//                         follower.movementType))
//                 {
//                     transform.position = partialPosition;
//
//                     // Update rotation to face movement direction if moving (DETERMINISTIC)
//                     if (dmath.lengthsq(partialMovement) > new dfloat(0.001f))
//                     {
//                         // Calculate Y-axis rotation angle from 2D movement vector (much safer than LookRotationSafe)
//                         // This avoids the complex matrix operations that can cause crashes
//                         var angle = dmath.atan2(partialMovement.x, partialMovement.y);
//
//                         // Validate the angle is finite before creating rotation
//                         if (dmath.isfinite(angle))
//                         {
//                             transform.rotation = dquaternion.RotateY(angle);
//                         }
//                     }
//                 }
//                 // If even partial movement fails, don't move
//             }
//
//             avoidance.lastAvoidanceTime = currentTime;
//         }
//
//         private dfloat2 GetFlowFieldDirection(dfloat2 worldPos, int targetId, MovementType movementType)
//         {
//             // Find the flow field entity with matching targetId
//             for (int i = 0; i < flowFieldEntities.Length; i++)
//             {
//                 var entity = flowFieldEntities[i];
//                 if (!flowFieldGridLookup.HasComponent(entity)) continue;
//
//                 var gridData = flowFieldGridLookup[entity];
//                 if (gridData.targetId != targetId) continue;
//
//                 // Found matching flow field, sample it
//                 if (flowFieldLookup.HasBuffer(entity))
//                 {
//                     var buffer = flowFieldLookup[entity];
//                     return FlowFieldUtils.SampleFlowField(buffer, worldPos, gridData.worldOrigin,
//                         gridData.cellSize, gridData.gridSize, movementType);
//                 }
//             }
//
//             // No matching flow field found, return zero direction
//             return dfloat2.zero;
//         }
//
//         private dfloat GetDistanceToTarget(dfloat2 worldPos, int targetId)
//         {
//             // Find the target data with matching targetId
//             for (int i = 0; i < targetData.Length; i++)
//             {
//                 var target = targetData[i];
//                 if (!target.isValid || target.targetId != targetId) continue;
//
//                 // Found matching target, calculate distance (DETERMINISTIC)
//                 // Map 3D target position (x,y,z) to flow field coordinates (x,z)
//                 var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
//                 var diff = targetPos - worldPos;
//                 return dmath.Sqrt(diff.x * diff.x + diff.y * diff.y);
//             }
//
//             // No target found, return large distance to keep avoidance active
//             return new dfloat(1000.0f);
//         }
//
//         private dfloat2 PreventTargetOvershoot(dfloat2 currentPos, dfloat2 movement, int targetId,
//             dfloat proximityThreshold)
//         {
//             // Find the target data with matching targetId
//             for (int i = 0; i < targetData.Length; i++)
//             {
//                 var target = targetData[i];
//                 if (!target.isValid || target.targetId != targetId) continue;
//
//                 // Calculate current distance to target (DETERMINISTIC)
//                 // Map 3D target position (x,y,z) to flow field coordinates (x,z)
//                 var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
//                 var toTarget = targetPos - currentPos;
//                 var distanceToTarget = dmath.Sqrt(toTarget.x * toTarget.x + toTarget.y * toTarget.y);
//
//                 // If we're very close to the target, limit movement to prevent overshoot
//                 var movementMagnitude = dmath.Sqrt(movement.x * movement.x + movement.y * movement.y);
//
//                 // Start decelerating when within 3x the proximity threshold
//                 var decelerationDistance = proximityThreshold * new dfloat(3.0f);
//
//                 if (distanceToTarget < decelerationDistance)
//                 {
//                     // Stop completely when extremely close to target to prevent jittering
//                     var stopThreshold = proximityThreshold * new dfloat(0.05f);
//                     if (distanceToTarget < stopThreshold)
//                     {
//                         movement = dfloat2.zero;
//                     }
//                     else
//                     {
//                         // Calculate how much we can move without overshooting
//                         var maxMovementDistance = dmath.max(distanceToTarget - proximityThreshold * new dfloat(0.1f),
//                             dfloat.Zero);
//
//                         if (movementMagnitude > maxMovementDistance)
//                         {
//                             // Scale down movement to prevent overshoot
//                             if (movementMagnitude > dfloat.Zero)
//                             {
//                                 var scale = maxMovementDistance / movementMagnitude;
//                                 movement *= scale;
//                             }
//                             else
//                             {
//                                 movement = dfloat2.zero;
//                             }
//                         }
//                     }
//                 }
//
//                 break; // Found target, no need to continue
//             }
//
//             return movement;
//         }
//
//         private dfloat2 GetDirectTargetDirection(dfloat2 currentPos, int targetId, dfloat proximityThreshold)
//         {
//             // Find the target data with matching targetId
//             for (int i = 0; i < targetData.Length; i++)
//             {
//                 var target = targetData[i];
//                 if (!target.isValid || target.targetId != targetId) continue;
//
//                 // Calculate direction to target (DETERMINISTIC)
//                 // Map 3D target position (x,y,z) to flow field coordinates (x,z)
//                 var targetPos = new dfloat2(target.targetPosition.x, target.targetPosition.z);
//                 var toTarget = targetPos - currentPos;
//                 var distance = dmath.Sqrt(toTarget.x * toTarget.x + toTarget.y * toTarget.y);
//
//                 // Only provide direct direction when very close to target
//                 var directTargetRange = proximityThreshold * new dfloat(2.0f);
//                 if (distance < directTargetRange && distance > new dfloat(0.01f))
//                 {
//                     // Return normalized direction to target
//                     return toTarget / distance;
//                 }
//
//                 break; // Found target, no need to continue
//             }
//
//             return dfloat2.zero;
//         }
//
//         private dfloat2 CalculateSeparation(dfloat2 position, dfloat radius, int avoidanceLayer)
//         {
//             var separationForce = dfloat2.zero;
//             var neighborCount = 0;
//
//             for (int i = 0; i < neighborData.Length; i++)
//             {
//                 var neighbor = neighborData[i];
//                 if (!neighbor.isValid) continue;
//
//                 // Skip units on different avoidance layers
//                 if (neighbor.avoidanceLayer != avoidanceLayer) continue;
//                 
//                 var offset = position - new dfloat2(neighbor.position.x, neighbor.position.z);
//                 var distance = dmath.Sqrt(offset.x * offset.x + offset.y * offset.y);
//
//                 // Skip self and units outside radius
//                 if (distance < new dfloat(0.01f) || distance > radius) continue;
//
//                 // Calculate separation force (stronger when closer)
//                 var separationStrength = (radius - distance) / radius;
//                 var normalizedOffset = offset / distance;
//                 separationForce += normalizedOffset * separationStrength;
//                 neighborCount++;
//             }
//
//             if (neighborCount > 0)
//             {
//                 separationForce /= new dfloat(neighborCount);
//             }
//
//             return separationForce;
//         }
//
//         private dfloat2 CalculateCohesion(dfloat2 position, dfloat radius, int avoidanceLayer)
//         {
//             var centerOfMass = dfloat2.zero;
//             var neighborCount = 0;
//
//             for (int i = 0; i < neighborData.Length; i++)
//             {
//                 var neighbor = neighborData[i];
//                 if (!neighbor.isValid) continue;
//
//                 // Skip units on different avoidance layers
//                 if (neighbor.avoidanceLayer != avoidanceLayer) continue;
//
//                 // Map 3D positions (x,y,z) to flow field coordinates (x,z)
//                 var otherPos = new dfloat2(neighbor.position.x, neighbor.position.z);
//                 var offset = otherPos - position;
//                 var distance = dmath.Sqrt(offset.x * offset.x + offset.y * offset.y);
//
//                 // Skip self and units outside radius
//                 if (distance < new dfloat(0.01f) || distance > radius) continue;
//
//                 centerOfMass += otherPos;
//                 neighborCount++;
//             }
//
//             if (neighborCount > 0)
//             {
//                 centerOfMass /= new dfloat(neighborCount);
//                 // Map 3D position (x,y,z) to flow field coordinates (x,z)
//                 var cohesionDirection = centerOfMass - position;
//                 var magnitude = dmath.Sqrt(cohesionDirection.x * cohesionDirection.x +
//                                            cohesionDirection.y * cohesionDirection.y);
//
//                 if (magnitude > dfloat.Zero)
//                 {
//                     return cohesionDirection / magnitude;
//                 }
//             }
//
//             return dfloat2.zero;
//         }
//
//         private dfloat2 CalculateAlignment(dfloat2 position, dfloat radius, int avoidanceLayer)
//         {
//             var averageVelocity = dfloat2.zero;
//             var neighborCount = 0;
//
//             for (int i = 0; i < neighborData.Length; i++)
//             {
//                 var neighbor = neighborData[i];
//                 if (!neighbor.isValid) continue;
//
//                 // Skip units on different avoidance layers
//                 if (neighbor.avoidanceLayer != avoidanceLayer) continue;
//
//                 // Map 3D positions (x,y,z) to flow field coordinates (x,z)
//                 var offset = new dfloat2(neighbor.position.x, neighbor.position.z) - position;
//                 var distance = dmath.Sqrt(offset.x * offset.x + offset.y * offset.y);
//
//                 // Skip self and units outside radius
//                 if (distance < new dfloat(0.01f) || distance > radius) continue;
//
//                 averageVelocity += neighbor.velocity;
//                 neighborCount++;
//             }
//
//             if (neighborCount > 0)
//             {
//                 averageVelocity /= new dfloat(neighborCount);
//                 var magnitude =
//                     dmath.Sqrt(averageVelocity.x * averageVelocity.x + averageVelocity.y * averageVelocity.y);
//
//                 if (magnitude > dfloat.Zero)
//                 {
//                     return averageVelocity / magnitude;
//                 }
//             }
//
//             return dfloat2.zero;
//         }
//
//         private dfloat2 CalculateObstacleAvoidance(dfloat2 position, dfloat2 velocity, dfloat radius,
//             MovementType movementType)
//         {
//             var avoidanceForce = dfloat2.zero;
//
//             // Find the flow field entity to check for obstacles
//             for (int i = 0; i < flowFieldEntities.Length; i++)
//             {
//                 var entity = flowFieldEntities[i];
//                 if (!flowFieldGridLookup.HasComponent(entity)) continue;
//                 if (!flowFieldLookup.HasBuffer(entity)) continue;
//
//                 var gridData = flowFieldGridLookup[entity];
//                 var buffer = flowFieldLookup[entity];
//
//                 // Sample multiple points around the unit to detect obstacles
//                 var samplePoints = new NativeArray<dfloat2>(8, Allocator.Temp);
//                 var angleStep = new dfloat(2.0f) * dmath.PI / new dfloat(8.0f);
//
//                 for (int j = 0; j < 8; j++)
//                 {
//                     var angle = new dfloat(j) * angleStep;
//                     var sampleOffset = new dfloat2(dmath.cos(angle), dmath.sin(angle)) * radius;
//                     // Map 3D position (x,y,z) to flow field coordinates (x,z)
//                     samplePoints[j] = position + sampleOffset;
//                 }
//
//                 try
//                 {
//                     for (int j = 0; j < samplePoints.Length; j++)
//                     {
//                         var samplePos = samplePoints[j];
//                         var relativePos = samplePos - gridData.worldOrigin;
//                         var gridPos = new int2((int)(relativePos.x / gridData.cellSize.x),
//                             (int)(relativePos.y / gridData.cellSize.y));
//
//                         if (FlowFieldGridUtils.IsValidGridPosition(gridPos, gridData.gridSize))
//                         {
//                             var index = FlowFieldGridUtils.GridToIndex(gridPos, gridData.gridSize);
//                             if (index < buffer.Length)
//                             {
//                                 var cell = buffer[index].cell;
//
//                                 // If this cell is not walkable for our movement type, add avoidance force
//                                 if (!cell.IsWalkableFor(movementType))
//                                 {
//                                     var obstacleDirection = samplePos - position;
//                                     var distance = dmath.Sqrt(obstacleDirection.x * obstacleDirection.x +
//                                                               obstacleDirection.y * obstacleDirection.y);
//
//                                     if (distance > dfloat.Zero)
//                                     {
//                                         // Add force away from obstacle (stronger when closer)
//                                         var avoidanceStrength = (radius - distance) / radius;
//                                         var normalizedDirection = obstacleDirection / distance;
//                                         avoidanceForce -= normalizedDirection * avoidanceStrength;
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 finally
//                 {
//                     samplePoints.Dispose();
//                 }
//
//                 break; // Only check the first valid flow field
//             }
//
//             return avoidanceForce;
//         }
//
//         private bool IsPositionWalkable(float2 position, MovementType movementType)
//         {
//             // Check all flow fields to see if this position is walkable
//             for (int i = 0; i < flowFieldEntities.Length; i++)
//             {
//                 var entity = flowFieldEntities[i];
//                 if (!flowFieldGridLookup.HasComponent(entity)) continue;
//                 if (!flowFieldLookup.HasBuffer(entity)) continue;
//
//                 var gridData = flowFieldGridLookup[entity];
//                 var buffer = flowFieldLookup[entity];
//
//                 // Map 3D position (x,y,z) to flow field coordinates (x,z)
//                 var relativePos = new dfloat2((dfloat)position.x, (dfloat)position.y) - gridData.worldOrigin;
//                 var gridPos = new int2((int)(relativePos.x / gridData.cellSize.x),
//                     (int)(relativePos.y / gridData.cellSize.y));
//
//                 if (FlowFieldGridUtils.IsValidGridPosition(gridPos, gridData.gridSize))
//                 {
//                     var index = FlowFieldGridUtils.GridToIndex(gridPos, gridData.gridSize);
//                     if (index < buffer.Length)
//                     {
//                         var cell = buffer[index].cell;
//                         return cell.IsWalkableFor(movementType);
//                     }
//                 }
//
//                 // If position is outside grid, consider it unwalkable
//                 return false;
//             }
//
//             // If no flow field found, assume walkable (fallback)
//             return true;
//         }
//     }
// }