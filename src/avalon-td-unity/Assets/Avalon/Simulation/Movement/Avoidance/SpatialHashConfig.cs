using Unity.Deterministic.Mathematics;
using Unity.Mathematics;

namespace FlowField
{
    public struct SpatialHashConfig
    {
        public dfloat cellSize;
        public int2 gridSize;
        public dfloat2 worldOrigin;
        public dfloat2 worldSize;

        public static SpatialHashConfig Create(dfloat maxAvoidanceRadius, dfloat2 worldMin, dfloat2 worldMax)
        {
            var cellSize = maxAvoidanceRadius * new dfloat(2.0f); // Each cell is 2x the max avoidance radius
            var worldSize = worldMax - worldMin;
            var gridSize = new int2(
                (int)dmath.ceil(worldSize.x / cellSize) + 1,
                (int)dmath.ceil(worldSize.y / cellSize) + 1
            );

            return new SpatialHashConfig
            {
                cellSize = cellSize,
                gridSize = gridSize,
                worldOrigin = worldMin,
                worldSize = worldSize
            };
        }

        public int2 WorldToGrid(float3 worldPos)
        {
            // Map 3D world position (x,y,z) to spatial hash coordinates (x,z)
            var relativePos = new dfloat2((dfloat)worldPos.x, (dfloat)worldPos.z) - worldOrigin;
            return new int2(
                math.clamp((int)(relativePos.x / cellSize), 0, gridSize.x - 1),
                math.clamp((int)(relativePos.y / cellSize), 0, gridSize.y - 1)
            );
        }

        public int GridToIndex(int2 gridPos)
        {
            return gridPos.y * gridSize.x + gridPos.x;
        }
    }
}