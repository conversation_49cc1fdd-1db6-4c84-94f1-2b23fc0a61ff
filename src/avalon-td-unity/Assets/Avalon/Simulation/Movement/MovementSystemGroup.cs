﻿using FlowField;
using Unity.Entities;

namespace Avalon.Simulation.Movement
{
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(FlowFieldManagerSystem))]
    [WorldSystemFilter(WorldSystemFilterFlags.Default | WorldSystemFilterFlags.EntitySceneOptimizations)]
    public partial class MovementSystemGroup : ComponentSystemGroup
    {
        protected override void OnCreate()
        {
            base.OnCreate();

            // Use the unified movement system instead of separate systems
            var unifiedMovementSystem = World.GetOrCreateSystem<UnifiedMovementSystem>();
            var storePreviousSystem = World.GetOrCreateSystem<StorePreviousTransformSystem>();

            AddSystemToUpdateList(unifiedMovementSystem);
            AddSystemToUpdateList(storePreviousSystem);
        }
    }
}