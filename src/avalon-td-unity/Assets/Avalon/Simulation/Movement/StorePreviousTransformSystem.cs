using FlowField;
using Unity.Burst;
using Unity.Entities;

namespace Avalon.Simulation.Movement
{
    [BurstCompile]
    [UpdateInGroup(typeof(MovementSystemGroup))]
    [UpdateAfter(typeof(UnifiedMovementSystem))]
    public partial struct StorePreviousTransformSystem : ISystem
    {
        private EntityQuery fixedTimestepQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<SimulationTransform>();
            state.RequireForUpdate<PreviousSimulationTransform>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();

            // Only store previous state when we process a tick
            if (!fixedTimestep.shouldProcessTick) return;

            // Store current simulation transform as previous for next frame's interpolation
            var storeJob = new StorePreviousTransformJob();
            state.Dependency = storeJob.ScheduleParallel(state.Dependency);
        }
    }
}