using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation
{
    [BurstCompile]
    [UpdateInGroup(typeof(InitializationSystemGroup))]
    public partial struct FixedTimestepSystem : ISystem
    {
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Create the fixed timestep singleton
            var fixedTimestep = new FixedTimestep
            {
                tickRate = new dfloat(10.0d), // 10 Hz - 10 ticks per second
                tickDuration = new dfloat(0.1d), // 0.1 seconds per tick (1/10)
                accumulator = 0f,
                currentTime = dfloat.Zero,
                previousTime = dfloat.Zero,
                tickCount = 0,
                shouldProcessTick = false,
                interpolationAlpha = 0f
            };

            var entity = state.EntityManager.CreateEntity();
            state.EntityManager.AddComponentData(entity, fixedTimestep);

            // Require the fixed timestep to exist
            state.RequireForUpdate<FixedTimestep>();
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var deltaTime = SystemAPI.Time.DeltaTime;

            foreach (var fixedTimestep in SystemAPI.Query<RefRW<FixedTimestep>>())
            {
                var timestep = fixedTimestep.ValueRW;

                // Accumulate time
                timestep.accumulator += deltaTime;

                // Check if we should process a tick
                timestep.shouldProcessTick = timestep.accumulator >= (float)timestep.tickDuration;

                if (timestep.shouldProcessTick)
                {
                    // Store previous time for interpolation
                    timestep.previousTime = timestep.currentTime;

                    // Advance simulation time
                    timestep.currentTime += timestep.tickDuration;
                    timestep.tickCount++;

                    // Reduce accumulator
                    timestep.accumulator -= (float)timestep.tickDuration;

                    // Clamp accumulator to prevent spiral of death
                    if (timestep.accumulator > (float)timestep.tickDuration)
                    {
                        timestep.accumulator = (float)timestep.tickDuration;
                    }
                }

                // Calculate interpolation alpha for smooth rendering
                timestep.interpolationAlpha = timestep.accumulator / (float)timestep.tickDuration;

                fixedTimestep.ValueRW = timestep;
                break; // Only one fixed timestep singleton
            }
        }
    }
}