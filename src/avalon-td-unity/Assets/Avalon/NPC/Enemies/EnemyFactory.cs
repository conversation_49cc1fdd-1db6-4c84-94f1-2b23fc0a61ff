﻿using Avalon.Animation;
using Avalon.Simulation.Movement;
using FlowField;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Transforms;

namespace Avalon.NPC.Enemies
{
    public static class EnemyFactory
    {
        private static EntityArchetype _archetype;
        
        public static void Initialize(EntityManager entityManager)
        {
            _archetype = entityManager.CreateArchetype(
                typeof(LocalTransform),
                typeof(FlowFieldFollower),
                typeof(PreviousSimulationTransform),
                typeof(SimulationTransform),
                typeof(EnemyDefinitionRef),
                typeof(UnitStats),
                typeof(AvoidanceData),
                typeof(MovementTarget),
                typeof(Movement),
                typeof(EnemyAnimationState)
            );
        }

        public static Entity CreateUnitEntity(EntityCommandBuffer ecb, int enemyDefinitionId, dfloat3 position)
        {
            var entity = ecb.CreateEntity(_archetype);
            ecb.SetComponent(entity, new SimulationTransform
            {
                position = position,
                rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                scale = dfloat.One
            });
            ecb.SetComponent(entity, new PreviousSimulationTransform
            {
                Position = position,
                Rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                Scale = dfloat.One
            });
            
            var enemyDefinition = EnemyDefinition.Database.GetById(enemyDefinitionId);
            
            var unitType = new EnemyDefinitionRef
            {
                EnemyDefinitionId = enemyDefinition.Id
            };
            ecb.SetComponent(entity, unitType);

            var unitStats = enemyDefinition.ToUnitStats();
            ecb.SetComponent(entity, unitStats);
            
            var flowFieldFollower = enemyDefinition.ToFlowFieldFollower();
            ecb.SetComponent(entity, flowFieldFollower);

            var avoidanceData = enemyDefinition.ToAvoidanceData();
            if (avoidanceData.HasValue)
            {
                ecb.SetComponent(entity, avoidanceData.Value);
            }
            
            return entity;
        }
    }
}