﻿using System.Diagnostics;
using Avalon.Visualization;
using FlowField;
using FlowField.Core;
using Unity.Collections;
using Unity.Entities;
using UnityEngine;

namespace Avalon.NPC.Enemies
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    public partial struct EnemySpawnerSystem : ISystem
    {
        public void OnUpdate(ref SystemState state)
        {
            var ecb = new EntityCommandBuffer(Allocator.TempJob);

            foreach (var (spawnRequest, entity) in
                     SystemAPI.Query<RefRO<EnemySpawnRequest>>().WithEntityAccess())
            {
                var unitEntity = EnemyFactory.CreateUnitEntity(
                    ecb, 
                    spawnRequest.ValueRO.EnemyDefinitionId,
                    spawnRequest.ValueRO.Position
                );
                
                ecb.SetComponent(unitEntity, new MovementTarget
                {
                    targetId = spawnRequest.ValueRO.TargetId
                });
                
                CreateGameObject(ecb, unitEntity, spawnRequest.ValueRO.EnemyDefinitionId);
                
                ecb.DestroyEntity(entity);
            }
            
            ecb.Playback(state.EntityManager);
            ecb.Dispose();
        }

        [Conditional("UNITY_6000_0_OR_NEWER")]
        private void CreateGameObject(EntityCommandBuffer ecb, Entity entity, int unitTypeDefinitionId)
        {
            var unitTypeDefinition = EnemyDefinition.Database.GetById(unitTypeDefinitionId);
            var gameObject = Object.Instantiate(unitTypeDefinition.Prefab);
            gameObject.name = $"{unitTypeDefinition.Name}_{entity.Index}";
                
            var gameObjectId = gameObject.GetInstanceID();
            GameObjectPool.Instance.Add(gameObjectId, gameObject);
            ecb.AddComponent(entity, new GameObjectRef
            {
                gameObjectId = gameObjectId
            });
        }
    }
}