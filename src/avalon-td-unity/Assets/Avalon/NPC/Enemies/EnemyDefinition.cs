using Avalon.Core;
using Avalon.Simulation.Movement;
using Avalon.Startup;
using FlowField;
using Sirenix.OdinInspector;
using Sirenix.Serialization;
using Unity.Deterministic.Mathematics;
using UnityEngine;

namespace Avalon.NPC.Enemies
{
    /// <summary>
    /// ScriptableObject that defines a unit type with its stats and visual representation
    /// </summary>
    [CreateAssetMenu(fileName = "New Enemy", menuName = "Avalon/Definitions/Create Enemy")]
    public class EnemyDefinition : IdentifiableScriptableObject<EnemyDefinition>
    {
        public static ScriptableObjectDatabase<EnemyDefinition> Database => ScriptableObjectLoader<EnemyDefinition>.Database;

        [TitleGroup("Movement")]
        public dfloat MinSpeed;
        public dfloat MaxSpeed;
        
        [TitleGroup("Base Defense Stats")]
        public dfloat MaxHealth;
        
        [ShowInInspector]
        public dfloat3 VisualOffset { get; set; }
    
        [Header("Movement Stats")]
        [Range(0.1f, 20.0f)]
        public float maxSpeed = 5.0f;
    
        [Range(0.1f, 2.0f)]
        public float minSpeed = 0.5f;
    
        [Range(1.0f, 50.0f)]
        public float acceleration = 10.0f;
    
        [Range(1.0f, 50.0f)]
        public float deceleration = 8.0f;
    
        [Range(0.1f, 10.0f)]
        public float rotationSpeed = 5.0f;
    
        [Header("Physical Properties")]
        [Range(0.1f, 5.0f)]
        public float radius = 0.5f;
    
        [Range(0.1f, 10.0f)]
        public float mass = 1.0f;
    
        [Range(0.1f, 5.0f)]
        public float height = 1.0f;
    
        [Header("Combat Stats")]
        [Range(1.0f, 1000.0f)]
        public float maxHealth = 100.0f;
    
        [Range(1.0f, 100.0f)]
        public float attackDamage = 10.0f;
    
        [Range(0.1f, 10.0f)]
        public float attackRange = 1.5f;
    
        [Range(0.1f, 5.0f)]
        public float attackCooldown = 1.0f;
    
        [Header("Avoidance Settings")]
        public bool useAvoidance = true;
    
        [Range(0.5f, 5.0f)]
        public float avoidanceRadius = 1.5f;
    
        [Range(0.1f, 5.0f)]
        public float separationStrength = 2.0f;
    
        [Range(0.0f, 2.0f)]
        public float cohesionStrength = 0.5f;
    
        [Range(0.0f, 2.0f)]
        public float alignmentStrength = 0.3f;
    
        [Range(0.1f, 10.0f)]
        public float obstacleAvoidanceStrength = 3.0f;
    
        [Range(0.1f, 5.0f)]
        public float targetProximityThreshold = 2.0f;
    
        [Header("Visual Settings")]
        [Range(0.1f, 3.0f)]
        public float visualScale = 1.0f;
    
        public Vector3 visualOffset = Vector3.zero;
    
        [Header("Special Abilities")]
        public bool canFly = false;
        public bool canSwim = false;
        public bool isHeavy = false;
        public bool hasSpecialAbility1 = false;
        public bool hasSpecialAbility2 = false;
        public bool hasSpecialAbility3 = false;
    
        /// <summary>
        /// Convert this definition to UnitStats component
        /// </summary>
        public UnitStats ToUnitStats()
        {
            return new UnitStats
            {
                // Movement stats
                maxSpeed = new dfloat(maxSpeed),
                acceleration = new dfloat(acceleration),
                deceleration = new dfloat(deceleration),
                rotationSpeed = new dfloat(rotationSpeed),
            
                // Combat stats
                maxHealth = new dfloat(maxHealth),
                currentHealth = new dfloat(maxHealth), // Start at full health
                attackDamage = new dfloat(attackDamage),
                attackRange = new dfloat(attackRange),
                attackCooldown = new dfloat(attackCooldown),
            
                // Physical properties
                radius = new dfloat(radius),
                mass = new dfloat(mass),
                height = new dfloat(height),
            
                // Special abilities flags
                abilityFlags = (byte)(
                    (canFly ? 1 : 0) |
                    (canSwim ? 2 : 0) |
                    (isHeavy ? 4 : 0) |
                    (hasSpecialAbility1 ? 8 : 0) |
                    (hasSpecialAbility2 ? 16 : 0) |
                    (hasSpecialAbility3 ? 32 : 0)
                )
            };
        }
    
        /// <summary>
        /// Convert this definition to FlowFieldFollower component
        /// </summary>
        public FlowFieldFollower ToFlowFieldFollower()
        {
            return new FlowFieldFollower
            {
                // Movement stats
                maxSpeed = new dfloat(maxSpeed),
                minSpeed = new dfloat(minSpeed),
                acceleration = new dfloat(acceleration),
                deceleration = new dfloat(deceleration),
                flowFieldStrength = new dfloat(1.0f),
            
                // Avoidance parameters
                avoidanceRadius = new dfloat(avoidanceRadius),
                separationStrength = new dfloat(separationStrength),
                cohesionStrength = new dfloat(cohesionStrength),
                alignmentStrength = new dfloat(alignmentStrength),
                obstacleAvoidanceStrength = new dfloat(obstacleAvoidanceStrength),
            
                // Target and movement settings
                avoidanceLayer = 0,
                movementType = GetMovementType(),
                useAvoidance = useAvoidance,
                targetProximityThreshold = new dfloat(targetProximityThreshold)
            };
        }
    
        /// <summary>
        /// Get the movement type based on special abilities
        /// </summary>
        public MovementType GetMovementType()
        {
            if (canFly) return MovementType.Flying;
            if (canSwim) return MovementType.Amphibious;
            if (isHeavy) return MovementType.Heavy;
            return MovementType.Ground;
        }
    
        /// <summary>
        /// Create AvoidanceData component if this unit uses avoidance
        /// </summary>
        public AvoidanceData? ToAvoidanceData()
        {
            if (!useAvoidance) return null;
        
            return new AvoidanceData
            {
                velocity = new dfloat2(dfloat.Zero, dfloat.Zero),
                desiredDirection = new dfloat2(dfloat.Zero, dfloat.Zero),
                avoidanceForce = new dfloat2(dfloat.Zero, dfloat.Zero),
                radius = new dfloat(radius),
                lastAvoidanceTime = new dfloat(0)
            };
        }
    }
}
