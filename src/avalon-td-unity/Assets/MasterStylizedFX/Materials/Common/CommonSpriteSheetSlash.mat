%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: CommonSpriteSheetSlash
  m_Shader: {fileID: 4800000, guid: dd5eb991161d50b4d9452916f712ba14, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ALPHABLEND_ON
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Base:
        m_Texture: {fileID: 2800000, guid: 914383562fa61074293a6a0851ae016a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Dissolvenoise:
        m_Texture: {fileID: 2800000, guid: ddb7a48c331a2e54c880f46455ec878f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 4cdecdfbc6f2fa34689b6691b5d55879, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 2800000, guid: 4e09b5b2b10fa1d4e9d91646a223ccc1, type: 3}
        m_Scale: {x: 1.15, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureNoise:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: d2bebb4f23170554295c5d546295ee3e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _BlendOp: 0
    - _BumpScale: 1
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _CenterSaturation: 15.04
    - _ColorMode: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _DstBlend: 10
    - _EmissionEnabled: 0
    - _FlipbookMode: 0
    - _Float0: 0.26
    - _FresnelPower: 0
    - _FresnelScale: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _HighLightCutter: 0.31
    - _InvFade: 1
    - _LightingEnabled: 0
    - _Metallic: 0
    - _Mode: 2
    - _Noise1Strength: 0
    - _Noise2Strength: 0
    - _NoiseCutter: 0.26
    - _NoiseStep: 0.35
    - _NoiseStrength: 0.88
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 5
    - _Stepper: -0.21
    - _UVSec: 0
    - _Usedepth: 0
    - _Usetexturecolor: 0
    - _Usetexturedissolve: 0
    - _WhiteIntensity: 7.9
    - _ZWrite: 0
    - __dirty: 0
    m_Colors:
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 3.491178, g: 3.491178, b: 3.491178, a: 1}
    - _Color0: {r: 2.828427, g: 2.828427, b: 2.828427, a: 0}
    - _ColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _Dissolvecolor: {r: 1, g: 1, b: 1, a: 1}
    - _DissolvespeedXY: {r: 2, g: 2, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Maincolor: {r: 0.7609469, g: 0.8547776, b: 0.9433962, a: 1}
    - _MaskOffset: {r: -0.1, g: -0.5, b: 0, a: 0}
    - _MaskScroll: {r: 0.81, g: 0, b: 0, a: 0}
    - _MaskTiling: {r: 0.57, g: 1.82, b: 0, a: 0}
    - _Noise1Movement: {r: 0, g: 0, b: 0, a: 0}
    - _Noise1Tiling: {r: 0, g: 0, b: 0, a: 0}
    - _Noise2Movement: {r: 0, g: 0, b: 0, a: 0}
    - _Noise2Tiling: {r: 0, g: 0, b: 0, a: 0}
    - _Noisecolor: {r: 0.2470588, g: 0.3012382, b: 0.3607843, a: 1}
    - _NoisespeedXYEmissonZPowerW: {r: 0.5, g: 0, b: 2, a: 1}
    - _ScrollSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
