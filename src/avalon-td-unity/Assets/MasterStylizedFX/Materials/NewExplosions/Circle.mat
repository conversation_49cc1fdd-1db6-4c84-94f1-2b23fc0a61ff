%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Circle
  m_Shader: {fileID: 4800000, guid: b156fd4c207138c4cb506de2c012a55f, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ALPHABLEND_ON
  - _LIMITUV_ON
  - _LOOPUV_ON
  - _NOLIMITUV_ON
  - _STRETCHMULTIPLIER_ON
  - _STRETCHX_ON
  - _STRETCH_ON
  - _TOGGLESWITCH0_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Base:
        m_Texture: {fileID: 2800000, guid: 4c46ee3bed9556e4c9482fc515243f81, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: f85b5f409e299864087ef26111ed5f14, type: 3}
        m_Scale: {x: 2, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Main:
        m_Texture: {fileID: 2800000, guid: e94bba4d835e0f84eaac5f3db648b2d9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: e94bba4d835e0f84eaac5f3db648b2d9, type: 3}
        m_Scale: {x: 2, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTexture:
        m_Texture: {fileID: 2800000, guid: e94bba4d835e0f84eaac5f3db648b2d9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 2800000, guid: f980f1e947232534a98289ee95bfb5ee, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMask:
        m_Texture: {fileID: 2800000, guid: 4e09b5b2b10fa1d4e9d91646a223ccc1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _StaticMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: e94bba4d835e0f84eaac5f3db648b2d9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 6db8fe8c68b8ad6498a4d9d914668b79, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _BlendOp: 0
    - _BumpScale: 1
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _ColorMode: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _DstBlend: 10
    - _EdgeSharpness: 0.207
    - _EmissionEnabled: 0
    - _Feather: 0
    - _FlipbookMode: 0
    - _Float2: 0
    - _FresnelPower: 0
    - _FresnelScale: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _LightingEnabled: 0
    - _LimitUV: 1
    - _LoopMain: 0
    - _LoopUV: 1
    - _Mask: 0
    - _MaskOffset: 0
    - _Metallic: 0
    - _Mode: 2
    - _NoLimitUV: 1
    - _NoiseStep: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ScaleUV: -0.55
    - _ScaleUVTarget: 1.75
    - _Sharpness: 0
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 5
    - _Stretch: 1
    - _StretchMultiplier: 1
    - _StretchUVStrength: 1
    - _StretchUVTarget: 1.75
    - _StretchX: 1
    - _ToggleSwitch0: 1
    - _UVSec: 0
    - _ZWrite: 0
    - __dirty: 0
    m_Colors:
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 2, g: 2, b: 2, a: 1}
    - _ColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _LimitUV: {r: 0, g: 1, b: 0, a: 1}
    - _LimitUVRange: {r: 0, g: 2, b: 0, a: 1}
    - _LimitXY: {r: 0, g: 1, b: 0, a: 1}
    - _Limitation: {r: 0, g: 1, b: 0, a: 1}
    - _MainOffset: {r: 0, g: 0, b: 0, a: 0}
    - _MainScroll: {r: 0, g: 1, b: 0, a: 0}
    - _MainTiling: {r: 2, g: 1, b: 0, a: 0}
    - _MaskOffset: {r: 0, g: 0, b: 0, a: 0}
    - _MaskScale: {r: 1, g: 1, b: 0, a: 0}
    - _MaskScroll: {r: 0, g: 1, b: 0, a: 0}
    - _MaskTiling: {r: 1, g: 1, b: 0, a: 0}
    - _NoiseScroll: {r: 0, g: 0, b: 0, a: 0}
    - _Offset: {r: 0, g: 0.57, b: 0, a: 0}
    - _Scroll: {r: 0, g: -1.62, b: 0, a: 0}
    - _ScrollMultiplier: {r: 0, g: -1, b: 0, a: 0}
    - _SmoothStep: {r: 0, g: 1, b: 0, a: 0}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _StretchMultiplier: {r: 0, g: -1.39, b: 0, a: 0}
    - _StretchUVDes: {r: 0, g: 1, b: 0, a: 0}
    - _TestOffset: {r: 0.08, g: 0, b: 0, a: 0}
    - _Tiling: {r: 2, g: 1, b: 0, a: 0}
    - _UVRange: {r: 0, g: 2, b: -1, a: 0}
    - _Vector0: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
