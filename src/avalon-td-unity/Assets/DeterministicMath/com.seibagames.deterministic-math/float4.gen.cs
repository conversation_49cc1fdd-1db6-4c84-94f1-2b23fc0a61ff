//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. To update the generation of this file, modify and re-run Unity.Mathematics.CodeGen.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using System.Diagnostics;
using Sirenix.OdinInspector;
using Unity.IL2CPP.CompilerServices;
using Unity.Mathematics;
using static Unity.Mathematics.math;
using static Unity.Deterministic.Mathematics.dmath;

#pragma warning disable 0660, 0661

namespace Unity.Deterministic.Mathematics
{
    /// <summary>A 4 component vector of dfloats.</summary>
    [DebuggerTypeProxy(typeof(dfloat4.DebuggerProxy))]
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    [InlineProperty(LabelWidth = 13)]
    public partial struct dfloat4 : System.IEquatable<dfloat4>, IFormattable
    {
        /// <summary>x component of the vector.</summary>
        [HorizontalGroup]
        public dfloat x;
        /// <summary>y component of the vector.</summary>
        [HorizontalGroup]
        public dfloat y;
        /// <summary>z component of the vector.</summary>
        [HorizontalGroup]
        public dfloat z;
        /// <summary>w component of the vector.</summary>
        [HorizontalGroup]
        public dfloat w;

        /// <summary>dfloat4 zero value.</summary>
        public static readonly dfloat4 zero;

        /// <summary>Constructs a dfloat4 vector from four dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat x, dfloat y, dfloat z, dfloat w)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = w;
        }

        /// <summary>Constructs a dfloat4 vector from two dfloat values and a dfloat2 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="zw">The constructed vector's zw components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat x, dfloat y, dfloat2 zw)
        {
            this.x = x;
            this.y = y;
            this.z = zw.x;
            this.w = zw.y;
        }

        /// <summary>Constructs a dfloat4 vector from a dfloat value, a dfloat2 vector and a dfloat value.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yz">The constructed vector's yz components will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat x, dfloat2 yz, dfloat w)
        {
            this.x = x;
            this.y = yz.x;
            this.z = yz.y;
            this.w = w;
        }

        /// <summary>Constructs a dfloat4 vector from a dfloat value and a dfloat3 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yzw">The constructed vector's yzw components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat x, dfloat3 yzw)
        {
            this.x = x;
            this.y = yzw.x;
            this.z = yzw.y;
            this.w = yzw.z;
        }

        /// <summary>Constructs a dfloat4 vector from a dfloat2 vector and two dfloat values.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat2 xy, dfloat z, dfloat w)
        {
            this.x = xy.x;
            this.y = xy.y;
            this.z = z;
            this.w = w;
        }

        /// <summary>Constructs a dfloat4 vector from two dfloat2 vectors.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="zw">The constructed vector's zw components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat2 xy, dfloat2 zw)
        {
            this.x = xy.x;
            this.y = xy.y;
            this.z = zw.x;
            this.w = zw.y;
        }

        /// <summary>Constructs a dfloat4 vector from a dfloat3 vector and a dfloat value.</summary>
        /// <param name="xyz">The constructed vector's xyz components will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat3 xyz, dfloat w)
        {
            this.x = xyz.x;
            this.y = xyz.y;
            this.z = xyz.z;
            this.w = w;
        }

        /// <summary>Constructs a dfloat4 vector from a dfloat4 vector.</summary>
        /// <param name="xyzw">The constructed vector's xyzw components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat4 xyzw)
        {
            this.x = xyzw.x;
            this.y = xyzw.y;
            this.z = xyzw.z;
            this.w = xyzw.w;
        }

        /// <summary>Constructs a dfloat4 vector from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(dfloat v)
        {
            this.x = v;
            this.y = v;
            this.z = v;
            this.w = v;
        }

        /// <summary>Constructs a dfloat4 vector from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(bool v)
        {
            this.x = v ? dfloat.One : dfloat.Zero;
            this.y = v ? dfloat.One : dfloat.Zero;
            this.z = v ? dfloat.One : dfloat.Zero;
            this.w = v ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat4 vector from a bool4 vector by componentwise conversion.</summary>
        /// <param name="v">bool4 to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(bool4 v)
        {
            this.x = v.x ? dfloat.One : dfloat.Zero;
            this.y = v.y ? dfloat.One : dfloat.Zero;
            this.z = v.z ? dfloat.One : dfloat.Zero;
            this.w = v.w ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat4 vector from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(int v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
            this.z = (dfloat) v;
            this.w = (dfloat) v;
        }

        /// <summary>Constructs a dfloat4 vector from a int4 vector by componentwise conversion.</summary>
        /// <param name="v">int4 to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(int4 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
            this.z = (dfloat) v.z;
            this.w = (dfloat) v.w;
        }

        /// <summary>Constructs a dfloat4 vector from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(uint v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
            this.z = (dfloat) v;
            this.w = (dfloat) v;
        }

        /// <summary>Constructs a dfloat4 vector from a uint4 vector by componentwise conversion.</summary>
        /// <param name="v">uint4 to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(uint4 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
            this.z = (dfloat) v.z;
            this.w = (dfloat) v.w;
        }

        /// <summary>Constructs a dfloat4 vector from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(double v)
        {
            this.x = (dfloat)v;
            this.y = (dfloat)v;
            this.z = (dfloat)v;
            this.w = (dfloat)v;
        }

        /// <summary>Constructs a dfloat4 vector from a double4 vector by componentwise conversion.</summary>
        /// <param name="v">double4 to convert to dfloat4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat4(double4 v)
        {
            this.x = (dfloat)v.x;
            this.y = (dfloat)v.y;
            this.z = (dfloat)v.z;
            this.w = (dfloat)v.w;
        }


        /// <summary>Implicitly converts a single dfloat value to a dfloat4 vector by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat4(dfloat v) { return new dfloat4(v); }

        /// <summary>Explicitly converts a single bool value to a dfloat4 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat4(bool v) { return new dfloat4(v); }

        /// <summary>Explicitly converts a bool4 vector to a dfloat4 vector by componentwise conversion.</summary>
        /// <param name="v">bool4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat4(bool4 v) { return new dfloat4(v); }

        /// <summary>Implicitly converts a single int value to a dfloat4 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat4(int v) { return new dfloat4(v); }

        /// <summary>Implicitly converts a int4 vector to a dfloat4 vector by componentwise conversion.</summary>
        /// <param name="v">int4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat4(int4 v) { return new dfloat4(v); }

        /// <summary>Implicitly converts a single uint value to a dfloat4 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat4(uint v) { return new dfloat4(v); }

        /// <summary>Implicitly converts a uint4 vector to a dfloat4 vector by componentwise conversion.</summary>
        /// <param name="v">uint4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat4(uint4 v) { return new dfloat4(v); }

        /// <summary>Explicitly converts a single double value to a dfloat4 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat4(double v) { return new dfloat4(v); }

        /// <summary>Explicitly converts a double4 vector to a dfloat4 vector by componentwise conversion.</summary>
        /// <param name="v">double4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat4(double4 v) { return new dfloat4(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator * (dfloat4 lhs, dfloat4 rhs) { return new dfloat4 (lhs.x * rhs.x, lhs.y * rhs.y, lhs.z * rhs.z, lhs.w * rhs.w); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise multiplication.</param>
        /// <returns>dfloat4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator * (dfloat4 lhs, dfloat rhs) { return new dfloat4 (lhs.x * rhs, lhs.y * rhs, lhs.z * rhs, lhs.w * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator * (dfloat lhs, dfloat4 rhs) { return new dfloat4 (lhs * rhs.x, lhs * rhs.y, lhs * rhs.z, lhs * rhs.w); }


        /// <summary>Returns the result of a componentwise addition operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise addition.</param>
        /// <returns>dfloat4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator + (dfloat4 lhs, dfloat4 rhs) { return new dfloat4 (lhs.x + rhs.x, lhs.y + rhs.y, lhs.z + rhs.z, lhs.w + rhs.w); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise addition.</param>
        /// <returns>dfloat4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator + (dfloat4 lhs, dfloat rhs) { return new dfloat4 (lhs.x + rhs, lhs.y + rhs, lhs.z + rhs, lhs.w + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise addition.</param>
        /// <returns>dfloat4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator + (dfloat lhs, dfloat4 rhs) { return new dfloat4 (lhs + rhs.x, lhs + rhs.y, lhs + rhs.z, lhs + rhs.w); }


        /// <summary>Returns the result of a componentwise subtraction operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator - (dfloat4 lhs, dfloat4 rhs) { return new dfloat4 (lhs.x - rhs.x, lhs.y - rhs.y, lhs.z - rhs.z, lhs.w - rhs.w); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise subtraction.</param>
        /// <returns>dfloat4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator - (dfloat4 lhs, dfloat rhs) { return new dfloat4 (lhs.x - rhs, lhs.y - rhs, lhs.z - rhs, lhs.w - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator - (dfloat lhs, dfloat4 rhs) { return new dfloat4 (lhs - rhs.x, lhs - rhs.y, lhs - rhs.z, lhs - rhs.w); }


        /// <summary>Returns the result of a componentwise division operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise division.</param>
        /// <returns>dfloat4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator / (dfloat4 lhs, dfloat4 rhs) { return new dfloat4 (lhs.x / rhs.x, lhs.y / rhs.y, lhs.z / rhs.z, lhs.w / rhs.w); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise division.</param>
        /// <returns>dfloat4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator / (dfloat4 lhs, dfloat rhs) { return new dfloat4 (lhs.x / rhs, lhs.y / rhs, lhs.z / rhs, lhs.w / rhs); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise division.</param>
        /// <returns>dfloat4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator / (dfloat lhs, dfloat4 rhs) { return new dfloat4 (lhs / rhs.x, lhs / rhs.y, lhs / rhs.z, lhs / rhs.w); }


        /// <summary>Returns the result of a componentwise modulus operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise modulus.</param>
        /// <returns>dfloat4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator % (dfloat4 lhs, dfloat4 rhs) { return new dfloat4 (lhs.x % rhs.x, lhs.y % rhs.y, lhs.z % rhs.z, lhs.w % rhs.w); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise modulus.</param>
        /// <returns>dfloat4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator % (dfloat4 lhs, dfloat rhs) { return new dfloat4 (lhs.x % rhs, lhs.y % rhs, lhs.z % rhs, lhs.w % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise modulus.</param>
        /// <returns>dfloat4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator % (dfloat lhs, dfloat4 rhs) { return new dfloat4 (lhs % rhs.x, lhs % rhs.y, lhs % rhs.z, lhs % rhs.w); }


        /// <summary>Returns the result of a componentwise increment operation on a dfloat4 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>dfloat4 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator ++ (dfloat4 val) { return new dfloat4 (++val.x, ++val.y, ++val.z, ++val.w); }


        /// <summary>Returns the result of a componentwise decrement operation on a dfloat4 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>dfloat4 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator -- (dfloat4 val) { return new dfloat4 (--val.x, --val.y, --val.z, --val.w); }


        /// <summary>Returns the result of a componentwise less than operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise less than.</param>
        /// <returns>bool4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator < (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x < rhs.x, lhs.y < rhs.y, lhs.z < rhs.z, lhs.w < rhs.w); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less than.</param>
        /// <returns>bool4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator < (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x < rhs, lhs.y < rhs, lhs.z < rhs, lhs.w < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise less than.</param>
        /// <returns>bool4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator < (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs < rhs.x, lhs < rhs.y, lhs < rhs.z, lhs < rhs.w); }


        /// <summary>Returns the result of a componentwise less or equal operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise less or equal.</param>
        /// <returns>bool4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator <= (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x <= rhs.x, lhs.y <= rhs.y, lhs.z <= rhs.z, lhs.w <= rhs.w); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less or equal.</param>
        /// <returns>bool4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator <= (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x <= rhs, lhs.y <= rhs, lhs.z <= rhs, lhs.w <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise less or equal.</param>
        /// <returns>bool4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator <= (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs <= rhs.x, lhs <= rhs.y, lhs <= rhs.z, lhs <= rhs.w); }


        /// <summary>Returns the result of a componentwise greater than operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise greater than.</param>
        /// <returns>bool4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator > (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x > rhs.x, lhs.y > rhs.y, lhs.z > rhs.z, lhs.w > rhs.w); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater than.</param>
        /// <returns>bool4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator > (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x > rhs, lhs.y > rhs, lhs.z > rhs, lhs.w > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise greater than.</param>
        /// <returns>bool4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator > (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs > rhs.x, lhs > rhs.y, lhs > rhs.z, lhs > rhs.w); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator >= (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x >= rhs.x, lhs.y >= rhs.y, lhs.z >= rhs.z, lhs.w >= rhs.w); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <returns>bool4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator >= (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x >= rhs, lhs.y >= rhs, lhs.z >= rhs, lhs.w >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator >= (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs >= rhs.x, lhs >= rhs.y, lhs >= rhs.z, lhs >= rhs.w); }


        /// <summary>Returns the result of a componentwise unary minus operation on a dfloat4 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>dfloat4 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator - (dfloat4 val) { return new dfloat4 (-val.x, -val.y, -val.z, -val.w); }


        /// <summary>Returns the result of a componentwise unary plus operation on a dfloat4 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>dfloat4 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 operator + (dfloat4 val) { return new dfloat4 (+val.x, +val.y, +val.z, +val.w); }


        /// <summary>Returns the result of a componentwise equality operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise equality.</param>
        /// <returns>bool4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator == (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x == rhs.x, lhs.y == rhs.y, lhs.z == rhs.z, lhs.w == rhs.w); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise equality.</param>
        /// <returns>bool4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator == (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x == rhs, lhs.y == rhs, lhs.z == rhs, lhs.w == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise equality.</param>
        /// <returns>bool4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator == (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs == rhs.x, lhs == rhs.y, lhs == rhs.z, lhs == rhs.w); }


        /// <summary>Returns the result of a componentwise not equal operation on two dfloat4 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise not equal.</param>
        /// <returns>bool4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator != (dfloat4 lhs, dfloat4 rhs) { return new bool4 (lhs.x != rhs.x, lhs.y != rhs.y, lhs.z != rhs.z, lhs.w != rhs.w); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat4 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise not equal.</param>
        /// <returns>bool4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator != (dfloat4 lhs, dfloat rhs) { return new bool4 (lhs.x != rhs, lhs.y != rhs, lhs.z != rhs, lhs.w != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat value and a dfloat4 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat4 to use to compute componentwise not equal.</param>
        /// <returns>bool4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 operator != (dfloat lhs, dfloat4 rhs) { return new bool4 (lhs != rhs.x, lhs != rhs.y, lhs != rhs.z, lhs != rhs.w); }




        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; z = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xywx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xywy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xywz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, w, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; w = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; y = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, w, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; w = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; w = value.y; y = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; w = value.y; z = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xwww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, w, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; z = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, w, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; w = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yywx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yywy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yywz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; x = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, w, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; w = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; w = value.y; x = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; w = value.y; z = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 ywww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, w, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; y = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, w, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; w = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; x = value.z; w = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zywx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, w, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; w = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zywy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zywz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; w = value.y; x = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; w = value.y; y = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zwww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, w, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; x = value.y; y = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; x = value.y; z = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wxww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, x, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; y = value.y; x = value.z; z = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; y = value.y; z = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wywx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wywy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wywz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wyww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, y, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; z = value.y; x = value.z; y = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; z = value.y; y = value.z; x = value.w; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wzww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, z, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 wwww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(w, w, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, w, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; w = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, w, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; w = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 ywx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, w, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; w = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 ywy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 ywz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, w, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; w = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; w = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, w, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; w = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, w, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; w = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; x = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; x = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wxw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, x, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; y = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; y = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wyw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, y, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; z = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; z = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wzw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, z, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wwx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, w, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wwy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, w, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 wwz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, w, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 www
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(w, w, w); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; w = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; w = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zw
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, w); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; w = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 wx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(w, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 wy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(w, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 wz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(w, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { w = value.x; z = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 ww
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(w, w); }
        }



        /// <summary>Returns the dfloat element at a specified index.</summary>
        unsafe public dfloat this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 4)
                    throw new System.ArgumentException("index must be between[0...3]");
#endif
                fixed (dfloat4* array = &this) { return ((dfloat*)array)[index]; }
            }
            set
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 4)
                    throw new System.ArgumentException("index must be between[0...3]");
#endif
                fixed (dfloat* array = &x) { array[index] = value; }
            }
        }

        /// <summary>Returns true if the dfloat4 is equal to a given dfloat4, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(dfloat4 rhs) { return x == rhs.x && y == rhs.y && z == rhs.z && w == rhs.w; }

        /// <summary>Returns true if the dfloat4 is equal to a given dfloat4, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is dfloat4 converted && Equals(converted); }


        /// <summary>Returns a hash code for the dfloat4.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)dmath.hash(this); }


        /// <summary>Returns a string representation of the dfloat4.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("dfloat4({0}f, {1}f, {2}f, {3}f)", x, y, z, w);
        }

        /// <summary>Returns a string representation of the dfloat4 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("dfloat4({0}f, {1}f, {2}f, {3}f)", x.ToString(format, formatProvider), y.ToString(format, formatProvider), z.ToString(format, formatProvider), w.ToString(format, formatProvider));
        }

        internal sealed class DebuggerProxy
        {
            public dfloat x;
            public dfloat y;
            public dfloat z;
            public dfloat w;
            public DebuggerProxy(dfloat4 v)
            {
                x = v.x;
                y = v.y;
                z = v.z;
                w = v.w;
            }
        }

    }

    public static partial class dmath
    {
        /// <summary>Returns a dfloat4 vector constructed from four dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat x, dfloat y, dfloat z, dfloat w) { return new dfloat4(x, y, z, w); }

        /// <summary>Returns a dfloat4 vector constructed from two dfloat values and a dfloat2 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="zw">The constructed vector's zw components will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat x, dfloat y, dfloat2 zw) { return new dfloat4(x, y, zw); }

        /// <summary>Returns a dfloat4 vector constructed from a dfloat value, a dfloat2 vector and a dfloat value.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yz">The constructed vector's yz components will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat x, dfloat2 yz, dfloat w) { return new dfloat4(x, yz, w); }

        /// <summary>Returns a dfloat4 vector constructed from a dfloat value and a dfloat3 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yzw">The constructed vector's yzw components will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat x, dfloat3 yzw) { return new dfloat4(x, yzw); }

        /// <summary>Returns a dfloat4 vector constructed from a dfloat2 vector and two dfloat values.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat2 xy, dfloat z, dfloat w) { return new dfloat4(xy, z, w); }

        /// <summary>Returns a dfloat4 vector constructed from two dfloat2 vectors.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="zw">The constructed vector's zw components will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat2 xy, dfloat2 zw) { return new dfloat4(xy, zw); }

        /// <summary>Returns a dfloat4 vector constructed from a dfloat3 vector and a dfloat value.</summary>
        /// <param name="xyz">The constructed vector's xyz components will be set to this value.</param>
        /// <param name="w">The constructed vector's w component will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat3 xyz, dfloat w) { return new dfloat4(xyz, w); }

        /// <summary>Returns a dfloat4 vector constructed from a dfloat4 vector.</summary>
        /// <param name="xyzw">The constructed vector's xyzw components will be set to this value.</param>
        /// <returns>dfloat4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat4 xyzw) { return new dfloat4(xyzw); }

        /// <summary>Returns a dfloat4 vector constructed from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(dfloat v) { return new dfloat4(v); }

        /// <summary>Returns a dfloat4 vector constructed from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(bool v) { return new dfloat4(v); }

        /// <summary>Return a dfloat4 vector constructed from a bool4 vector by componentwise conversion.</summary>
        /// <param name="v">bool4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(bool4 v) { return new dfloat4(v); }

        /// <summary>Returns a dfloat4 vector constructed from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(int v) { return new dfloat4(v); }

        /// <summary>Return a dfloat4 vector constructed from a int4 vector by componentwise conversion.</summary>
        /// <param name="v">int4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(int4 v) { return new dfloat4(v); }

        /// <summary>Returns a dfloat4 vector constructed from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(uint v) { return new dfloat4(v); }

        /// <summary>Return a dfloat4 vector constructed from a uint4 vector by componentwise conversion.</summary>
        /// <param name="v">uint4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(uint4 v) { return new dfloat4(v); }

        /// <summary>Returns a dfloat4 vector constructed from a single half value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">half to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(half v) { return new dfloat4(v); }

        /// <summary>Return a dfloat4 vector constructed from a half4 vector by componentwise conversion.</summary>
        /// <param name="v">half4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(half4 v) { return new dfloat4(v); }

        /// <summary>Returns a dfloat4 vector constructed from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(double v) { return new dfloat4(v); }

        /// <summary>Return a dfloat4 vector constructed from a double4 vector by componentwise conversion.</summary>
        /// <param name="v">double4 to convert to dfloat4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 dfloat4(double4 v) { return new dfloat4(v); }

        /// <summary>Returns a uint hash code of a dfloat4 vector.</summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(dfloat4 v)
        {
            return (uint) csum(asuint(v) * uint4(0xE69626FFu, 0xBD010EEBu, 0x9CEDE1D1u, 0x43BE0B51u)) + 0xAF836EE1u;
        }

        /// <summary>
        /// Returns a uint4 vector hash code of a dfloat4 vector.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint4 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint4 hashwide(dfloat4 v)
        {
            return (asuint(v) * uint4(0xB130C137u, 0x54834775u, 0x7C022221u, 0xA2D00EDFu)) + 0xA8977779u;
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat4 vectors into a dfloat value.</summary>
        /// <param name="left">dfloat4 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat4 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat.</param>
        /// <returns>dfloat result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat shuffle(dfloat4 left, dfloat4 right, ShuffleComponent x)
        {
            return select_shuffle_component(left, right, x);
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat4 vectors into a dfloat2 vector.</summary>
        /// <param name="left">dfloat4 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat4 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat2 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat2 y component.</param>
        /// <returns>dfloat2 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 shuffle(dfloat4 left, dfloat4 right, ShuffleComponent x, ShuffleComponent y)
        {
            return dfloat2(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat4 vectors into a dfloat3 vector.</summary>
        /// <param name="left">dfloat4 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat4 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat3 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat3 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat3 z component.</param>
        /// <returns>dfloat3 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 shuffle(dfloat4 left, dfloat4 right, ShuffleComponent x, ShuffleComponent y, ShuffleComponent z)
        {
            return dfloat3(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat4 vectors into a dfloat4 vector.</summary>
        /// <param name="left">dfloat4 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat4 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat4 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat4 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat4 z component.</param>
        /// <param name="w">The ShuffleComponent to use when setting the resulting dfloat4 w component.</param>
        /// <returns>dfloat4 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 shuffle(dfloat4 left, dfloat4 right, ShuffleComponent x, ShuffleComponent y, ShuffleComponent z, ShuffleComponent w)
        {
            return dfloat4(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z),
                select_shuffle_component(left, right, w));
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat select_shuffle_component(dfloat4 a, dfloat4 b, ShuffleComponent component)
        {
            switch(component)
            {
                case ShuffleComponent.LeftX:
                    return a.x;
                case ShuffleComponent.LeftY:
                    return a.y;
                case ShuffleComponent.LeftZ:
                    return a.z;
                case ShuffleComponent.LeftW:
                    return a.w;
                case ShuffleComponent.RightX:
                    return b.x;
                case ShuffleComponent.RightY:
                    return b.y;
                case ShuffleComponent.RightZ:
                    return b.z;
                case ShuffleComponent.RightW:
                    return b.w;
                default:
                    throw new System.ArgumentException("Invalid shuffle component: " + component);
            }
        }

    }
}
