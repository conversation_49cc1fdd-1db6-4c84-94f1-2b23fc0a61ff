﻿using System;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using Unity.Deterministic.Mathematics;
using UnityEditor;
using UnityEngine;

namespace DeterministicMath.com.seibagames.deterministic_math.Editor
{
    public class dfloatDrawer : OdinValueDrawer<dfloat>
    {
        public override bool CanDrawTypeFilter(Type type)
        {
            return type == typeof(dfloat);
        }

        protected override void DrawPropertyLayout(GUIContent label)
        {
            var rect = EditorGUILayout.GetControlRect();
            var value = ValueEntry.SmartValue;
            
            value = (dfloat)EditorGUI.DoubleField(rect, label, Math.Round(dfloat.ToDouble(value.m_rawValue), 4, MidpointRounding.AwayFromZero));
            ValueEntry.SmartValue = value;
        }
    }
}