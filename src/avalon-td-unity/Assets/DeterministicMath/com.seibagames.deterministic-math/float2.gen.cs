//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. To update the generation of this file, modify and re-run Unity.Mathematics.CodeGen.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using System.Diagnostics;
using Sirenix.OdinInspector;
using Unity.IL2CPP.CompilerServices;
using Unity.Mathematics;
using UnityEngine;
using static Unity.Mathematics.math;
using static Unity.Deterministic.Mathematics.dmath;

#pragma warning disable 0660, 0661

namespace Unity.Deterministic.Mathematics
{
    /// <summary>A 2 component vector of dfloats.</summary>
    [DebuggerTypeProxy(typeof(dfloat2.DebuggerProxy))]
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    [InlineProperty(LabelWidth = 13)]
    public partial struct dfloat2 : System.IEquatable<dfloat2>, IFormattable
    {
        /// <summary>x component of the vector.</summary>
        [HorizontalGroup]
        public dfloat x;
        /// <summary>y component of the vector.</summary>
        [HorizontalGroup]
        public dfloat y;

        /// <summary>dfloat2 zero value.</summary>
        public static readonly dfloat2 zero;

        /// <summary>Constructs a dfloat2 vector from two dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(dfloat x, dfloat y)
        {
            this.x = x;
            this.y = y;
        }

        /// <summary>Constructs a dfloat2 vector from a dfloat2 vector.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(dfloat2 xy)
        {
            this.x = xy.x;
            this.y = xy.y;
        }

        /// <summary>Constructs a dfloat2 vector from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(dfloat v)
        {
            this.x = v;
            this.y = v;
        }

        /// <summary>Constructs a dfloat2 vector from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(bool v)
        {
            this.x = v ? dfloat.One : dfloat.Zero;
            this.y = v ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat2 vector from a bool2 vector by componentwise conversion.</summary>
        /// <param name="v">bool2 to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(bool2 v)
        {
            this.x = v.x ? dfloat.One : dfloat.Zero;
            this.y = v.y ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat2 vector from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(int v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
        }

        /// <summary>Constructs a dfloat2 vector from a int2 vector by componentwise conversion.</summary>
        /// <param name="v">int2 to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(int2 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
        }

        /// <summary>Constructs a dfloat2 vector from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(uint v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
        }

        /// <summary>Constructs a dfloat2 vector from a uint2 vector by componentwise conversion.</summary>
        /// <param name="v">uint2 to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(uint2 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
        }

        /// <summary>Constructs a dfloat2 vector from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(double v)
        {
            this.x = (dfloat)v;
            this.y = (dfloat)v;
        }

        /// <summary>Constructs a dfloat2 vector from a double2 vector by componentwise conversion.</summary>
        /// <param name="v">double2 to convert to dfloat2</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat2(double2 v)
        {
            this.x = (dfloat)v.x;
            this.y = (dfloat)v.y;
        }


        /// <summary>Implicitly converts a single dfloat value to a dfloat2 vector by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat2(dfloat v) { return new dfloat2(v); }

        /// <summary>Explicitly converts a single bool value to a dfloat2 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat2(bool v) { return new dfloat2(v); }

        /// <summary>Explicitly converts a bool2 vector to a dfloat2 vector by componentwise conversion.</summary>
        /// <param name="v">bool2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat2(bool2 v) { return new dfloat2(v); }

        /// <summary>Implicitly converts a single int value to a dfloat2 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat2(int v) { return new dfloat2(v); }

        /// <summary>Implicitly converts a int2 vector to a dfloat2 vector by componentwise conversion.</summary>
        /// <param name="v">int2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat2(int2 v) { return new dfloat2(v); }

        /// <summary>Implicitly converts a single uint value to a dfloat2 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat2(uint v) { return new dfloat2(v); }

        /// <summary>Implicitly converts a uint2 vector to a dfloat2 vector by componentwise conversion.</summary>
        /// <param name="v">uint2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat2(uint2 v) { return new dfloat2(v); }

        /// <summary>Explicitly converts a single double value to a dfloat2 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat2(double v) { return new dfloat2(v); }

        /// <summary>Explicitly converts a double2 vector to a dfloat2 vector by componentwise conversion.</summary>
        /// <param name="v">double2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat2(double2 v) { return new dfloat2(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat2 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator * (dfloat2 lhs, dfloat2 rhs) { return new dfloat2 (lhs.x * rhs.x, lhs.y * rhs.y); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise multiplication.</param>
        /// <returns>dfloat2 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator * (dfloat2 lhs, dfloat rhs) { return new dfloat2 (lhs.x * rhs, lhs.y * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat2 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator * (dfloat lhs, dfloat2 rhs) { return new dfloat2 (lhs * rhs.x, lhs * rhs.y); }


        /// <summary>Returns the result of a componentwise addition operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise addition.</param>
        /// <returns>dfloat2 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator + (dfloat2 lhs, dfloat2 rhs) { return new dfloat2 (lhs.x + rhs.x, lhs.y + rhs.y); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise addition.</param>
        /// <returns>dfloat2 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator + (dfloat2 lhs, dfloat rhs) { return new dfloat2 (lhs.x + rhs, lhs.y + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise addition.</param>
        /// <returns>dfloat2 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator + (dfloat lhs, dfloat2 rhs) { return new dfloat2 (lhs + rhs.x, lhs + rhs.y); }


        /// <summary>Returns the result of a componentwise subtraction operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat2 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator - (dfloat2 lhs, dfloat2 rhs) { return new dfloat2 (lhs.x - rhs.x, lhs.y - rhs.y); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise subtraction.</param>
        /// <returns>dfloat2 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator - (dfloat2 lhs, dfloat rhs) { return new dfloat2 (lhs.x - rhs, lhs.y - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat2 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator - (dfloat lhs, dfloat2 rhs) { return new dfloat2 (lhs - rhs.x, lhs - rhs.y); }


        /// <summary>Returns the result of a componentwise division operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise division.</param>
        /// <returns>dfloat2 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator / (dfloat2 lhs, dfloat2 rhs) { return new dfloat2 (lhs.x / rhs.x, lhs.y / rhs.y); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise division.</param>
        /// <returns>dfloat2 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator / (dfloat2 lhs, dfloat rhs) { return new dfloat2 (lhs.x / rhs, lhs.y / rhs); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise division.</param>
        /// <returns>dfloat2 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator / (dfloat lhs, dfloat2 rhs) { return new dfloat2 (lhs / rhs.x, lhs / rhs.y); }


        /// <summary>Returns the result of a componentwise modulus operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise modulus.</param>
        /// <returns>dfloat2 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator % (dfloat2 lhs, dfloat2 rhs) { return new dfloat2 (lhs.x % rhs.x, lhs.y % rhs.y); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise modulus.</param>
        /// <returns>dfloat2 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator % (dfloat2 lhs, dfloat rhs) { return new dfloat2 (lhs.x % rhs, lhs.y % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise modulus.</param>
        /// <returns>dfloat2 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator % (dfloat lhs, dfloat2 rhs) { return new dfloat2 (lhs % rhs.x, lhs % rhs.y); }


        /// <summary>Returns the result of a componentwise increment operation on a dfloat2 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>dfloat2 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator ++ (dfloat2 val) { return new dfloat2 (++val.x, ++val.y); }


        /// <summary>Returns the result of a componentwise decrement operation on a dfloat2 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>dfloat2 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator -- (dfloat2 val) { return new dfloat2 (--val.x, --val.y); }


        /// <summary>Returns the result of a componentwise less than operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise less than.</param>
        /// <returns>bool2 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator < (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x < rhs.x, lhs.y < rhs.y); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less than.</param>
        /// <returns>bool2 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator < (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x < rhs, lhs.y < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise less than.</param>
        /// <returns>bool2 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator < (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs < rhs.x, lhs < rhs.y); }


        /// <summary>Returns the result of a componentwise less or equal operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise less or equal.</param>
        /// <returns>bool2 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator <= (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x <= rhs.x, lhs.y <= rhs.y); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less or equal.</param>
        /// <returns>bool2 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator <= (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x <= rhs, lhs.y <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise less or equal.</param>
        /// <returns>bool2 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator <= (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs <= rhs.x, lhs <= rhs.y); }


        /// <summary>Returns the result of a componentwise greater than operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise greater than.</param>
        /// <returns>bool2 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator > (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x > rhs.x, lhs.y > rhs.y); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater than.</param>
        /// <returns>bool2 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator > (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x > rhs, lhs.y > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise greater than.</param>
        /// <returns>bool2 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator > (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs > rhs.x, lhs > rhs.y); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator >= (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x >= rhs.x, lhs.y >= rhs.y); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <returns>bool2 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator >= (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x >= rhs, lhs.y >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator >= (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs >= rhs.x, lhs >= rhs.y); }


        /// <summary>Returns the result of a componentwise unary minus operation on a dfloat2 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>dfloat2 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator - (dfloat2 val) { return new dfloat2 (-val.x, -val.y); }


        /// <summary>Returns the result of a componentwise unary plus operation on a dfloat2 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>dfloat2 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 operator + (dfloat2 val) { return new dfloat2 (+val.x, +val.y); }


        /// <summary>Returns the result of a componentwise equality operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise equality.</param>
        /// <returns>bool2 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator == (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x == rhs.x, lhs.y == rhs.y); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise equality.</param>
        /// <returns>bool2 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator == (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x == rhs, lhs.y == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise equality.</param>
        /// <returns>bool2 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator == (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs == rhs.x, lhs == rhs.y); }


        /// <summary>Returns the result of a componentwise not equal operation on two dfloat2 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise not equal.</param>
        /// <returns>bool2 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator != (dfloat2 lhs, dfloat2 rhs) { return new bool2 (lhs.x != rhs.x, lhs.y != rhs.y); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat2 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat2 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise not equal.</param>
        /// <returns>bool2 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator != (dfloat2 lhs, dfloat rhs) { return new bool2 (lhs.x != rhs, lhs.y != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat value and a dfloat2 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat2 to use to compute componentwise not equal.</param>
        /// <returns>bool2 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 operator != (dfloat lhs, dfloat2 rhs) { return new bool2 (lhs != rhs.x, lhs != rhs.y); }




        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, y); }
        }



        /// <summary>Returns the dfloat element at a specified index.</summary>
        unsafe public dfloat this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 2)
                    throw new System.ArgumentException("index must be between[0...1]");
#endif
                fixed (dfloat2* array = &this) { return ((dfloat*)array)[index]; }
            }
            set
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 2)
                    throw new System.ArgumentException("index must be between[0...1]");
#endif
                fixed (dfloat* array = &x) { array[index] = value; }
            }
        }

        /// <summary>Returns true if the dfloat2 is equal to a given dfloat2, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(dfloat2 rhs) { return x == rhs.x && y == rhs.y; }

        /// <summary>Returns true if the dfloat2 is equal to a given dfloat2, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is dfloat2 converted && Equals(converted); }


        /// <summary>Returns a hash code for the dfloat2.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)dmath.hash(this); }


        /// <summary>Returns a string representation of the dfloat2.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("dfloat2({0}f, {1}f)", x, y);
        }

        /// <summary>Returns a string representation of the dfloat2 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("dfloat2({0}f, {1}f)", x.ToString(format, formatProvider), y.ToString(format, formatProvider));
        }

        internal sealed class DebuggerProxy
        {
            public dfloat x;
            public dfloat y;
            public DebuggerProxy(dfloat2 v)
            {
                x = v.x;
                y = v.y;
            }
        }

    }

    public static partial class dmath
    {
        public static Vector2 ToVector2(this dfloat2 v) { return new Vector2((float)v.x, (float)v.y); }
        
        /// <summary>Returns a dfloat2 vector constructed from two dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <returns>dfloat2 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(dfloat x, dfloat y) { return new dfloat2(x, y); }

        /// <summary>Returns a dfloat2 vector constructed from a dfloat2 vector.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <returns>dfloat2 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(dfloat2 xy) { return new dfloat2(xy); }

        /// <summary>Returns a dfloat2 vector constructed from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(dfloat v) { return new dfloat2(v); }

        /// <summary>Returns a dfloat2 vector constructed from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(bool v) { return new dfloat2(v); }

        /// <summary>Return a dfloat2 vector constructed from a bool2 vector by componentwise conversion.</summary>
        /// <param name="v">bool2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(bool2 v) { return new dfloat2(v); }

        /// <summary>Returns a dfloat2 vector constructed from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(int v) { return new dfloat2(v); }

        /// <summary>Return a dfloat2 vector constructed from a int2 vector by componentwise conversion.</summary>
        /// <param name="v">int2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(int2 v) { return new dfloat2(v); }

        /// <summary>Returns a dfloat2 vector constructed from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(uint v) { return new dfloat2(v); }

        /// <summary>Return a dfloat2 vector constructed from a uint2 vector by componentwise conversion.</summary>
        /// <param name="v">uint2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(uint2 v) { return new dfloat2(v); }

        /// <summary>Returns a dfloat2 vector constructed from a single half value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">half to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(half v) { return new dfloat2(v); }

        /// <summary>Return a dfloat2 vector constructed from a half2 vector by componentwise conversion.</summary>
        /// <param name="v">half2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(half2 v) { return new dfloat2(v); }

        /// <summary>Returns a dfloat2 vector constructed from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(double v) { return new dfloat2(v); }

        /// <summary>Return a dfloat2 vector constructed from a double2 vector by componentwise conversion.</summary>
        /// <param name="v">double2 to convert to dfloat2</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 dfloat2(double2 v) { return new dfloat2(v); }

        /// <summary>Returns a uint hash code of a dfloat2 vector.</summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(dfloat2 v)
        {
            return (uint) csum(asuint(v) * uint2(0xFA3A3285u, 0xAD55999Du)) + 0xDCDD5341u;
        }

        /// <summary>
        /// Returns a uint2 vector hash code of a dfloat2 vector.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint2 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2 hashwide(dfloat2 v)
        {
            return (asuint(v) * uint2(0x94DDD769u, 0xA1E92D39u)) + 0x4583C801u;
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat2 vectors into a dfloat value.</summary>
        /// <param name="left">dfloat2 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat2 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat.</param>
        /// <returns>dfloat result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat shuffle(dfloat2 left, dfloat2 right, Unity.Mathematics.math.ShuffleComponent x)
        {
            return select_shuffle_component(left, right, x);
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat2 vectors into a dfloat2 vector.</summary>
        /// <param name="left">dfloat2 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat2 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat2 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat2 y component.</param>
        /// <returns>dfloat2 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 shuffle(dfloat2 left, dfloat2 right, Unity.Mathematics.math.ShuffleComponent x, Unity.Mathematics.math.ShuffleComponent y)
        {
            return dfloat2(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat2 vectors into a dfloat3 vector.</summary>
        /// <param name="left">dfloat2 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat2 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat3 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat3 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat3 z component.</param>
        /// <returns>dfloat3 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 shuffle(dfloat2 left, dfloat2 right, Unity.Mathematics.math.ShuffleComponent x, Unity.Mathematics.math.ShuffleComponent y, Unity.Mathematics.math.ShuffleComponent z)
        {
            return dfloat3(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat2 vectors into a dfloat4 vector.</summary>
        /// <param name="left">dfloat2 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat2 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat4 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat4 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat4 z component.</param>
        /// <param name="w">The ShuffleComponent to use when setting the resulting dfloat4 w component.</param>
        /// <returns>dfloat4 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 shuffle(dfloat2 left, dfloat2 right, Unity.Mathematics.math.ShuffleComponent x, Unity.Mathematics.math.ShuffleComponent y, Unity.Mathematics.math.ShuffleComponent z, Unity.Mathematics.math.ShuffleComponent w)
        {
            return dfloat4(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z),
                select_shuffle_component(left, right, w));
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat select_shuffle_component(dfloat2 a, dfloat2 b, Unity.Mathematics.math.ShuffleComponent component)
        {
            switch(component)
            {
                case Unity.Mathematics.math.ShuffleComponent.LeftX:
                    return a.x;
                case Unity.Mathematics.math.ShuffleComponent.LeftY:
                    return a.y;
                case Unity.Mathematics.math.ShuffleComponent.RightX:
                    return b.x;
                case Unity.Mathematics.math.ShuffleComponent.RightY:
                    return b.y;
                default:
                    throw new System.ArgumentException("Invalid shuffle component: " + component);
            }
        }

    }
}
