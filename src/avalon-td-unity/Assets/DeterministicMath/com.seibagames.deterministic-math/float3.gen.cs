//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. To update the generation of this file, modify and re-run Unity.Mathematics.CodeGen.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using System.Diagnostics;
using Sirenix.OdinInspector;
using Unity.IL2CPP.CompilerServices;
using Unity.Mathematics;
using UnityEngine;
using static Unity.Mathematics.math;
using static Unity.Deterministic.Mathematics.dmath;

#pragma warning disable 0660, 0661

namespace Unity.Deterministic.Mathematics
{
    /// <summary>A 3 component vector of dfloats.</summary>
    [DebuggerTypeProxy(typeof(dfloat3.DebuggerProxy))]
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    [InlineProperty(LabelWidth = 13)]
    public partial struct dfloat3 : System.IEquatable<dfloat3>, IFormattable
    {
        /// <summary>x component of the vector.</summary>
        [HorizontalGroup]
        public dfloat x;
        /// <summary>y component of the vector.</summary>
        [HorizontalGroup]
        public dfloat y;
        /// <summary>z component of the vector.</summary>
        [HorizontalGroup]
        public dfloat z;

        /// <summary>dfloat3 zero value.</summary>
        public static readonly dfloat3 zero;

        /// <summary>Constructs a dfloat3 vector from three dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(dfloat x, dfloat y, dfloat z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        /// <summary>Constructs a dfloat3 vector from a dfloat value and a dfloat2 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yz">The constructed vector's yz components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(dfloat x, dfloat2 yz)
        {
            this.x = x;
            this.y = yz.x;
            this.z = yz.y;
        }

        /// <summary>Constructs a dfloat3 vector from a dfloat2 vector and a dfloat value.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(dfloat2 xy, dfloat z)
        {
            this.x = xy.x;
            this.y = xy.y;
            this.z = z;
        }

        /// <summary>Constructs a dfloat3 vector from a dfloat3 vector.</summary>
        /// <param name="xyz">The constructed vector's xyz components will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(dfloat3 xyz)
        {
            this.x = xyz.x;
            this.y = xyz.y;
            this.z = xyz.z;
        }

        /// <summary>Constructs a dfloat3 vector from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(dfloat v)
        {
            this.x = v;
            this.y = v;
            this.z = v;
        }

        /// <summary>Constructs a dfloat3 vector from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(bool v)
        {
            this.x = v ? dfloat.One : dfloat.Zero;
            this.y = v ? dfloat.One : dfloat.Zero;
            this.z = v ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat3 vector from a bool3 vector by componentwise conversion.</summary>
        /// <param name="v">bool3 to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(bool3 v)
        {
            this.x = v.x ? dfloat.One : dfloat.Zero;
            this.y = v.y ? dfloat.One : dfloat.Zero;
            this.z = v.z ? dfloat.One : dfloat.Zero;
        }

        /// <summary>Constructs a dfloat3 vector from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(int v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
            this.z = (dfloat) v;
        }

        /// <summary>Constructs a dfloat3 vector from a int3 vector by componentwise conversion.</summary>
        /// <param name="v">int3 to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(int3 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
            this.z = (dfloat) v.z;
        }

        /// <summary>Constructs a dfloat3 vector from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(uint v)
        {
            this.x = (dfloat) v;
            this.y = (dfloat) v;
            this.z = (dfloat) v;
        }

        /// <summary>Constructs a dfloat3 vector from a uint3 vector by componentwise conversion.</summary>
        /// <param name="v">uint3 to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(uint3 v)
        {
            this.x = (dfloat) v.x;
            this.y = (dfloat) v.y;
            this.z = (dfloat) v.z;
        }

        /// <summary>Constructs a dfloat3 vector from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(double v)
        {
            this.x = (dfloat)v;
            this.y = (dfloat)v;
            this.z = (dfloat)v;
        }

        /// <summary>Constructs a dfloat3 vector from a double3 vector by componentwise conversion.</summary>
        /// <param name="v">double3 to convert to dfloat3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public dfloat3(double3 v)
        {
            this.x = (dfloat)v.x;
            this.y = (dfloat)v.y;
            this.z = (dfloat)v.z;
        }


        /// <summary>Implicitly converts a single dfloat value to a dfloat3 vector by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat3(dfloat v) { return new dfloat3(v); }

        /// <summary>Explicitly converts a single bool value to a dfloat3 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat3(bool v) { return new dfloat3(v); }

        /// <summary>Explicitly converts a bool3 vector to a dfloat3 vector by componentwise conversion.</summary>
        /// <param name="v">bool3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat3(bool3 v) { return new dfloat3(v); }

        /// <summary>Implicitly converts a single int value to a dfloat3 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat3(int v) { return new dfloat3(v); }

        /// <summary>Implicitly converts a int3 vector to a dfloat3 vector by componentwise conversion.</summary>
        /// <param name="v">int3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat3(int3 v) { return new dfloat3(v); }

        /// <summary>Implicitly converts a single uint value to a dfloat3 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat3(uint v) { return new dfloat3(v); }

        /// <summary>Implicitly converts a uint3 vector to a dfloat3 vector by componentwise conversion.</summary>
        /// <param name="v">uint3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator dfloat3(uint3 v) { return new dfloat3(v); }

        /// <summary>Explicitly converts a single double value to a dfloat3 vector by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat3(double v) { return new dfloat3(v); }

        /// <summary>Explicitly converts a double3 vector to a dfloat3 vector by componentwise conversion.</summary>
        /// <param name="v">double3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator dfloat3(double3 v) { return new dfloat3(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator * (dfloat3 lhs, dfloat3 rhs) { return new dfloat3 (lhs.x * rhs.x, lhs.y * rhs.y, lhs.z * rhs.z); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise multiplication.</param>
        /// <returns>dfloat3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator * (dfloat3 lhs, dfloat rhs) { return new dfloat3 (lhs.x * rhs, lhs.y * rhs, lhs.z * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise multiplication.</param>
        /// <returns>dfloat3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator * (dfloat lhs, dfloat3 rhs) { return new dfloat3 (lhs * rhs.x, lhs * rhs.y, lhs * rhs.z); }


        /// <summary>Returns the result of a componentwise addition operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise addition.</param>
        /// <returns>dfloat3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator + (dfloat3 lhs, dfloat3 rhs) { return new dfloat3 (lhs.x + rhs.x, lhs.y + rhs.y, lhs.z + rhs.z); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise addition.</param>
        /// <returns>dfloat3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator + (dfloat3 lhs, dfloat rhs) { return new dfloat3 (lhs.x + rhs, lhs.y + rhs, lhs.z + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise addition.</param>
        /// <returns>dfloat3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator + (dfloat lhs, dfloat3 rhs) { return new dfloat3 (lhs + rhs.x, lhs + rhs.y, lhs + rhs.z); }


        /// <summary>Returns the result of a componentwise subtraction operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator - (dfloat3 lhs, dfloat3 rhs) { return new dfloat3 (lhs.x - rhs.x, lhs.y - rhs.y, lhs.z - rhs.z); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise subtraction.</param>
        /// <returns>dfloat3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator - (dfloat3 lhs, dfloat rhs) { return new dfloat3 (lhs.x - rhs, lhs.y - rhs, lhs.z - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise subtraction.</param>
        /// <returns>dfloat3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator - (dfloat lhs, dfloat3 rhs) { return new dfloat3 (lhs - rhs.x, lhs - rhs.y, lhs - rhs.z); }


        /// <summary>Returns the result of a componentwise division operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise division.</param>
        /// <returns>dfloat3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator / (dfloat3 lhs, dfloat3 rhs) { return new dfloat3 (lhs.x / rhs.x, lhs.y / rhs.y, lhs.z / rhs.z); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise division.</param>
        /// <returns>dfloat3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator / (dfloat3 lhs, dfloat rhs) { return new dfloat3 (lhs.x / rhs, lhs.y / rhs, lhs.z / rhs); }

        /// <summary>Returns the result of a componentwise division operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise division.</param>
        /// <returns>dfloat3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator / (dfloat lhs, dfloat3 rhs) { return new dfloat3 (lhs / rhs.x, lhs / rhs.y, lhs / rhs.z); }


        /// <summary>Returns the result of a componentwise modulus operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise modulus.</param>
        /// <returns>dfloat3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator % (dfloat3 lhs, dfloat3 rhs) { return new dfloat3 (lhs.x % rhs.x, lhs.y % rhs.y, lhs.z % rhs.z); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise modulus.</param>
        /// <returns>dfloat3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator % (dfloat3 lhs, dfloat rhs) { return new dfloat3 (lhs.x % rhs, lhs.y % rhs, lhs.z % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise modulus.</param>
        /// <returns>dfloat3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator % (dfloat lhs, dfloat3 rhs) { return new dfloat3 (lhs % rhs.x, lhs % rhs.y, lhs % rhs.z); }


        /// <summary>Returns the result of a componentwise increment operation on a dfloat3 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>dfloat3 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator ++ (dfloat3 val) { return new dfloat3 (++val.x, ++val.y, ++val.z); }


        /// <summary>Returns the result of a componentwise decrement operation on a dfloat3 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>dfloat3 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator -- (dfloat3 val) { return new dfloat3 (--val.x, --val.y, --val.z); }


        /// <summary>Returns the result of a componentwise less than operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise less than.</param>
        /// <returns>bool3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator < (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x < rhs.x, lhs.y < rhs.y, lhs.z < rhs.z); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less than.</param>
        /// <returns>bool3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator < (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x < rhs, lhs.y < rhs, lhs.z < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise less than.</param>
        /// <returns>bool3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator < (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs < rhs.x, lhs < rhs.y, lhs < rhs.z); }


        /// <summary>Returns the result of a componentwise less or equal operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise less or equal.</param>
        /// <returns>bool3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator <= (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x <= rhs.x, lhs.y <= rhs.y, lhs.z <= rhs.z); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise less or equal.</param>
        /// <returns>bool3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator <= (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x <= rhs, lhs.y <= rhs, lhs.z <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise less or equal.</param>
        /// <returns>bool3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator <= (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs <= rhs.x, lhs <= rhs.y, lhs <= rhs.z); }


        /// <summary>Returns the result of a componentwise greater than operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise greater than.</param>
        /// <returns>bool3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator > (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x > rhs.x, lhs.y > rhs.y, lhs.z > rhs.z); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater than.</param>
        /// <returns>bool3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator > (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x > rhs, lhs.y > rhs, lhs.z > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise greater than.</param>
        /// <returns>bool3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator > (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs > rhs.x, lhs > rhs.y, lhs > rhs.z); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise greater or equal.</param>
        /// <returns>bool3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator >= (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x >= rhs.x, lhs.y >= rhs.y, lhs.z >= rhs.z); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <returns>bool3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator >= (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x >= rhs, lhs.y >= rhs, lhs.z >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise greater or equal.</param>
        /// <returns>bool3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator >= (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs >= rhs.x, lhs >= rhs.y, lhs >= rhs.z); }


        /// <summary>Returns the result of a componentwise unary minus operation on a dfloat3 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>dfloat3 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator - (dfloat3 val) { return new dfloat3 (-val.x, -val.y, -val.z); }


        /// <summary>Returns the result of a componentwise unary plus operation on a dfloat3 vector.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>dfloat3 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 operator + (dfloat3 val) { return new dfloat3 (+val.x, +val.y, +val.z); }


        /// <summary>Returns the result of a componentwise equality operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise equality.</param>
        /// <returns>bool3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator == (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x == rhs.x, lhs.y == rhs.y, lhs.z == rhs.z); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise equality.</param>
        /// <returns>bool3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator == (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x == rhs, lhs.y == rhs, lhs.z == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise equality.</param>
        /// <returns>bool3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator == (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs == rhs.x, lhs == rhs.y, lhs == rhs.z); }


        /// <summary>Returns the result of a componentwise not equal operation on two dfloat3 vectors.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise not equal.</param>
        /// <returns>bool3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator != (dfloat3 lhs, dfloat3 rhs) { return new bool3 (lhs.x != rhs.x, lhs.y != rhs.y, lhs.z != rhs.z); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat3 vector and a dfloat value.</summary>
        /// <param name="lhs">Left hand side dfloat3 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat to use to compute componentwise not equal.</param>
        /// <returns>bool3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator != (dfloat3 lhs, dfloat rhs) { return new bool3 (lhs.x != rhs, lhs.y != rhs, lhs.z != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on a dfloat value and a dfloat3 vector.</summary>
        /// <param name="lhs">Left hand side dfloat to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side dfloat3 to use to compute componentwise not equal.</param>
        /// <returns>bool3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 operator != (dfloat lhs, dfloat3 rhs) { return new bool3 (lhs != rhs.x, lhs != rhs.y, lhs != rhs.z); }




        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 xzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(x, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 yzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(y, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zxzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zyzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat4 zzzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat4(z, z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 xzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(x, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; z = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 yzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(y, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; y = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zxz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, x, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; x = value.z; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zyz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, y, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat3 zzz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat3(z, z, z); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, x); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 xz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(x, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { x = value.x; z = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, y); }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 yz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(y, z); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { y = value.x; z = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zx
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, x); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; x = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zy
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, y); }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set { z = value.x; y = value.y; }
        }


        /// <summary>Swizzles the vector.</summary>
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        public dfloat2 zz
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get { return new dfloat2(z, z); }
        }



        /// <summary>Returns the dfloat element at a specified index.</summary>
        unsafe public dfloat this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 3)
                    throw new System.ArgumentException("index must be between[0...2]");
#endif
                fixed (dfloat3* array = &this) { return ((dfloat*)array)[index]; }
            }
            set
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 3)
                    throw new System.ArgumentException("index must be between[0...2]");
#endif
                fixed (dfloat* array = &x) { array[index] = value; }
            }
        }

        /// <summary>Returns true if the dfloat3 is equal to a given dfloat3, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(dfloat3 rhs) { return x == rhs.x && y == rhs.y && z == rhs.z; }

        /// <summary>Returns true if the dfloat3 is equal to a given dfloat3, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is dfloat3 converted && Equals(converted); }


        /// <summary>Returns a hash code for the dfloat3.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)dmath.hash(this); }


        /// <summary>Returns a string representation of the dfloat3.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("dfloat3({0}f, {1}f, {2}f)", x, y, z);
        }

        /// <summary>Returns a string representation of the dfloat3 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("dfloat3({0}f, {1}f, {2}f)", x.ToString(format, formatProvider), y.ToString(format, formatProvider), z.ToString(format, formatProvider));
        }

        internal sealed class DebuggerProxy
        {
            public dfloat x;
            public dfloat y;
            public dfloat z;
            public DebuggerProxy(dfloat3 v)
            {
                x = v.x;
                y = v.y;
                z = v.z;
            }
        }

    }

    public static partial class dmath
    {
        public static Vector3 ToVector3(this dfloat3 v) { return new Vector3((float)v.x, (float)v.y, (float)v.z); }
        
        /// <summary>Returns a dfloat3 vector constructed from three dfloat values.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="y">The constructed vector's y component will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <returns>dfloat3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(dfloat x, dfloat y, dfloat z) { return new dfloat3(x, y, z); }

        /// <summary>Returns a dfloat3 vector constructed from a dfloat value and a dfloat2 vector.</summary>
        /// <param name="x">The constructed vector's x component will be set to this value.</param>
        /// <param name="yz">The constructed vector's yz components will be set to this value.</param>
        /// <returns>dfloat3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(dfloat x, dfloat2 yz) { return new dfloat3(x, yz); }

        /// <summary>Returns a dfloat3 vector constructed from a dfloat2 vector and a dfloat value.</summary>
        /// <param name="xy">The constructed vector's xy components will be set to this value.</param>
        /// <param name="z">The constructed vector's z component will be set to this value.</param>
        /// <returns>dfloat3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(dfloat2 xy, dfloat z) { return new dfloat3(xy, z); }

        /// <summary>Returns a dfloat3 vector constructed from a dfloat3 vector.</summary>
        /// <param name="xyz">The constructed vector's xyz components will be set to this value.</param>
        /// <returns>dfloat3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(dfloat3 xyz) { return new dfloat3(xyz); }

        /// <summary>Returns a dfloat3 vector constructed from a single dfloat value by assigning it to every component.</summary>
        /// <param name="v">dfloat to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(dfloat v) { return new dfloat3(v); }

        /// <summary>Returns a dfloat3 vector constructed from a single bool value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">bool to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(bool v) { return new dfloat3(v); }

        /// <summary>Return a dfloat3 vector constructed from a bool3 vector by componentwise conversion.</summary>
        /// <param name="v">bool3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(bool3 v) { return new dfloat3(v); }

        /// <summary>Returns a dfloat3 vector constructed from a single int value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">int to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(int v) { return new dfloat3(v); }

        /// <summary>Return a dfloat3 vector constructed from a int3 vector by componentwise conversion.</summary>
        /// <param name="v">int3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(int3 v) { return new dfloat3(v); }

        /// <summary>Returns a dfloat3 vector constructed from a single uint value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">uint to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(uint v) { return new dfloat3(v); }

        /// <summary>Return a dfloat3 vector constructed from a uint3 vector by componentwise conversion.</summary>
        /// <param name="v">uint3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(uint3 v) { return new dfloat3(v); }

        /// <summary>Returns a dfloat3 vector constructed from a single half value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">half to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(half v) { return new dfloat3(v); }

        /// <summary>Return a dfloat3 vector constructed from a half3 vector by componentwise conversion.</summary>
        /// <param name="v">half3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(half3 v) { return new dfloat3(v); }

        /// <summary>Returns a dfloat3 vector constructed from a single double value by converting it to dfloat and assigning it to every component.</summary>
        /// <param name="v">double to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(double v) { return new dfloat3(v); }

        /// <summary>Return a dfloat3 vector constructed from a double3 vector by componentwise conversion.</summary>
        /// <param name="v">double3 to convert to dfloat3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 dfloat3(double3 v) { return new dfloat3(v); }

        /// <summary>Returns a uint hash code of a dfloat3 vector.</summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(dfloat3 v)
        {
            return (uint) csum(asuint(v) * uint3(0x9B13B92Du, 0x4ABF0813u, 0x86068063u)) + 0xD75513F9u;
        }

        /// <summary>
        /// Returns a uint3 vector hash code of a dfloat3 vector.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Vector value to hash.</param>
        /// <returns>uint3 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint3 hashwide(dfloat3 v)
        {
            return (asuint(v) * uint3(0x5AB3E8CDu, 0x676E8407u, 0xB36DE767u)) + 0x6FCA387Du;
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat3 vectors into a dfloat value.</summary>
        /// <param name="left">dfloat3 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat3 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat.</param>
        /// <returns>dfloat result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat shuffle(dfloat3 left, dfloat3 right, ShuffleComponent x)
        {
            return select_shuffle_component(left, right, x);
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat3 vectors into a dfloat2 vector.</summary>
        /// <param name="left">dfloat3 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat3 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat2 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat2 y component.</param>
        /// <returns>dfloat2 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 shuffle(dfloat3 left, dfloat3 right, ShuffleComponent x, ShuffleComponent y)
        {
            return dfloat2(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat3 vectors into a dfloat3 vector.</summary>
        /// <param name="left">dfloat3 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat3 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat3 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat3 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat3 z component.</param>
        /// <returns>dfloat3 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 shuffle(dfloat3 left, dfloat3 right, ShuffleComponent x, ShuffleComponent y, ShuffleComponent z)
        {
            return dfloat3(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z));
        }

        /// <summary>Returns the result of specified shuffling of the components from two dfloat3 vectors into a dfloat4 vector.</summary>
        /// <param name="left">dfloat3 to use as the left argument of the shuffle operation.</param>
        /// <param name="right">dfloat3 to use as the right argument of the shuffle operation.</param>
        /// <param name="x">The ShuffleComponent to use when setting the resulting dfloat4 x component.</param>
        /// <param name="y">The ShuffleComponent to use when setting the resulting dfloat4 y component.</param>
        /// <param name="z">The ShuffleComponent to use when setting the resulting dfloat4 z component.</param>
        /// <param name="w">The ShuffleComponent to use when setting the resulting dfloat4 w component.</param>
        /// <returns>dfloat4 result of the shuffle operation.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 shuffle(dfloat3 left, dfloat3 right, ShuffleComponent x, ShuffleComponent y, ShuffleComponent z, ShuffleComponent w)
        {
            return dfloat4(
                select_shuffle_component(left, right, x),
                select_shuffle_component(left, right, y),
                select_shuffle_component(left, right, z),
                select_shuffle_component(left, right, w));
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat select_shuffle_component(dfloat3 a, dfloat3 b, ShuffleComponent component)
        {
            switch(component)
            {
                case ShuffleComponent.LeftX:
                    return a.x;
                case ShuffleComponent.LeftY:
                    return a.y;
                case ShuffleComponent.LeftZ:
                    return a.z;
                case ShuffleComponent.RightX:
                    return b.x;
                case ShuffleComponent.RightY:
                    return b.y;
                case ShuffleComponent.RightZ:
                    return b.z;
                default:
                    throw new System.ArgumentException("Invalid shuffle component: " + component);
            }
        }

    }
}
