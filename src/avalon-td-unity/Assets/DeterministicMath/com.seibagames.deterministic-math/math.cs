using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;
using Unity.IL2CPP.CompilerServices;
using Unity.Mathematics;
using static Unity.Mathematics.math;

namespace Unity.Deterministic.Mathematics
{
    /// <summary>
    /// A static class to contain various math functions and constants.
    /// </summary>
    [Il2CppEagerStaticClassConstruction]
    public static partial class dmath
    {
        // public static readonly dfloat E = 2.7182818284590451m;
        // public static readonly dfloat PI = new dfloat(RAW_PI);
        // public static readonly dfloat PI_TIMES_2 = new dfloat(RAW_PI_TIMES_2);
        // public static readonly dfloat PI_OVER_2 = new dfloat(RAW_PI_OVER_2);
        // public static readonly dfloat PI_INV = 0.3183098861837906715377675267M;
        // public static readonly dfloat PI_OVER_2_INV = 0.6366197723675813430755350535M;
        // public static readonly dfloat SQRT2 = 1.4142135623730952m;
        // public static readonly dfloat LN10 = 2.3025850929940459m;
        // public static readonly dfloat LN2 = 0.69314718055994529m;
        // public static readonly dfloat LOG10E = 0.43429448190325182m;
        // public static readonly dfloat LOG2E = 1.4426950408889634m;
        //
        // const long RAW_PI_TIMES_2 = 0x6487ED511;
        // const long RAW_PI = 0x3243F6A88;
        // const long RAW_PI_OVER_2 = 0x1921FB544;

        /// <summary>Returns the bit pattern of a dfloat as an int.</summary>
        /// <param name="x">The dfloat bits to copy.</param>
        /// <returns>The int with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int asint(dfloat x) { return (int)math.asint((int)x); }

        /// <summary>Returns the bit pattern of a dfloat2 as an int2.</summary>
        /// <param name="x">The dfloat2 bits to copy.</param>
        /// <returns>The int2 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2 asint(dfloat2 x) { return math.int2((int)x.x, (int)x.y); }

        /// <summary>Returns the bit pattern of a dfloat3 as an int3.</summary>
        /// <param name="x">The dfloat3 bits to copy.</param>
        /// <returns>The int3 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int3 asint(dfloat3 x) { return math.int3((int)x.x, (int)x.y, (int)x.z); }

        /// <summary>Returns the bit pattern of a dfloat4 as an int4.</summary>
        /// <param name="x">The dfloat4 bits to copy.</param>
        /// <returns>The int4 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int4 asint(dfloat4 x) { return math.int4((int)x.x, (int)x.y, (int)x.z, (int)x.w); }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint asuint(dfloat x) { return (uint)x; }

        /// <summary>Returns the bit pattern of a fp2 as a uint2.</summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2 asuint(dfloat2 x) { return math.uint2(asuint(x.x), asuint(x.y)); }

        /// <summary>Returns the bit pattern of a fp3 as a uint3.</summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint3 asuint(dfloat3 x) { return math.uint3(asuint(x.x), asuint(x.y), asuint(x.z)); }

        /// <summary>Returns the bit pattern of a fp4 as a uint4.</summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint4 asuint(dfloat4 x) { return math.uint4(asuint(x.x), asuint(x.y), asuint(x.z), asuint(x.w)); }

        /// <summary>Returns the bit pattern of an int as a dfloat.</summary>
        /// <param name="x">The int bits to copy.</param>
        /// <returns>The dfloat with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat asdfloat(int x)
        {
            return new dfloat(x);
        }

        /// <summary>Returns the bit pattern of an int2 as a dfloat2.</summary>
        /// <param name="x">The int2 bits to copy.</param>
        /// <returns>The dfloat2 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 asdfloat(int2 x)
        {
            return new dfloat2(new dfloat(x.x), new dfloat(x.y));
        }

        /// <summary>Returns the bit pattern of an int3 as a dfloat3.</summary>
        /// <param name="x">The int3 bits to copy.</param>
        /// <returns>The dfloat3 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 asdfloat(int3 x)
        {
            return new dfloat3(new dfloat(x.x), new dfloat(x.y), new dfloat(x.z));
        }

        /// <summary>Returns the bit pattern of an int4 as a dfloat4.</summary>
        /// <param name="x">The int4 bits to copy.</param>
        /// <returns>The dfloat4 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 asdfloat(int4 x)
        {
            return new dfloat4(new dfloat(x.x), new dfloat(x.y), new dfloat(x.z), new dfloat(x.w));
        }

        /// <summary>Returns the bit pattern of a uint as a dfloat.</summary>
        /// <param name="x">The uint bits to copy.</param>
        /// <returns>The dfloat with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat asdfloat(uint x)
        {
            return new dfloat(x);
        }

        /// <summary>Returns the bit pattern of a uint2 as a dfloat2.</summary>
        /// <param name="x">The uint2 bits to copy.</param>
        /// <returns>The dfloat2 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 asdfloat(uint2 x)
        {
            return new dfloat2(new dfloat(x.x), new dfloat(x.y));
        }

        /// <summary>Returns the bit pattern of a uint3 as a dfloat3.</summary>
        /// <param name="x">The uint3 bits to copy.</param>
        /// <returns>The dfloat3 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 asdfloat(uint3 x)
        {
            return new dfloat3(new dfloat(x.x), new dfloat(x.y), new dfloat(x.z));
        }

        /// <summary>Returns the bit pattern of a uint4 as a dfloat4.</summary>
        /// <param name="x">The uint4 bits to copy.</param>
        /// <returns>The dfloat4 with the same bit pattern as the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 asdfloat(uint4 x)
        {
            return new dfloat4(new dfloat(x.x), new dfloat(x.y), new dfloat(x.z), new dfloat(x.w));
        }

        /// <summary>Returns true if the input dfloat is a finite dfloating point value, false otherwise.</summary>
        /// <param name="x">The dfloat value to test.</param>
        /// <returns>True if the dfloat is finite, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool isfinite(dfloat x) { return true; }

        /// <summary>Returns a bool2 indicating for each component of a dfloat2 whether it is a finite dfloating point value.</summary>
        /// <param name="x">The dfloat2 value to test.</param>
        /// <returns>A bool2 where it is true in a component if that component is finite, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 isfinite(dfloat2 x) { return true; }

        /// <summary>Returns a bool3 indicating for each component of a dfloat3 whether it is a finite dfloating point value.</summary>
        /// <param name="x">The dfloat3 value to test.</param>
        /// <returns>A bool3 where it is true in a component if that component is finite, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 isfinite(dfloat3 x) { return true; }

        /// <summary>Returns a bool4 indicating for each component of a dfloat4 whether it is a finite dfloating point value.</summary>
        /// <param name="x">The dfloat4 value to test.</param>
        /// <returns>A bool4 where it is true in a component if that component is finite, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 isfinite(dfloat4 x) { return true; }

        /// <summary>Returns true if the input dfloat is an infinite dfloating point value, false otherwise.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>True if the input was an infinite value; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool isinf(dfloat x) { return false; }

        /// <summary>Returns a bool2 indicating for each component of a dfloat2 whether it is an infinite dfloating point value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was an infinite value; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 isinf(dfloat2 x) { return false; }

        /// <summary>Returns a bool3 indicating for each component of a dfloat3 whether it is an infinite dfloating point value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was an infinite value; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 isinf(dfloat3 x) { return false; }

        /// <summary>Returns a bool4 indicating for each component of a dfloat4 whether it is an infinite dfloating point value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was an infinite value; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 isinf(dfloat4 x) { return false; }


        /// <summary>Returns true if the input dfloat is a NaN (not a number) dfloating point value, false otherwise.</summary>
        /// <remarks>
        /// NaN has several representations and may vary across architectures. Use this function to check if you have a NaN.
        /// </remarks>
        /// <param name="x">Input value.</param>
        /// <returns>True if the value was NaN; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool isnan(dfloat x) { return false; }

        /// <summary>Returns a bool2 indicating for each component of a dfloat2 whether it is a NaN (not a number) dfloating point value.</summary>
        /// <remarks>
        /// NaN has several representations and may vary across architectures. Use this function to check if you have a NaN.
        /// </remarks>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was NaN; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2 isnan(dfloat2 x) { return false; }

        /// <summary>Returns a bool3 indicating for each component of a dfloat3 whether it is a NaN (not a number) dfloating point value.</summary>
        /// <remarks>
        /// NaN has several representations and may vary across architectures. Use this function to check if you have a NaN.
        /// </remarks>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was NaN; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool3 isnan(dfloat3 x) { return false; }

        /// <summary>Returns a bool4 indicating for each component of a dfloat4 whether it is a NaN (not a number) dfloating point value.</summary>
        /// <remarks>
        /// NaN has several representations and may vary across architectures. Use this function to check if you have a NaN.
        /// </remarks>
        /// <param name="x">Input value.</param>
        /// <returns>True if the component was NaN; false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4 isnan(dfloat4 x) { return false; }
        
        /// <summary>Returns the minimum of two dfloat values.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The minimum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat min(dfloat x, dfloat y) { return x < y ? x : y; }

        /// <summary>Returns the componentwise minimum of two dfloat2 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise minimum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 min(dfloat2 x, dfloat2 y) { return new dfloat2(min(x.x, y.x), min(x.y, y.y)); }

        /// <summary>Returns the componentwise minimum of two dfloat3 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise minimum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 min(dfloat3 x, dfloat3 y) { return new dfloat3(min(x.x, y.x), min(x.y, y.y), min(x.z, y.z)); }

        /// <summary>Returns the componentwise minimum of two dfloat4 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise minimum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 min(dfloat4 x, dfloat4 y) { return new dfloat4(min(x.x, y.x), min(x.y, y.y), min(x.z, y.z), min(x.w, y.w)); }

        /// <summary>Returns the maximum of two dfloat values.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The maximum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat max(dfloat x, dfloat y) { return x > y ? x : y; }

        /// <summary>Returns the componentwise maximum of two dfloat2 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise maximum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 max(dfloat2 x, dfloat2 y) { return new dfloat2(max(x.x, y.x), max(x.y, y.y)); }

        /// <summary>Returns the componentwise maximum of two dfloat3 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise maximum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 max(dfloat3 x, dfloat3 y) { return new dfloat3(max(x.x, y.x), max(x.y, y.y), max(x.z, y.z)); }

        /// <summary>Returns the componentwise maximum of two dfloat4 vectors.</summary>
        /// <param name="x">The first input value.</param>
        /// <param name="y">The second input value.</param>
        /// <returns>The componentwise maximum of the two input values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 max(dfloat4 x, dfloat4 y) { return new dfloat4(max(x.x, y.x), max(x.y, y.y), max(x.z, y.z), max(x.w, y.w)); }

        /// <summary>Returns the result of linearly interpolating from start to end using the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The interpolation from start to end.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat lerp(dfloat start, dfloat end, dfloat t) { return start + t * (end - start); }

        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 lerp(dfloat2 start, dfloat2 end, dfloat t) { return start + t * (end - start); }

        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 lerp(dfloat3 start, dfloat3 end, dfloat t) { return start + t * (end - start); }

        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 lerp(dfloat4 start, dfloat4 end, dfloat t) { return start + t * (end - start); }


        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the corresponding components of the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 lerp(dfloat2 start, dfloat2 end, dfloat2 t) { return start + t * (end - start); }

        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the corresponding components of the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 lerp(dfloat3 start, dfloat3 end, dfloat3 t) { return start + t * (end - start); }

        /// <summary>Returns the result of a componentwise linear interpolating from x to y using the corresponding components of the interpolation parameter t.</summary>
        /// <remarks>
        /// If the interpolation parameter is not in the range [0, 1], then this function extrapolates.
        /// </remarks>
        /// <param name="start">The start point, corresponding to the interpolation parameter value of 0.</param>
        /// <param name="end">The end point, corresponding to the interpolation parameter value of 1.</param>
        /// <param name="t">The interpolation parameter. May be a value outside the interval [0, 1].</param>
        /// <returns>The componentwise interpolation from x to y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 lerp(dfloat4 start, dfloat4 end, dfloat4 t) { return start + t * (end - start); }
        
        /// <summary>Returns the result of normalizing a dfloating point value x to a range [a, b]. The opposite of lerp. Equivalent to (x - a) / (b - a).</summary>
        /// <param name="start">The start point of the range.</param>
        /// <param name="end">The end point of the range.</param>
        /// <param name="x">The value to normalize to the range.</param>
        /// <returns>The interpolation parameter of x with respect to the input range [a, b].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat unlerp(dfloat start, dfloat end, dfloat x) { return (x - start) / (end - start); }

        /// <summary>Returns the componentwise result of normalizing a dfloating point value x to a range [a, b]. The opposite of lerp. Equivalent to (x - a) / (b - a).</summary>
        /// <param name="start">The start point of the range.</param>
        /// <param name="end">The end point of the range.</param>
        /// <param name="x">The value to normalize to the range.</param>
        /// <returns>The componentwise interpolation parameter of x with respect to the input range [a, b].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 unlerp(dfloat2 start, dfloat2 end, dfloat2 x) { return (x - start) / (end - start); }

        /// <summary>Returns the componentwise result of normalizing a dfloating point value x to a range [a, b]. The opposite of lerp. Equivalent to (x - a) / (b - a).</summary>
        /// <param name="start">The start point of the range.</param>
        /// <param name="end">The end point of the range.</param>
        /// <param name="x">The value to normalize to the range.</param>
        /// <returns>The componentwise interpolation parameter of x with respect to the input range [a, b].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 unlerp(dfloat3 start, dfloat3 end, dfloat3 x) { return (x - start) / (end - start); }

        /// <summary>Returns the componentwise result of normalizing a dfloating point value x to a range [a, b]. The opposite of lerp. Equivalent to (x - a) / (b - a).</summary>
        /// <param name="start">The start point of the range.</param>
        /// <param name="end">The end point of the range.</param>
        /// <param name="x">The value to normalize to the range.</param>
        /// <returns>The componentwise interpolation parameter of x with respect to the input range [a, b].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 unlerp(dfloat4 start, dfloat4 end, dfloat4 x) { return (x - start) / (end - start); }

        /// <summary>Returns the result of a non-clamping linear remapping of a value x from source range [srcStart, srcEnd] to the destination range [dstStart, dstEnd].</summary>
        /// <param name="srcStart">The start point of the source range [srcStart, srcEnd].</param>
        /// <param name="srcEnd">The end point of the source range [srcStart, srcEnd].</param>
        /// <param name="dstStart">The start point of the destination range [dstStart, dstEnd].</param>
        /// <param name="dstEnd">The end point of the destination range [dstStart, dstEnd].</param>
        /// <param name="x">The value to remap from the source to destination range.</param>
        /// <returns>The remap of input x from the source range to the destination range.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat remap(dfloat srcStart, dfloat srcEnd, dfloat dstStart, dfloat dstEnd, dfloat x) { return lerp(dstStart, dstEnd, unlerp(srcStart, srcEnd, x)); }

        /// <summary>Returns the componentwise result of a non-clamping linear remapping of a value x from source range [srcStart, srcEnd] to the destination range [dstStart, dstEnd].</summary>
        /// <param name="srcStart">The start point of the source range [srcStart, srcEnd].</param>
        /// <param name="srcEnd">The end point of the source range [srcStart, srcEnd].</param>
        /// <param name="dstStart">The start point of the destination range [dstStart, dstEnd].</param>
        /// <param name="dstEnd">The end point of the destination range [dstStart, dstEnd].</param>
        /// <param name="x">The value to remap from the source to destination range.</param>
        /// <returns>The componentwise remap of input x from the source range to the destination range.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 remap(dfloat2 srcStart, dfloat2 srcEnd, dfloat2 dstStart, dfloat2 dstEnd, dfloat2 x) { return lerp(dstStart, dstEnd, unlerp(srcStart, srcEnd, x)); }

        /// <summary>Returns the componentwise result of a non-clamping linear remapping of a value x from source range [srcStart, srcEnd] to the destination range [dstStart, dstEnd].</summary>
        /// <param name="srcStart">The start point of the source range [srcStart, srcEnd].</param>
        /// <param name="srcEnd">The end point of the source range [srcStart, srcEnd].</param>
        /// <param name="dstStart">The start point of the destination range [dstStart, dstEnd].</param>
        /// <param name="dstEnd">The end point of the destination range [dstStart, dstEnd].</param>
        /// <param name="x">The value to remap from the source to destination range.</param>
        /// <returns>The componentwise remap of input x from the source range to the destination range.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 remap(dfloat3 srcStart, dfloat3 srcEnd, dfloat3 dstStart, dfloat3 dstEnd, dfloat3 x) { return lerp(dstStart, dstEnd, unlerp(srcStart, srcEnd, x)); }

        /// <summary>Returns the componentwise result of a non-clamping linear remapping of a value x from source range [srcStart, srcEnd] to the destination range [dstStart, dstEnd].</summary>
        /// <param name="srcStart">The start point of the source range [srcStart, srcEnd].</param>
        /// <param name="srcEnd">The end point of the source range [srcStart, srcEnd].</param>
        /// <param name="dstStart">The start point of the destination range [dstStart, dstEnd].</param>
        /// <param name="dstEnd">The end point of the destination range [dstStart, dstEnd].</param>
        /// <param name="x">The value to remap from the source to destination range.</param>
        /// <returns>The componentwise remap of input x from the source range to the destination range.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 remap(dfloat4 srcStart, dfloat4 srcEnd, dfloat4 dstStart, dfloat4 dstEnd, dfloat4 x) { return lerp(dstStart, dstEnd, unlerp(srcStart, srcEnd, x)); }
        

        /// <summary>Returns the result of a multiply-add operation (a * b + c) on 3 dfloat values.</summary>
        /// <remarks>
        /// When Burst compiled with fast math enabled on some architectures, this could be converted to a fused multiply add (FMA).
        /// FMA is more accurate due to rounding once at the end of the computation rather than twice that is required when
        /// this computation is not fused.
        /// </remarks>
        /// <param name="mulA">First value to multiply.</param>
        /// <param name="mulB">Second value to multiply.</param>
        /// <param name="addC">Third value to add to the product of a and b.</param>
        /// <returns>The multiply-add of the inputs.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat mad(dfloat mulA, dfloat mulB, dfloat addC) { return mulA * mulB + addC; }

        /// <summary>Returns the result of a componentwise multiply-add operation (a * b + c) on 3 dfloat2 vectors.</summary>
        /// <remarks>
        /// When Burst compiled with fast math enabled on some architectures, this could be converted to a fused multiply add (FMA).
        /// FMA is more accurate due to rounding once at the end of the computation rather than twice that is required when
        /// this computation is not fused.
        /// </remarks>
        /// <param name="mulA">First value to multiply.</param>
        /// <param name="mulB">Second value to multiply.</param>
        /// <param name="addC">Third value to add to the product of a and b.</param>
        /// <returns>The componentwise multiply-add of the inputs.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 mad(dfloat2 mulA, dfloat2 mulB, dfloat2 addC) { return mulA * mulB + addC; }

        /// <summary>Returns the result of a componentwise multiply-add operation (a * b + c) on 3 dfloat3 vectors.</summary>
        /// <remarks>
        /// When Burst compiled with fast math enabled on some architectures, this could be converted to a fused multiply add (FMA).
        /// FMA is more accurate due to rounding once at the end of the computation rather than twice that is required when
        /// this computation is not fused.
        /// </remarks>
        /// <param name="mulA">First value to multiply.</param>
        /// <param name="mulB">Second value to multiply.</param>
        /// <param name="addC">Third value to add to the product of a and b.</param>
        /// <returns>The componentwise multiply-add of the inputs.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 mad(dfloat3 mulA, dfloat3 mulB, dfloat3 addC) { return mulA * mulB + addC; }

        /// <summary>Returns the result of a componentwise multiply-add operation (a * b + c) on 3 dfloat4 vectors.</summary>
        /// <remarks>
        /// When Burst compiled with fast math enabled on some architectures, this could be converted to a fused multiply add (FMA).
        /// FMA is more accurate due to rounding once at the end of the computation rather than twice that is required when
        /// this computation is not fused.
        /// </remarks>
        /// <param name="mulA">First value to multiply.</param>
        /// <param name="mulB">Second value to multiply.</param>
        /// <param name="addC">Third value to add to the product of a and b.</param>
        /// <returns>The componentwise multiply-add of the inputs.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 mad(dfloat4 mulA, dfloat4 mulB, dfloat4 addC) { return mulA * mulB + addC; }
        
        /// <summary>Returns the result of clamping the value valueToClamp into the interval (inclusive) [lowerBound, upperBound], where valueToClamp, lowerBound and upperBound are dfloat values.</summary>
        /// <param name="valueToClamp">Input value to be clamped.</param>
        /// <param name="lowerBound">Lower bound of the interval.</param>
        /// <param name="upperBound">Upper bound of the interval.</param>
        /// <returns>The clamping of the input valueToClamp into the interval (inclusive) [lowerBound, upperBound].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat clamp(dfloat valueToClamp, dfloat lowerBound, dfloat upperBound) { return max(lowerBound, min(upperBound, valueToClamp)); }

        /// <summary>Returns the result of a componentwise clamping of the value valueToClamp into the interval (inclusive) [lowerBound, upperBound], where valueToClamp, lowerBound and upperBound are dfloat2 vectors.</summary>
        /// <param name="valueToClamp">Input value to be clamped.</param>
        /// <param name="lowerBound">Lower bound of the interval.</param>
        /// <param name="upperBound">Upper bound of the interval.</param>
        /// <returns>The componentwise clamping of the input valueToClamp into the interval (inclusive) [lowerBound, upperBound].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 clamp(dfloat2 valueToClamp, dfloat2 lowerBound, dfloat2 upperBound) { return max(lowerBound, min(upperBound, valueToClamp)); }

        /// <summary>Returns the result of a componentwise clamping of the value valueToClamp into the interval (inclusive) [lowerBound, upperBound], where valueToClamp, lowerBound and upperBound are dfloat3 vectors.</summary>
        /// <param name="valueToClamp">Input value to be clamped.</param>
        /// <param name="lowerBound">Lower bound of the interval.</param>
        /// <param name="upperBound">Upper bound of the interval.</param>
        /// <returns>The componentwise clamping of the input valueToClamp into the interval (inclusive) [lowerBound, upperBound].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 clamp(dfloat3 valueToClamp, dfloat3 lowerBound, dfloat3 upperBound) { return max(lowerBound, min(upperBound, valueToClamp)); }

        /// <summary>Returns the result of a componentwise clamping of the value valueToClamp into the interval (inclusive) [lowerBound, upperBound], where valueToClamp, lowerBound and upperBound are dfloat4 vectors.</summary>
        /// <param name="valueToClamp">Input value to be clamped.</param>
        /// <param name="lowerBound">Lower bound of the interval.</param>
        /// <param name="upperBound">Upper bound of the interval.</param>
        /// <returns>The componentwise clamping of the input valueToClamp into the interval (inclusive) [lowerBound, upperBound].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 clamp(dfloat4 valueToClamp, dfloat4 lowerBound, dfloat4 upperBound) { return max(lowerBound, min(upperBound, valueToClamp)); }

        /// <summary>Returns the result of clamping the dfloat value x into the interval [0, 1].</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The clamping of the input into the interval [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat saturate(dfloat x) { return clamp(x, dfloat.Zero, dfloat.One); }

        /// <summary>Returns the result of a componentwise clamping of the dfloat2 vector x into the interval [0, 1].</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise clamping of the input into the interval [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 saturate(dfloat2 x) { return clamp(x, new dfloat2(dfloat.Zero), new dfloat2(dfloat.One)); }

        /// <summary>Returns the result of a componentwise clamping of the dfloat3 vector x into the interval [0, 1].</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise clamping of the input into the interval [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 saturate(dfloat3 x) { return clamp(x, new dfloat3(dfloat.Zero), new dfloat3(dfloat.One)); }

        /// <summary>Returns the result of a componentwise clamping of the dfloat4 vector x into the interval [0, 1].</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise clamping of the input into the interval [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 saturate(dfloat4 x) { return clamp(x, new dfloat4(dfloat.Zero), new dfloat4(dfloat.One)); }

        /// <summary>Returns the absolute value of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The absolute value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat abs(dfloat x) { return +x; }

        /// <summary>Returns the componentwise absolute value of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise absolute value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 abs(dfloat2 x) { return new dfloat2(abs(x.x), abs(x.y)); }

        /// <summary>Returns the componentwise absolute value of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise absolute value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 abs(dfloat3 x) { return new dfloat3(abs(x.x), abs(x.y), abs(x.z)); }

        /// <summary>Returns the componentwise absolute value of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise absolute value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 abs(dfloat4 x) { return new dfloat4(abs(x.x), abs(x.y), abs(x.z), abs(x.w)); }

        /// <summary>Returns the dot product of two dfloat values. Equivalent to multiplication.</summary>
        /// <param name="x">The first value.</param>
        /// <param name="y">The second value.</param>
        /// <returns>The dot product of two values.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat dot(dfloat x, dfloat y) { return x * y; }

        /// <summary>Returns the dot product of two dfloat2 vectors.</summary>
        /// <param name="x">The first vector.</param>
        /// <param name="y">The second vector.</param>
        /// <returns>The dot product of two vectors.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat dot(dfloat2 x, dfloat2 y) { return x.x * y.x + x.y * y.y; }

        /// <summary>Returns the dot product of two dfloat3 vectors.</summary>
        /// <param name="x">The first vector.</param>
        /// <param name="y">The second vector.</param>
        /// <returns>The dot product of two vectors.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat dot(dfloat3 x, dfloat3 y) { return x.x * y.x + x.y * y.y + x.z * y.z; }

        /// <summary>Returns the dot product of two dfloat4 vectors.</summary>
        /// <param name="x">The first vector.</param>
        /// <param name="y">The second vector.</param>
        /// <returns>The dot product of two vectors.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat dot(dfloat4 x, dfloat4 y) { return x.x * y.x + x.y * y.y + x.z * y.z + x.w * y.w; }

        /// <summary>Returns the tangent of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat tan(dfloat x)
        {
            return dmath.Tan(x);
        }

        /// <summary>Returns the componentwise tangent of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 tan(dfloat2 x) { return new dfloat2(tan(x.x), tan(x.y)); }

        /// <summary>Returns the componentwise tangent of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 tan(dfloat3 x) { return new dfloat3(tan(x.x), tan(x.y), tan(x.z)); }

        /// <summary>Returns the componentwise tangent of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 tan(dfloat4 x) { return new dfloat4(tan(x.x), tan(x.y), tan(x.z), tan(x.w)); }


        /*/// <summary>Returns the hyperbolic tangent of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The hyperbolic tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat tanh(dfloat x) { return (dfloat)System.Math.Tanh(x); }

        /// <summary>Returns the componentwise hyperbolic tangent of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 tanh(dfloat2 x) { return new dfloat2(tanh(x.x), tanh(x.y)); }

        /// <summary>Returns the componentwise hyperbolic tangent of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 tanh(dfloat3 x) { return new dfloat3(tanh(x.x), tanh(x.y), tanh(x.z)); }

        /// <summary>Returns the componentwise hyperbolic tangent of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic tangent of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 tanh(dfloat4 x) { return new dfloat4(tanh(x.x), tanh(x.y), tanh(x.z), tanh(x.w)); }*/

        /// <summary>Returns the arctangent of a dfloat value.</summary>
        /// <param name="x">A tangent value, usually the ratio y/x on the unit circle.</param>
        /// <returns>The arctangent of the input, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat atan(dfloat x)
        {
            return dmath.Atan(x);
        }

        /// <summary>Returns the componentwise arctangent of a dfloat2 vector.</summary>
        /// <param name="x">A tangent value, usually the ratio y/x on the unit circle.</param>
        /// <returns>The componentwise arctangent of the input, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 atan(dfloat2 x) { return new dfloat2(atan(x.x), atan(x.y)); }

        /// <summary>Returns the componentwise arctangent of a dfloat3 vector.</summary>
        /// <param name="x">A tangent value, usually the ratio y/x on the unit circle.</param>
        /// <returns>The componentwise arctangent of the input, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 atan(dfloat3 x) { return new dfloat3(atan(x.x), atan(x.y), atan(x.z)); }

        /// <summary>Returns the componentwise arctangent of a dfloat4 vector.</summary>
        /// <param name="x">A tangent value, usually the ratio y/x on the unit circle.</param>
        /// <returns>The componentwise arctangent of the input, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 atan(dfloat4 x) { return new dfloat4(atan(x.x), atan(x.y), atan(x.z), atan(x.w)); }


        /// <summary>Returns the 2-argument arctangent of a pair of dfloat values.</summary>
        /// <param name="y">Numerator of the ratio y/x, usually the y component on the unit circle.</param>
        /// <param name="x">Denominator of the ratio y/x, usually the x component on the unit circle.</param>
        /// <returns>The arctangent of the ratio y/x, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat atan2(dfloat y, dfloat x) { return dmath.Atan2(y, x); }

        /// <summary>Returns the componentwise 2-argument arctangent of a pair of dfloats2 vectors.</summary>
        /// <param name="y">Numerator of the ratio y/x, usually the y component on the unit circle.</param>
        /// <param name="x">Denominator of the ratio y/x, usually the x component on the unit circle.</param>
        /// <returns>The componentwise arctangent of the ratio y/x, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 atan2(dfloat2 y, dfloat2 x) { return new dfloat2(atan2(y.x, x.x), atan2(y.y, x.y)); }

        /// <summary>Returns the componentwise 2-argument arctangent of a pair of dfloats3 vectors.</summary>
        /// <param name="y">Numerator of the ratio y/x, usually the y component on the unit circle.</param>
        /// <param name="x">Denominator of the ratio y/x, usually the x component on the unit circle.</param>
        /// <returns>The componentwise arctangent of the ratio y/x, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 atan2(dfloat3 y, dfloat3 x) { return new dfloat3(atan2(y.x, x.x), atan2(y.y, x.y), atan2(y.z, x.z)); }

        /// <summary>Returns the componentwise 2-argument arctangent of a pair of dfloats4 vectors.</summary>
        /// <param name="y">Numerator of the ratio y/x, usually the y component on the unit circle.</param>
        /// <param name="x">Denominator of the ratio y/x, usually the x component on the unit circle.</param>
        /// <returns>The componentwise arctangent of the ratio y/x, in radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 atan2(dfloat4 y, dfloat4 x) { return new dfloat4(atan2(y.x, x.x), atan2(y.y, x.y), atan2(y.z, x.z), atan2(y.w, x.w)); }

        /// <summary>Returns the cosine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The cosine cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cos(dfloat x)
        {
            return dmath.Cos(x);
        }

        /// <summary>Returns the componentwise cosine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise cosine cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 cos(dfloat2 x) { return new dfloat2(cos(x.x), cos(x.y)); }

        /// <summary>Returns the componentwise cosine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise cosine cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 cos(dfloat3 x) { return new dfloat3(cos(x.x), cos(x.y), cos(x.z)); }

        /// <summary>Returns the componentwise cosine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise cosine cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 cos(dfloat4 x) { return new dfloat4(cos(x.x), cos(x.y), cos(x.z), cos(x.w)); }
        
        /*/// <summary>Returns the hyperbolic cosine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The hyperbolic cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cosh(dfloat x) { return (dfloat)System.Math.Cosh(x); }

        /// <summary>Returns the componentwise hyperbolic cosine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 cosh(dfloat2 x) { return new dfloat2(cosh(x.x), cosh(x.y)); }

        /// <summary>Returns the componentwise hyperbolic cosine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 cosh(dfloat3 x) { return new dfloat3(cosh(x.x), cosh(x.y), cosh(x.z)); }

        /// <summary>Returns the componentwise hyperbolic cosine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic cosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 cosh(dfloat4 x) { return new dfloat4(cosh(x.x), cosh(x.y), cosh(x.z), cosh(x.w)); }*/
        
        /// <summary>Returns the arccosine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The arccosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat acos(dfloat x) { return dmath.Acos(x); }

        /// <summary>Returns the componentwise arccosine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arccosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 acos(dfloat2 x) { return new dfloat2(acos(x.x), acos(x.y)); }

        /// <summary>Returns the componentwise arccosine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arccosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 acos(dfloat3 x) { return new dfloat3(acos(x.x), acos(x.y), acos(x.z)); }

        /// <summary>Returns the componentwise arccosine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arccosine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 acos(dfloat4 x) { return new dfloat4(acos(x.x), acos(x.y), acos(x.z), acos(x.w)); }
        
        /// <summary>Returns the sine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat sin(dfloat x) { return dmath.Sin(x); }

        /// <summary>Returns the componentwise sine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 sin(dfloat2 x) { return new dfloat2(sin(x.x), sin(x.y)); }

        /// <summary>Returns the componentwise sine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 sin(dfloat3 x) { return new dfloat3(sin(x.x), sin(x.y), sin(x.z)); }

        /// <summary>Returns the componentwise sine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 sin(dfloat4 x) { return new dfloat4(sin(x.x), sin(x.y), sin(x.z), sin(x.w)); }
        
        /*/// <summary>Returns the hyperbolic sine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The hyperbolic sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat sinh(dfloat x) { return (dfloat)System.Math.Sinh((dfloat)x); }

        /// <summary>Returns the componentwise hyperbolic sine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 sinh(dfloat2 x) { return new dfloat2(sinh(x.x), sinh(x.y)); }

        /// <summary>Returns the componentwise hyperbolic sine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 sinh(dfloat3 x) { return new dfloat3(sinh(x.x), sinh(x.y), sinh(x.z)); }

        /// <summary>Returns the componentwise hyperbolic sine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise hyperbolic sine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 sinh(dfloat4 x) { return new dfloat4(sinh(x.x), sinh(x.y), sinh(x.z), sinh(x.w)); }*/

        /// <summary>Returns the arcsine of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The arcsine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat asin(dfloat x) { return dmath.Asin(x); }

        /// <summary>Returns the componentwise arcsine of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arcsine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 asin(dfloat2 x) { return new dfloat2(asin(x.x), asin(x.y)); }

        /// <summary>Returns the componentwise arcsine of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arcsine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 asin(dfloat3 x) { return new dfloat3(asin(x.x), asin(x.y), asin(x.z)); }

        /// <summary>Returns the componentwise arcsine of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise arcsine of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 asin(dfloat4 x) { return new dfloat4(asin(x.x), asin(x.y), asin(x.z), asin(x.w)); }
        
        /// <summary>Returns the result of rounding a dfloat value up to the nearest integral value less or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The round down to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat floor(dfloat x) { return dmath.Floor(x); }

        /// <summary>Returns the result of rounding each component of a dfloat2 vector value down to the nearest value less or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round down to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 floor(dfloat2 x) { return new dfloat2(floor(x.x), floor(x.y)); }

        /// <summary>Returns the result of rounding each component of a dfloat3 vector value down to the nearest value less or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round down to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 floor(dfloat3 x) { return new dfloat3(floor(x.x), floor(x.y), floor(x.z)); }

        /// <summary>Returns the result of rounding each component of a dfloat4 vector value down to the nearest value less or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round down to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 floor(dfloat4 x) { return new dfloat4(floor(x.x), floor(x.y), floor(x.z), floor(x.w)); }
        
        /// <summary>Returns the result of rounding a dfloat value up to the nearest integral value greater or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The round up to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat ceil(dfloat x)
        {
            return dmath.Ceiling(x);
        }

        /// <summary>Returns the result of rounding each component of a dfloat2 vector value up to the nearest value greater or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round up to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 ceil(dfloat2 x) { return new dfloat2(ceil(x.x), ceil(x.y)); }

        /// <summary>Returns the result of rounding each component of a dfloat3 vector value up to the nearest value greater or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round up to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 ceil(dfloat3 x) { return new dfloat3(ceil(x.x), ceil(x.y), ceil(x.z)); }

        /// <summary>Returns the result of rounding each component of a dfloat4 vector value up to the nearest value greater or equal to the original value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round up to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 ceil(dfloat4 x) { return new dfloat4(ceil(x.x), ceil(x.y), ceil(x.z), ceil(x.w)); }

        /// <summary>Returns the result of rounding a dfloat value to the nearest integral value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The round to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat round(dfloat x) { return dmath.Round(x); }

        /// <summary>Returns the result of rounding each component of a dfloat2 vector value to the nearest integral value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 round(dfloat2 x) { return new dfloat2(round(x.x), round(x.y)); }

        /// <summary>Returns the result of rounding each component of a dfloat3 vector value to the nearest integral value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 round(dfloat3 x) { return new dfloat3(round(x.x), round(x.y), round(x.z)); }

        /// <summary>Returns the result of rounding each component of a dfloat4 vector value to the nearest integral value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise round to nearest integral value of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 round(dfloat4 x) { return new dfloat4(round(x.x), round(x.y), round(x.z), round(x.w)); }
        
        /// <summary>Returns the result of truncating a dfloat value to an integral dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The truncation of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat trunc(dfloat x) { return (dfloat) asint(x); }

        /// <summary>Returns the result of a componentwise truncation of a dfloat2 value to an integral dfloat2 value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise truncation of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 trunc(dfloat2 x) { return new dfloat2(trunc(x.x), trunc(x.y)); }

        /// <summary>Returns the result of a componentwise truncation of a dfloat3 value to an integral dfloat3 value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise truncation of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 trunc(dfloat3 x) { return new dfloat3(trunc(x.x), trunc(x.y), trunc(x.z)); }

        /// <summary>Returns the result of a componentwise truncation of a dfloat4 value to an integral dfloat4 value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise truncation of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 trunc(dfloat4 x) { return new dfloat4(trunc(x.x), trunc(x.y), trunc(x.z), trunc(x.w)); }
        
        /// <summary>Returns the fractional part of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The fractional part of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat frac(dfloat x) { return x - floor(x); }

        /// <summary>Returns the componentwise fractional parts of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise fractional part of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 frac(dfloat2 x) { return x - floor(x); }

        /// <summary>Returns the componentwise fractional parts of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise fractional part of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 frac(dfloat3 x) { return x - floor(x); }

        /// <summary>Returns the componentwise fractional parts of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise fractional part of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 frac(dfloat4 x) { return x - floor(x); }

        /// <summary>Returns the reciprocal a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The reciprocal of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat rcp(dfloat x) { return (dfloat)1.0f / x; }

        /// <summary>Returns the componentwise reciprocal a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise reciprocal of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 rcp(dfloat2 x) { return (dfloat)1.0f / x; }

        /// <summary>Returns the componentwise reciprocal a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise reciprocal of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 rcp(dfloat3 x) { return (dfloat)1.0f / x; }

        /// <summary>Returns the componentwise reciprocal a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise reciprocal of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 rcp(dfloat4 x) { return (dfloat)1.0f / x; }

        /// <summary>Returns the sign of a dfloat value. -1.0f if it is less than zero, 0.0f if it is zero and 1.0f if it greater than zero.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The sign of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat sign(dfloat x)
        {
            return (dfloat) dfloat.Sign(x);
        }

        /// <summary>Returns the componentwise sign of a dfloat2 value. 1.0f for positive components, 0.0f for zero components and -1.0f for negative components.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sign of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 sign(dfloat2 x) { return new dfloat2(sign(x.x), sign(x.y)); }

        /// <summary>Returns the componentwise sign of a dfloat3 value. 1.0f for positive components, 0.0f for zero components and -1.0f for negative components.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sign of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 sign(dfloat3 x) { return new dfloat3(sign(x.x), sign(x.y), sign(x.z)); }

        /// <summary>Returns the componentwise sign of a dfloat4 value. 1.0f for positive components, 0.0f for zero components and -1.0f for negative components.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise sign of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 sign(dfloat4 x) { return new dfloat4(sign(x.x), sign(x.y), sign(x.z), sign(x.w)); }

        /// <summary>Returns x raised to the power y.</summary>
        /// <param name="x">The exponent base.</param>
        /// <param name="y">The exponent power.</param>
        /// <returns>The result of raising x to the power y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat pow(dfloat x, dfloat y) { return dmath.Pow(x, y); }

        /// <summary>Returns the componentwise result of raising x to the power y.</summary>
        /// <param name="x">The exponent base.</param>
        /// <param name="y">The exponent power.</param>
        /// <returns>The componentwise result of raising x to the power y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 pow(dfloat2 x, dfloat2 y) { return new dfloat2(pow(x.x, y.x), pow(x.y, y.y)); }

        /// <summary>Returns the componentwise result of raising x to the power y.</summary>
        /// <param name="x">The exponent base.</param>
        /// <param name="y">The exponent power.</param>
        /// <returns>The componentwise result of raising x to the power y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 pow(dfloat3 x, dfloat3 y) { return new dfloat3(pow(x.x, y.x), pow(x.y, y.y), pow(x.z, y.z)); }

        /// <summary>Returns the componentwise result of raising x to the power y.</summary>
        /// <param name="x">The exponent base.</param>
        /// <param name="y">The exponent power.</param>
        /// <returns>The componentwise result of raising x to the power y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 pow(dfloat4 x, dfloat4 y) { return new dfloat4(pow(x.x, y.x), pow(x.y, y.y), pow(x.z, y.z), pow(x.w, y.w)); }
        
        /*/// <summary>Returns the base-e exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The base-e exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat exp(dfloat x) { return (dfloat)System.Math.Exp((dfloat)x); }

        /// <summary>Returns the componentwise base-e exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-e exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 exp(dfloat2 x) { return new dfloat2(exp(x.x), exp(x.y)); }

        /// <summary>Returns the componentwise base-e exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-e exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 exp(dfloat3 x) { return new dfloat3(exp(x.x), exp(x.y), exp(x.z)); }

        /// <summary>Returns the componentwise base-e exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-e exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 exp(dfloat4 x) { return new dfloat4(exp(x.x), exp(x.y), exp(x.z), exp(x.w)); }
        
        /// <summary>Returns the base-2 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The base-2 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat exp2(dfloat x) { return (dfloat)System.Math.Exp((dfloat)x * 0.69314718f); }

        /// <summary>Returns the componentwise base-2 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 exp2(dfloat2 x) { return new dfloat2(exp2(x.x), exp2(x.y)); }

        /// <summary>Returns the componentwise base-2 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 exp2(dfloat3 x) { return new dfloat3(exp2(x.x), exp2(x.y), exp2(x.z)); }

        /// <summary>Returns the componentwise base-2 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 exp2(dfloat4 x) { return new dfloat4(exp2(x.x), exp2(x.y), exp2(x.z), exp2(x.w)); }*/
        
        /*/// <summary>Returns the base-10 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The base-10 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat exp10(dfloat x) { return (dfloat)System.Math.Exp((dfloat)x * 2.30258509f); }

        /// <summary>Returns the componentwise base-10 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 exp10(dfloat2 x) { return new dfloat2(exp10(x.x), exp10(x.y)); }

        /// <summary>Returns the componentwise base-10 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 exp10(dfloat3 x) { return new dfloat3(exp10(x.x), exp10(x.y), exp10(x.z)); }

        /// <summary>Returns the componentwise base-10 exponential of x.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 exponential of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 exp10(dfloat4 x) { return new dfloat4(exp10(x.x), exp10(x.y), exp10(x.z), exp10(x.w)); }*/

        /*/// <summary>Returns the natural logarithm of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The natural logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat log(dfloat x) { return (dfloat)System.Math.Log((dfloat)x); }

        /// <summary>Returns the componentwise natural logarithm of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise natural logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 log(dfloat2 x) { return new dfloat2(log(x.x), log(x.y)); }

        /// <summary>Returns the componentwise natural logarithm of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise natural logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 log(dfloat3 x) { return new dfloat3(log(x.x), log(x.y), log(x.z)); }

        /// <summary>Returns the componentwise natural logarithm of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise natural logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 log(dfloat4 x) { return new dfloat4(log(x.x), log(x.y), log(x.z), log(x.w)); }*/

        /// <summary>Returns the base-2 logarithm of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The base-2 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat log2(dfloat x) { return dmath.Log2(x); }

        /// <summary>Returns the componentwise base-2 logarithm of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 log2(dfloat2 x) { return new dfloat2(log2(x.x), log2(x.y)); }

        /// <summary>Returns the componentwise base-2 logarithm of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 log2(dfloat3 x) { return new dfloat3(log2(x.x), log2(x.y), log2(x.z)); }

        /// <summary>Returns the componentwise base-2 logarithm of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-2 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 log2(dfloat4 x) { return new dfloat4(log2(x.x), log2(x.y), log2(x.z), log2(x.w)); }
        
        /*/// <summary>Returns the base-10 logarithm of a dfloat value.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The base-10 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat log10(dfloat x) { return (dfloat)System.Math.Log10((dfloat)x); }

        /// <summary>Returns the componentwise base-10 logarithm of a dfloat2 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 log10(dfloat2 x) { return new dfloat2(log10(x.x), log10(x.y)); }

        /// <summary>Returns the componentwise base-10 logarithm of a dfloat3 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 log10(dfloat3 x) { return new dfloat3(log10(x.x), log10(x.y), log10(x.z)); }

        /// <summary>Returns the componentwise base-10 logarithm of a dfloat4 vector.</summary>
        /// <param name="x">Input value.</param>
        /// <returns>The componentwise base-10 logarithm of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 log10(dfloat4 x) { return new dfloat4(log10(x.x), log10(x.y), log10(x.z), log10(x.w)); }*/


        /// <summary>Returns the dfloating point remainder of x/y.</summary>
        /// <param name="x">The dividend in x/y.</param>
        /// <param name="y">The divisor in x/y.</param>
        /// <returns>The remainder of x/y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat fmod(dfloat x, dfloat y) { return x % y; }

        /// <summary>Returns the componentwise dfloating point remainder of x/y.</summary>
        /// <param name="x">The dividend in x/y.</param>
        /// <param name="y">The divisor in x/y.</param>
        /// <returns>The componentwise remainder of x/y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 fmod(dfloat2 x, dfloat2 y) { return new dfloat2(x.x % y.x, x.y % y.y); }

        /// <summary>Returns the componentwise dfloating point remainder of x/y.</summary>
        /// <param name="x">The dividend in x/y.</param>
        /// <param name="y">The divisor in x/y.</param>
        /// <returns>The componentwise remainder of x/y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 fmod(dfloat3 x, dfloat3 y) { return new dfloat3(x.x % y.x, x.y % y.y, x.z % y.z); }

        /// <summary>Returns the componentwise dfloating point remainder of x/y.</summary>
        /// <param name="x">The dividend in x/y.</param>
        /// <param name="y">The divisor in x/y.</param>
        /// <returns>The componentwise remainder of x/y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 fmod(dfloat4 x, dfloat4 y) { return new dfloat4(x.x % y.x, x.y % y.y, x.z % y.z, x.w % y.w); }
        /// <summary>Splits a dfloat value into an integral part i and a fractional part that gets returned. Both parts take the sign of the input.</summary>
        /// <param name="x">Value to split into integral and fractional part.</param>
        /// <param name="i">Output value containing integral part of x.</param>
        /// <returns>The fractional part of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat modf(dfloat x, out dfloat i) { i = trunc(x); return x - i; }

        /// <summary>
        /// Performs a componentwise split of a dfloat2 vector into an integral part i and a fractional part that gets returned.
        /// Both parts take the sign of the corresponding input component.
        /// </summary>
        /// <param name="x">Value to split into integral and fractional part.</param>
        /// <param name="i">Output value containing integral part of x.</param>
        /// <returns>The componentwise fractional part of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 modf(dfloat2 x, out dfloat2 i) { i = trunc(x); return x - i; }

        /// <summary>
        /// Performs a componentwise split of a dfloat3 vector into an integral part i and a fractional part that gets returned.
        /// Both parts take the sign of the corresponding input component.
        /// </summary>
        /// <param name="x">Value to split into integral and fractional part.</param>
        /// <param name="i">Output value containing integral part of x.</param>
        /// <returns>The componentwise fractional part of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 modf(dfloat3 x, out dfloat3 i) { i = trunc(x); return x - i; }

        /// <summary>
        /// Performs a componentwise split of a dfloat4 vector into an integral part i and a fractional part that gets returned.
        /// Both parts take the sign of the corresponding input component.
        /// </summary>
        /// <param name="x">Value to split into integral and fractional part.</param>
        /// <param name="i">Output value containing integral part of x.</param>
        /// <returns>The componentwise fractional part of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 modf(dfloat4 x, out dfloat4 i) { i = trunc(x); return x - i; }

        /// <summary>Returns the square root of a dfloat value.</summary>
        /// <param name="x">Value to use when computing square root.</param>
        /// <returns>The square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat sqrt(dfloat x) { return dmath.Sqrt(x); }

        /// <summary>Returns the componentwise square root of a dfloat2 vector.</summary>
        /// <param name="x">Value to use when computing square root.</param>
        /// <returns>The componentwise square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 sqrt(dfloat2 x) { return new dfloat2(sqrt(x.x), sqrt(x.y)); }

        /// <summary>Returns the componentwise square root of a dfloat3 vector.</summary>
        /// <param name="x">Value to use when computing square root.</param>
        /// <returns>The componentwise square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 sqrt(dfloat3 x) { return new dfloat3(sqrt(x.x), sqrt(x.y), sqrt(x.z)); }

        /// <summary>Returns the componentwise square root of a dfloat4 vector.</summary>
        /// <param name="x">Value to use when computing square root.</param>
        /// <returns>The componentwise square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 sqrt(dfloat4 x) { return new dfloat4(sqrt(x.x), sqrt(x.y), sqrt(x.z), sqrt(x.w)); }
        
        /// <summary>Returns the reciprocal square root of a dfloat value.</summary>
        /// <param name="x">Value to use when computing reciprocal square root.</param>
        /// <returns>The reciprocal square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat rsqrt(dfloat x) { return (dfloat)1.0f / sqrt(x); }

        /// <summary>Returns the componentwise reciprocal square root of a dfloat2 vector.</summary>
        /// <param name="x">Value to use when computing reciprocal square root.</param>
        /// <returns>The componentwise reciprocal square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 rsqrt(dfloat2 x) { return (dfloat)1.0f / sqrt(x); }

        /// <summary>Returns the componentwise reciprocal square root of a dfloat3 vector.</summary>
        /// <param name="x">Value to use when computing reciprocal square root.</param>
        /// <returns>The componentwise reciprocal square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 rsqrt(dfloat3 x) { return (dfloat)1.0f / sqrt(x); }

        /// <summary>Returns the componentwise reciprocal square root of a dfloat4 vector</summary>
        /// <param name="x">Value to use when computing reciprocal square root.</param>
        /// <returns>The componentwise reciprocal square root.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 rsqrt(dfloat4 x) { return (dfloat)1.0f / sqrt(x); }
        
        /// <summary>Returns a normalized version of the dfloat2 vector x by scaling it by 1 / length(x).</summary>
        /// <param name="x">Vector to normalize.</param>
        /// <returns>The normalized vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 normalize(dfloat2 x) { return rsqrt(dot(x, x)) * x; }

        /// <summary>Returns a normalized version of the dfloat3 vector x by scaling it by 1 / length(x).</summary>
        /// <param name="x">Vector to normalize.</param>
        /// <returns>The normalized vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 normalize(dfloat3 x) { return rsqrt(dot(x, x)) * x; }

        /// <summary>Returns a normalized version of the dfloat4 vector x by scaling it by 1 / length(x).</summary>
        /// <param name="x">Vector to normalize.</param>
        /// <returns>The normalized vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 normalize(dfloat4 x) { return rsqrt(dot(x, x)) * x; }

        /*/// <summary>
        /// Returns a safe normalized version of the dfloat2 vector x by scaling it by 1 / length(x).
        /// Returns the given default value when 1 / length(x) does not produce a finite number.
        /// </summary>
        /// <param name="x">Vector to normalize.</param>
        /// <param name="defaultvalue">Vector to return if normalized vector is not finite.</param>
        /// <returns>The normalized vector or the default value if the normalized vector is not finite.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        static public dfloat2 normalizesafe(dfloat2 x, dfloat2 defaultvalue = new dfloat2())
        {
            dfloat len = dmath.dot(x, x);
            return dmath.select(defaultvalue, x * dmath.rsqrt(len), len > FLT_MIN_NORMAL);
        }

        /// <summary>
        /// Returns a safe normalized version of the dfloat3 vector x by scaling it by 1 / length(x).
        /// Returns the given default value when 1 / length(x) does not produce a finite number.
        /// </summary>
        /// <param name="x">Vector to normalize.</param>
        /// <param name="defaultvalue">Vector to return if normalized vector is not finite.</param>
        /// <returns>The normalized vector or the default value if the normalized vector is not finite.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        static public dfloat3 normalizesafe(dfloat3 x, dfloat3 defaultvalue = new dfloat3())
        {
            dfloat len = dmath.dot(x, x);
            return dmath.select(defaultvalue, x * dmath.rsqrt(len), len > FLT_MIN_NORMAL);
        }

        /// <summary>
        /// Returns a safe normalized version of the dfloat4 vector x by scaling it by 1 / length(x).
        /// Returns the given default value when 1 / length(x) does not produce a finite number.
        /// </summary>
        /// <param name="x">Vector to normalize.</param>
        /// <param name="defaultvalue">Vector to return if normalized vector is not finite.</param>
        /// <returns>The normalized vector or the default value if the normalized vector is not finite.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        static public dfloat4 normalizesafe(dfloat4 x, dfloat4 defaultvalue = new dfloat4())
        {
            dfloat len = dmath.dot(x, x);
            return dmath.select(defaultvalue, x * dmath.rsqrt(len), len > FLT_MIN_NORMAL);
        }*/

        /// <summary>Returns the length of a dfloat value. Equivalent to the absolute value.</summary>
        /// <param name="x">Value to use when computing length.</param>
        /// <returns>Length of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat length(dfloat x) { return abs(x); }

        /// <summary>Returns the length of a dfloat2 vector.</summary>
        /// <param name="x">Vector to use when computing length.</param>
        /// <returns>Length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat length(dfloat2 x) { return sqrt(dot(x, x)); }

        /// <summary>Returns the length of a dfloat3 vector.</summary>
        /// <param name="x">Vector to use when computing length.</param>
        /// <returns>Length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat length(dfloat3 x) { return sqrt(dot(x, x)); }

        /// <summary>Returns the length of a dfloat4 vector.</summary>
        /// <param name="x">Vector to use when computing length.</param>
        /// <returns>Length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat length(dfloat4 x) { return sqrt(dot(x, x)); }

        /// <summary>Returns the squared length of a dfloat value. Equivalent to squaring the value.</summary>
        /// <param name="x">Value to use when computing squared length.</param>
        /// <returns>Squared length of x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat lengthsq(dfloat x) { return x*x; }

        /// <summary>Returns the squared length of a dfloat2 vector.</summary>
        /// <param name="x">Vector to use when computing squared length.</param>
        /// <returns>Squared length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat lengthsq(dfloat2 x) { return dot(x, x); }

        /// <summary>Returns the squared length of a dfloat3 vector.</summary>
        /// <param name="x">Vector to use when computing squared length.</param>
        /// <returns>Squared length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat lengthsq(dfloat3 x) { return dot(x, x); }

        /// <summary>Returns the squared length of a dfloat4 vector.</summary>
        /// <param name="x">Vector to use when computing squared length.</param>
        /// <returns>Squared length of vector x.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat lengthsq(dfloat4 x) { return dot(x, x); }

        /// <summary>Returns the distance between two dfloat values.</summary>
        /// <param name="x">First value to use in distance computation.</param>
        /// <param name="y">Second value to use in distance computation.</param>
        /// <returns>The distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distance(dfloat x, dfloat y) { return abs(y - x); }

        /// <summary>Returns the distance between two dfloat2 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distance(dfloat2 x, dfloat2 y) { return length(y - x); }

        /// <summary>Returns the distance between two dfloat3 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distance(dfloat3 x, dfloat3 y) { return length(y - x); }

        /// <summary>Returns the distance between two dfloat4 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distance(dfloat4 x, dfloat4 y) { return length(y - x); }

        /// <summary>Returns the squared distance between two dfloat values.</summary>
        /// <param name="x">First value to use in distance computation.</param>
        /// <param name="y">Second value to use in distance computation.</param>
        /// <returns>The squared distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distancesq(dfloat x, dfloat y) { return (y - x) * (y - x); }

        /// <summary>Returns the squared distance between two dfloat2 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The squared distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distancesq(dfloat2 x, dfloat2 y) { return lengthsq(y - x); }

        /// <summary>Returns the squared distance between two dfloat3 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The squared distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distancesq(dfloat3 x, dfloat3 y) { return lengthsq(y - x); }

        /// <summary>Returns the squared distance between two dfloat4 vectors.</summary>
        /// <param name="x">First vector to use in distance computation.</param>
        /// <param name="y">Second vector to use in distance computation.</param>
        /// <returns>The squared distance between x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat distancesq(dfloat4 x, dfloat4 y) { return lengthsq(y - x); }

        /// <summary>Returns the cross product of two dfloat3 vectors.</summary>
        /// <param name="x">First vector to use in cross product.</param>
        /// <param name="y">Second vector to use in cross product.</param>
        /// <returns>The cross product of x and y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 cross(dfloat3 x, dfloat3 y) { return (x * y.yzx - x.yzx * y).yzx; }

        /// <summary>Returns a smooth Hermite interpolation between 0.0f and 1.0f when x is in the interval (inclusive) [xMin, xMax].</summary>
        /// <param name="xMin">The minimum range of the x parameter.</param>
        /// <param name="xMax">The maximum range of the x parameter.</param>
        /// <param name="x">The value to be interpolated.</param>
        /// <returns>Returns a value camped to the range [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat smoothstep(dfloat xMin, dfloat xMax, dfloat x)
        {
            var t = saturate((x - xMin) / (xMax - xMin));
            return t * t * (dfloat.Three - (dfloat.Two * t));
        }

        /// <summary>Returns a componentwise smooth Hermite interpolation between 0.0f and 1.0f when x is in the interval (inclusive) [xMin, xMax].</summary>
        /// <param name="xMin">The minimum range of the x parameter.</param>
        /// <param name="xMax">The maximum range of the x parameter.</param>
        /// <param name="x">The value to be interpolated.</param>
        /// <returns>Returns component values camped to the range [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 smoothstep(dfloat2 xMin, dfloat2 xMax, dfloat2 x)
        {
            var t = saturate((x - xMin) / (xMax - xMin));
            return t * t * (dfloat.Three - (dfloat.Two * t));
        }

        /// <summary>Returns a componentwise smooth Hermite interpolation between 0.0f and 1.0f when x is in the interval (inclusive) [xMin, xMax].</summary>
        /// <param name="xMin">The minimum range of the x parameter.</param>
        /// <param name="xMax">The maximum range of the x parameter.</param>
        /// <param name="x">The value to be interpolated.</param>
        /// <returns>Returns component values camped to the range [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 smoothstep(dfloat3 xMin, dfloat3 xMax, dfloat3 x)
        {
            var t = saturate((x - xMin) / (xMax - xMin));
            return t * t * (dfloat.Three - (dfloat.Two * t));
        }

        /// <summary>Returns a componentwise smooth Hermite interpolation between 0.0f and 1.0f when x is in the interval (inclusive) [xMin, xMax].</summary>
        /// <param name="xMin">The minimum range of the x parameter.</param>
        /// <param name="xMax">The maximum range of the x parameter.</param>
        /// <param name="x">The value to be interpolated.</param>
        /// <returns>Returns component values camped to the range [0, 1].</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 smoothstep(dfloat4 xMin, dfloat4 xMax, dfloat4 x)
        {
            var t = saturate((x - xMin) / (xMax - xMin));
            return t * t * (dfloat.Three - (dfloat.Two * t));
        }
        
        /// <summary>Returns true if any component of the input dfloat2 vector is non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if any the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool any(dfloat2 x) { return x.x != dfloat.Zero || x.y != dfloat.Zero; }

        /// <summary>Returns true if any component of the input dfloat3 vector is non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if any the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool any(dfloat3 x) { return x.x != dfloat.Zero || x.y != dfloat.Zero || x.z != dfloat.Zero; }

        /// <summary>Returns true if any component of the input dfloat4 vector is non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if any the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool any(dfloat4 x) { return x.x != dfloat.Zero || x.y != dfloat.Zero || x.z != dfloat.Zero || x.w != dfloat.Zero; }

        /// <summary>Returns true if all components of the input dfloat2 vector are non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if all the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool all(dfloat2 x) { return x.x != dfloat.Zero && x.y != dfloat.Zero; }

        /// <summary>Returns true if all components of the input dfloat3 vector are non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if all the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool all(dfloat3 x) { return x.x != dfloat.Zero && x.y != dfloat.Zero && x.z != dfloat.Zero; }

        /// <summary>Returns true if all components of the input dfloat4 vector are non-zero, false otherwise.</summary>
        /// <param name="x">Vector of values to compare.</param>
        /// <returns>True if all the components of x are non-zero, false otherwise.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool all(dfloat4 x) { return x.x != dfloat.Zero && x.y != dfloat.Zero && x.z != dfloat.Zero && x.w != dfloat.Zero; }
        
        /// <summary>Returns trueValue if test is true, falseValue otherwise.</summary>
        /// <param name="falseValue">Value to use if test is false.</param>
        /// <param name="trueValue">Value to use if test is true.</param>
        /// <param name="test">Bool value to choose between falseValue and trueValue.</param>
        /// <returns>The selection between falseValue and trueValue according to bool test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat select(dfloat falseValue, dfloat trueValue, bool test)    { return test ? trueValue : falseValue; }

        /// <summary>Returns trueValue if test is true, falseValue otherwise.</summary>
        /// <param name="falseValue">Value to use if test is false.</param>
        /// <param name="trueValue">Value to use if test is true.</param>
        /// <param name="test">Bool value to choose between falseValue and trueValue.</param>
        /// <returns>The selection between falseValue and trueValue according to bool test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 select(dfloat2 falseValue, dfloat2 trueValue, bool test) { return test ? trueValue : falseValue; }

        /// <summary>Returns trueValue if test is true, falseValue otherwise.</summary>
        /// <param name="falseValue">Value to use if test is false.</param>
        /// <param name="trueValue">Value to use if test is true.</param>
        /// <param name="test">Bool value to choose between falseValue and trueValue.</param>
        /// <returns>The selection between falseValue and trueValue according to bool test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 select(dfloat3 falseValue, dfloat3 trueValue, bool test) { return test ? trueValue : falseValue; }

        /// <summary>Returns trueValue if test is true, falseValue otherwise.</summary>
        /// <param name="falseValue">Value to use if test is false.</param>
        /// <param name="trueValue">Value to use if test is true.</param>
        /// <param name="test">Bool value to choose between falseValue and trueValue.</param>
        /// <returns>The selection between falseValue and trueValue according to bool test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 select(dfloat4 falseValue, dfloat4 trueValue, bool test) { return test ? trueValue : falseValue; }


        /// <summary>
        /// Returns a componentwise selection between two double4 vectors falseValue and trueValue based on a bool4 selection mask test.
        /// Per component, the component from trueValue is selected when test is true, otherwise the component from falseValue is selected.
        /// </summary>
        /// <param name="falseValue">Values to use if test is false.</param>
        /// <param name="trueValue">Values to use if test is true.</param>
        /// <param name="test">Selection mask to choose between falseValue and trueValue.</param>
        /// <returns>The componentwise selection between falseValue and trueValue according to selection mask test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 select(dfloat2 falseValue, dfloat2 trueValue, bool2 test) { return new dfloat2(test.x ? trueValue.x : falseValue.x, test.y ? trueValue.y : falseValue.y); }

        /// <summary>
        /// Returns a componentwise selection between two double4 vectors falseValue and trueValue based on a bool4 selection mask test.
        /// Per component, the component from trueValue is selected when test is true, otherwise the component from falseValue is selected.
        /// </summary>
        /// <param name="falseValue">Values to use if test is false.</param>
        /// <param name="trueValue">Values to use if test is true.</param>
        /// <param name="test">Selection mask to choose between falseValue and trueValue.</param>
        /// <returns>The componentwise selection between falseValue and trueValue according to selection mask test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 select(dfloat3 falseValue, dfloat3 trueValue, bool3 test) { return new dfloat3(test.x ? trueValue.x : falseValue.x, test.y ? trueValue.y : falseValue.y, test.z ? trueValue.z : falseValue.z); }

        /// <summary>
        /// Returns a componentwise selection between two double4 vectors falseValue and trueValue based on a bool4 selection mask test.
        /// Per component, the component from trueValue is selected when test is true, otherwise the component from falseValue is selected.
        /// </summary>
        /// <param name="falseValue">Values to use if test is false.</param>
        /// <param name="trueValue">Values to use if test is true.</param>
        /// <param name="test">Selection mask to choose between falseValue and trueValue.</param>
        /// <returns>The componentwise selection between falseValue and trueValue according to selection mask test.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 select(dfloat4 falseValue, dfloat4 trueValue, bool4 test) { return new dfloat4(test.x ? trueValue.x : falseValue.x, test.y ? trueValue.y : falseValue.y, test.z ? trueValue.z : falseValue.z, test.w ? trueValue.w : falseValue.w); }
        
        /// <summary>Returns the result of a step function where the result is 1.0f when x &gt;= threshold and 0.0f otherwise.</summary>
        /// <param name="threshold">Value to be used as a threshold for returning 1.</param>
        /// <param name="x">Value to compare against threshold.</param>
        /// <returns>1 if the comparison x &gt;= threshold is true, otherwise 0.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat step(dfloat threshold, dfloat x) { return select(dfloat.Zero, dfloat.One, x >= threshold); }

        /// <summary>Returns the result of a componentwise step function where each component is 1.0f when x &gt;= threshold and 0.0f otherwise.</summary>
        /// <param name="threshold">Vector of values to be used as a threshold for returning 1.</param>
        /// <param name="x">Vector of values to compare against threshold.</param>
        /// <returns>1 if the componentwise comparison x &gt;= threshold is true, otherwise 0.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 step(dfloat2 threshold, dfloat2 x) { return select(dfloat2(0.0f), dfloat2(1.0f), x >= threshold); }

        /// <summary>Returns the result of a componentwise step function where each component is 1.0f when x &gt;= threshold and 0.0f otherwise.</summary>
        /// <param name="threshold">Vector of values to be used as a threshold for returning 1.</param>
        /// <param name="x">Vector of values to compare against threshold.</param>
        /// <returns>1 if the componentwise comparison x &gt;= threshold is true, otherwise 0.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 step(dfloat3 threshold, dfloat3 x) { return select(dfloat3(0.0f), dfloat3(1.0f), x >= threshold); }

        /// <summary>Returns the result of a componentwise step function where each component is 1.0f when x &gt;= threshold and 0.0f otherwise.</summary>
        /// <param name="threshold">Vector of values to be used as a threshold for returning 1.</param>
        /// <param name="x">Vector of values to compare against threshold.</param>
        /// <returns>1 if the componentwise comparison x &gt;= threshold is true, otherwise 0.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 step(dfloat4 threshold, dfloat4 x) { return select(dfloat4(0.0f), dfloat4(1.0f), x >= threshold); }

        /// <summary>Given an incident vector i and a normal vector n, returns the reflection vector r = i - 2.0f * dot(i, n) * n.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <returns>Reflection vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 reflect(dfloat2 i, dfloat2 n) { return i - (dfloat)2f * n * dot(i, n); }

        /// <summary>Given an incident vector i and a normal vector n, returns the reflection vector r = i - 2.0f * dot(i, n) * n.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <returns>Reflection vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 reflect(dfloat3 i, dfloat3 n) { return i - (dfloat)2f * n * dot(i, n); }

        /// <summary>Given an incident vector i and a normal vector n, returns the reflection vector r = i - 2.0f * dot(i, n) * n.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <returns>Reflection vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 reflect(dfloat4 i, dfloat4 n) { return i - (dfloat)2f * n * dot(i, n); }
        
        /// <summary>Returns the refraction vector given the incident vector i, the normal vector n and the refraction index.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <param name="indexOfRefraction">Index of refraction.</param>
        /// <returns>Refraction vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 refract(dfloat2 i, dfloat2 n, dfloat indexOfRefraction)
        {
            dfloat ni = dot(n, i);
            dfloat k = (dfloat)1.0m - indexOfRefraction * indexOfRefraction * ((dfloat)1.0m - ni * ni);
            return select((dfloat)0.0m, indexOfRefraction * i - (indexOfRefraction * ni + sqrt(k)) * n, k >= (dfloat)0);
        }

        /// <summary>Returns the refraction vector given the incident vector i, the normal vector n and the refraction index.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <param name="indexOfRefraction">Index of refraction.</param>
        /// <returns>Refraction vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 refract(dfloat3 i, dfloat3 n, dfloat indexOfRefraction)
        {
            dfloat ni = dot(n, i);
            dfloat k = (dfloat)1.0m - indexOfRefraction * indexOfRefraction * ((dfloat)1.0m - ni * ni);
            return select((dfloat)0.0m, indexOfRefraction * i - (indexOfRefraction * ni + sqrt(k)) * n, k >= (dfloat)0);
        }

        /// <summary>Returns the refraction vector given the incident vector i, the normal vector n and the refraction index.</summary>
        /// <param name="i">Incident vector.</param>
        /// <param name="n">Normal vector.</param>
        /// <param name="indexOfRefraction">Index of refraction.</param>
        /// <returns>Refraction vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 refract(dfloat4 i, dfloat4 n, dfloat indexOfRefraction)
        {
            dfloat ni = dot(n, i);
            dfloat k = (dfloat)1.0m - indexOfRefraction * indexOfRefraction * ((dfloat)1.0m - ni * ni);
            return select((dfloat)0.0m, indexOfRefraction * i - (indexOfRefraction * ni + sqrt(k)) * n, k >= (dfloat)0);
        }

        /// <summary>
        /// Compute vector projection of a onto b.
        /// </summary>
        /// <remarks>
        /// Some finite vectors a and b could generate a non-finite result. This is most likely when a's components
        /// are very large (close to Single.MaxValue) or when b's components are very small (close to FLT_MIN_NORMAL).
        /// In these cases, you can call <see cref="projectsafe(Unity.Mathematics.dfloat2,Unity.Mathematics.dfloat2,Unity.Mathematics.dfloat2)"/>
        /// which will use a given default value if the result is not finite.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <returns>Vector projection of a onto b.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 project(dfloat2 a, dfloat2 ontoB)
        {
            return (dot(a, ontoB) / dot(ontoB, ontoB)) * ontoB;
        }

        /// <summary>
        /// Compute vector projection of a onto b.
        /// </summary>
        /// <remarks>
        /// Some finite vectors a and b could generate a non-finite result. This is most likely when a's components
        /// are very large (close to Single.MaxValue) or when b's components are very small (close to FLT_MIN_NORMAL).
        /// In these cases, you can call <see cref="projectsafe(Unity.Mathematics.dfloat3,Unity.Mathematics.dfloat3,Unity.Mathematics.dfloat3)"/>
        /// which will use a given default value if the result is not finite.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <returns>Vector projection of a onto b.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 project(dfloat3 a, dfloat3 ontoB)
        {
            return (dot(a, ontoB) / dot(ontoB, ontoB)) * ontoB;
        }

        /// <summary>
        /// Compute vector projection of a onto b.
        /// </summary>
        /// <remarks>
        /// Some finite vectors a and b could generate a non-finite result. This is most likely when a's components
        /// are very large (close to Single.MaxValue) or when b's components are very small (close to FLT_MIN_NORMAL).
        /// In these cases, you can call <see cref="projectsafe(Unity.Mathematics.dfloat4,Unity.Mathematics.dfloat4,Unity.Mathematics.dfloat4)"/>
        /// which will use a given default value if the result is not finite.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <returns>Vector projection of a onto b.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 project(dfloat4 a, dfloat4 ontoB)
        {
            return (dot(a, ontoB) / dot(ontoB, ontoB)) * ontoB;
        }

        /// <summary>
        /// Compute vector projection of a onto b. If result is not finite, then return the default value instead.
        /// </summary>
        /// <remarks>
        /// This function performs extra checks to see if the result of projecting a onto b is finite. If you know that
        /// your inputs will generate a finite result or you don't care if the result is finite, then you can call
        /// <see cref="project(Unity.Mathematics.dfloat2,Unity.Mathematics.dfloat2)"/> instead which is faster than this
        /// function.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <param name="defaultValue">Default value to return if projection is not finite.</param>
        /// <returns>Vector projection of a onto b or the default value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 projectsafe(dfloat2 a, dfloat2 ontoB, dfloat2 defaultValue = new dfloat2())
        {
            var proj = project(a, ontoB);

            return select(defaultValue, proj, math.all(isfinite(proj)));
        }

        /// <summary>
        /// Compute vector projection of a onto b. If result is not finite, then return the default value instead.
        /// </summary>
        /// <remarks>
        /// This function performs extra checks to see if the result of projecting a onto b is finite. If you know that
        /// your inputs will generate a finite result or you don't care if the result is finite, then you can call
        /// <see cref="project(Unity.Mathematics.dfloat3,Unity.Mathematics.dfloat3)"/> instead which is faster than this
        /// function.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <param name="defaultValue">Default value to return if projection is not finite.</param>
        /// <returns>Vector projection of a onto b or the default value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 projectsafe(dfloat3 a, dfloat3 ontoB, dfloat3 defaultValue = new dfloat3())
        {
            var proj = project(a, ontoB);

            return select(defaultValue, proj, math.all(isfinite(proj)));
        }

        /// <summary>
        /// Compute vector projection of a onto b. If result is not finite, then return the default value instead.
        /// </summary>
        /// <remarks>
        /// This function performs extra checks to see if the result of projecting a onto b is finite. If you know that
        /// your inputs will generate a finite result or you don't care if the result is finite, then you can call
        /// <see cref="project(Unity.Mathematics.dfloat4,Unity.Mathematics.dfloat4)"/> instead which is faster than this
        /// function.
        /// </remarks>
        /// <param name="a">Vector to project.</param>
        /// <param name="ontoB">Non-zero vector to project onto.</param>
        /// <param name="defaultValue">Default value to return if projection is not finite.</param>
        /// <returns>Vector projection of a onto b or the default value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 projectsafe(dfloat4 a, dfloat4 ontoB, dfloat4 defaultValue = new dfloat4())
        {
            var proj = project(a, ontoB);

            return select(defaultValue, proj, math.all(isfinite(proj)));
        }

        /// <summary>Conditionally flips a vector n if two vectors i and ng are pointing in the same direction. Returns n if dot(i, ng) &lt; 0, -n otherwise.</summary>
        /// <param name="n">Vector to conditionally flip.</param>
        /// <param name="i">First vector in direction comparison.</param>
        /// <param name="ng">Second vector in direction comparison.</param>
        /// <returns>-n if i and ng point in the same direction; otherwise return n unchanged.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 faceforward(dfloat2 n, dfloat2 i, dfloat2 ng) { return select(n, -n, dot(ng, i) >= (dfloat)0.0f); }

        /// <summary>Conditionally flips a vector n if two vectors i and ng are pointing in the same direction. Returns n if dot(i, ng) &lt; 0, -n otherwise.</summary>
        /// <param name="n">Vector to conditionally flip.</param>
        /// <param name="i">First vector in direction comparison.</param>
        /// <param name="ng">Second vector in direction comparison.</param>
        /// <returns>-n if i and ng point in the same direction; otherwise return n unchanged.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 faceforward(dfloat3 n, dfloat3 i, dfloat3 ng) { return select(n, -n, dot(ng, i) >= (dfloat)0.0f); }

        /// <summary>Conditionally flips a vector n if two vectors i and ng are pointing in the same direction. Returns n if dot(i, ng) &lt; 0, -n otherwise.</summary>
        /// <param name="n">Vector to conditionally flip.</param>
        /// <param name="i">First vector in direction comparison.</param>
        /// <param name="ng">Second vector in direction comparison.</param>
        /// <returns>-n if i and ng point in the same direction; otherwise return n unchanged.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 faceforward(dfloat4 n, dfloat4 i, dfloat4 ng) { return select(n, -n, dot(ng, i) >= (dfloat)0.0f); }

        /// <summary>Returns the sine and cosine of the input dfloat value x through the out parameters s and c.</summary>
        /// <remarks>When Burst compiled, his method is faster than calling sin() and cos() separately.</remarks>
        /// <param name="x">Input angle in radians.</param>
        /// <param name="s">Output sine of the input.</param>
        /// <param name="c">Output cosine of the input.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void sincos(dfloat x, out dfloat s, out dfloat c) { s = sin(x); c = cos(x); }

        /// <summary>Returns the componentwise sine and cosine of the input dfloat2 vector x through the out parameters s and c.</summary>
        /// <remarks>When Burst compiled, his method is faster than calling sin() and cos() separately.</remarks>
        /// <param name="x">Input vector containing angles in radians.</param>
        /// <param name="s">Output vector containing the componentwise sine of the input.</param>
        /// <param name="c">Output vector containing the componentwise cosine of the input.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void sincos(dfloat2 x, out dfloat2 s, out dfloat2 c) { s = sin(x); c = cos(x); }

        /// <summary>Returns the componentwise sine and cosine of the input dfloat3 vector x through the out parameters s and c.</summary>
        /// <remarks>When Burst compiled, his method is faster than calling sin() and cos() separately.</remarks>
        /// <param name="x">Input vector containing angles in radians.</param>
        /// <param name="s">Output vector containing the componentwise sine of the input.</param>
        /// <param name="c">Output vector containing the componentwise cosine of the input.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void sincos(dfloat3 x, out dfloat3 s, out dfloat3 c) { s = sin(x); c = cos(x); }

        /// <summary>Returns the componentwise sine and cosine of the input dfloat4 vector x through the out parameters s and c.</summary>
        /// <remarks>When Burst compiled, his method is faster than calling sin() and cos() separately.</remarks>
        /// <param name="x">Input vector containing angles in radians.</param>
        /// <param name="s">Output vector containing the componentwise sine of the input.</param>
        /// <param name="c">Output vector containing the componentwise cosine of the input.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void sincos(dfloat4 x, out dfloat4 s, out dfloat4 c) { s = sin(x); c = cos(x); }

        /*/// <summary>Returns the result of converting a dfloat value from degrees to radians.</summary>
        /// <param name="x">Angle in degrees.</param>
        /// <returns>Angle converted to radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat radians(dfloat x) { return x * TORADIANS; }

        /// <summary>Returns the result of a componentwise conversion of a dfloat2 vector from degrees to radians.</summary>
        /// <param name="x">Vector containing angles in degrees.</param>
        /// <returns>Vector containing angles converted to radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 radians(dfloat2 x) { return x * TORADIANS; }

        /// <summary>Returns the result of a componentwise conversion of a dfloat3 vector from degrees to radians.</summary>
        /// <param name="x">Vector containing angles in degrees.</param>
        /// <returns>Vector containing angles converted to radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 radians(dfloat3 x) { return x * TORADIANS; }

        /// <summary>Returns the result of a componentwise conversion of a dfloat4 vector from degrees to radians.</summary>
        /// <param name="x">Vector containing angles in degrees.</param>
        /// <returns>Vector containing angles converted to radians.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 radians(dfloat4 x) { return x * TORADIANS; }

        /// <summary>Returns the result of converting a double value from radians to degrees.</summary>
        /// <param name="x">Angle in radians.</param>
        /// <returns>Angle converted to degrees.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat degrees(dfloat x) { return x * TODEGREES; }

        /// <summary>Returns the result of a componentwise conversion of a double2 vector from radians to degrees.</summary>
        /// <param name="x">Vector containing angles in radians.</param>
        /// <returns>Vector containing angles converted to degrees.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 degrees(dfloat2 x) { return x * TODEGREES; }

        /// <summary>Returns the result of a componentwise conversion of a double3 vector from radians to degrees.</summary>
        /// <param name="x">Vector containing angles in radians.</param>
        /// <returns>Vector containing angles converted to degrees.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 degrees(dfloat3 x) { return x * TODEGREES; }

        /// <summary>Returns the result of a componentwise conversion of a double4 vector from radians to degrees.</summary>
        /// <param name="x">Vector containing angles in radians.</param>
        /// <returns>Vector containing angles converted to degrees.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 degrees(dfloat4 x) { return x * TODEGREES; }*/

        /// <summary>Returns the minimum component of a dfloat2 vector.</summary>
        /// <param name="x">The vector to use when computing the minimum component.</param>
        /// <returns>The value of the minimum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmin(dfloat2 x) { return min(x.x, x.y); }

        /// <summary>Returns the minimum component of a dfloat3 vector.</summary>
        /// <param name="x">The vector to use when computing the minimum component.</param>
        /// <returns>The value of the minimum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmin(dfloat3 x) { return min(min(x.x, x.y), x.z); }

        /// <summary>Returns the minimum component of a dfloat4 vector.</summary>
        /// <param name="x">The vector to use when computing the minimum component.</param>
        /// <returns>The value of the minimum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmin(dfloat4 x) { return min(min(x.x, x.y), min(x.z, x.w)); }

        /// <summary>Returns the maximum component of a dfloat2 vector.</summary>
        /// <param name="x">The vector to use when computing the maximum component.</param>
        /// <returns>The value of the maximum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmax(dfloat2 x) { return max(x.x, x.y); }

        /// <summary>Returns the maximum component of a dfloat3 vector.</summary>
        /// <param name="x">The vector to use when computing the maximum component.</param>
        /// <returns>The value of the maximum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmax(dfloat3 x) { return max(max(x.x, x.y), x.z); }

        /// <summary>Returns the maximum component of a dfloat4 vector.</summary>
        /// <param name="x">The vector to use when computing the maximum component.</param>
        /// <returns>The value of the maximum component of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat cmax(dfloat4 x) { return max(max(x.x, x.y), max(x.z, x.w)); }

        /// <summary>Returns the horizontal sum of components of a dfloat2 vector.</summary>
        /// <param name="x">The vector to use when computing the horizontal sum.</param>
        /// <returns>The horizontal sum of of components of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat csum(dfloat2 x) { return x.x + x.y; }

        /// <summary>Returns the horizontal sum of components of a dfloat3 vector.</summary>
        /// <param name="x">The vector to use when computing the horizontal sum.</param>
        /// <returns>The horizontal sum of of components of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat csum(dfloat3 x) { return x.x + x.y + x.z; }

        /// <summary>Returns the horizontal sum of components of a dfloat4 vector.</summary>
        /// <param name="x">The vector to use when computing the horizontal sum.</param>
        /// <returns>The horizontal sum of of components of the vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat csum(dfloat4 x) { return (x.x + x.y) + (x.z + x.w); }

        /// <summary>
        /// Computes the square (x * x) of the input argument x.
        /// </summary>
        /// <param name="x">Value to square.</param>
        /// <returns>Returns the square of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat square(dfloat x)
        {
            return x * x;
        }

        /// <summary>
        /// Computes the component-wise square (x * x) of the input argument x.
        /// </summary>
        /// <param name="x">Value to square.</param>
        /// <returns>Returns the square of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 square(dfloat2 x)
        {
            return x * x;
        }

        /// <summary>
        /// Computes the component-wise square (x * x) of the input argument x.
        /// </summary>
        /// <param name="x">Value to square.</param>
        /// <returns>Returns the square of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 square(dfloat3 x)
        {
            return x * x;
        }

        /// <summary>
        /// Computes the component-wise square (x * x) of the input argument x.
        /// </summary>
        /// <param name="x">Value to square.</param>
        /// <returns>Returns the square of the input.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 square(dfloat4 x)
        {
            return x * x;
        }

        /*/// <summary>
        /// Packs components with an enabled mask to the left.
        /// </summary>
        /// <remarks>
        /// This function is also known as left packing. The effect of this function is to filter out components that
        /// are not enabled and leave an output buffer tightly packed with only the enabled components. A common use
        /// case is if you perform intersection tests on arrays of data in structure of arrays (SoA) form and need to
        /// produce an output array of the things that intersected.
        /// </remarks>
        /// <param name="output">Pointer to packed output array where enabled components should be stored to.</param>
        /// <param name="index">Index into output array where first enabled component should be stored to.</param>
        /// <param name="val">The value to to compress.</param>
        /// <param name="mask">Mask indicating which components are enabled.</param>
        /// <returns>Index to element after the last one stored.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static unsafe int compress(dfloat* output, int index, dfloat4 val, bool4 mask)
        {
            return compress((int*)output, index, *(int4*)&val, mask);
        }

        /// <summary>Returns the dfloating point representation of a half-precision dfloating point value.</summary>
        /// <param name="x">The half precision dfloat.</param>
        /// <returns>The single precision dfloat representation of the half precision dfloat.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat f16tof32(uint x)
        {
            const uint shifted_exp = (0x7c00 << 13);
            uint uf = (x & 0x7fff) << 13;
            uint e = uf & shifted_exp;
            uf += (127 - 15) << 23;
            uf += select(0, (128u - 16u) << 23, e == shifted_exp);
            uf = select(uf, asuint(asdfloat(uf + (1 << 23)) - 6.10351563e-05f), e == 0);
            uf |= (x & 0x8000) << 16;
            return asdfloat(uf);
        }

        /// <summary>Returns the dfloating point representation of a half-precision dfloating point vector.</summary>
        /// <param name="x">The half precision dfloat vector.</param>
        /// <returns>The single precision dfloat vector representation of the half precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 f16tof32(uint2 x)
        {
            const uint shifted_exp = (0x7c00 << 13);
            uint2 uf = (x & 0x7fff) << 13;
            uint2 e = uf & shifted_exp;
            uf += (127 - 15) << 23;
            uf += select(0, (128u - 16u) << 23, e == shifted_exp);
            uf = select(uf, asuint(asdfloat(uf + (1 << 23)) - 6.10351563e-05f), e == 0);
            uf |= (x & 0x8000) << 16;
            return asdfloat(uf);
        }

        /// <summary>Returns the dfloating point representation of a half-precision dfloating point vector.</summary>
        /// <param name="x">The half precision dfloat vector.</param>
        /// <returns>The single precision dfloat vector representation of the half precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 f16tof32(uint3 x)
        {
            const uint shifted_exp = (0x7c00 << 13);
            uint3 uf = (x & 0x7fff) << 13;
            uint3 e = uf & shifted_exp;
            uf += (127 - 15) << 23;
            uf += select(0, (128u - 16u) << 23, e == shifted_exp);
            uf = select(uf, asuint(asdfloat(uf + (1 << 23)) - 6.10351563e-05f), e == 0);
            uf |= (x & 0x8000) << 16;
            return asdfloat(uf);
        }

        /// <summary>Returns the dfloating point representation of a half-precision dfloating point vector.</summary>
        /// <param name="x">The half precision dfloat vector.</param>
        /// <returns>The single precision dfloat vector representation of the half precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 f16tof32(uint4 x)
        {
            const uint shifted_exp = (0x7c00 << 13);
            uint4 uf = (x & 0x7fff) << 13;
            uint4 e = uf & shifted_exp;
            uf += (127 - 15) << 23;
            uf += select(0, (128u - 16u) << 23, e == shifted_exp);
            uf = select(uf, asuint(asdfloat(uf + (1 << 23)) - 6.10351563e-05f), e == 0);
            uf |= (x & 0x8000) << 16;
            return asdfloat(uf);
        }

        /// <summary>Returns the result converting a dfloat value to its nearest half-precision dfloating point representation.</summary>
        /// <param name="x">The single precision dfloat.</param>
        /// <returns>The half precision dfloat representation of the single precision dfloat.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint f32tof16(dfloat x)
        {
            const int infinity_32 = 255 << 23;
            const uint msk = 0x7FFFF000u;

            uint ux = asuint(x);
            uint uux = ux & msk;
            uint h = (uint)(asuint(min(asdfloat(uux) * 1.92592994e-34f, 260042752.0f)) + 0x1000) >> 13;   // Clamp to signed infinity if overflowed
            h = select(h, select(0x7c00u, 0x7e00u, (int)uux > infinity_32), (int)uux >= infinity_32);   // NaN->qNaN and Inf->Inf
            return h | (ux & ~msk) >> 16;
        }

        /// <summary>Returns the result of a componentwise conversion of a dfloat2 vector to its nearest half-precision dfloating point representation.</summary>
        /// <param name="x">The single precision dfloat vector.</param>
        /// <returns>The half precision dfloat vector representation of the single precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2 f32tof16(dfloat2 x)
        {
            const int infinity_32 = 255 << 23;
            const uint msk = 0x7FFFF000u;

            uint2 ux = asuint(x);
            uint2 uux = ux & msk;
            uint2 h = (uint2)(asint(min(asdfloat(uux) * 1.92592994e-34f, 260042752.0f)) + 0x1000) >> 13;   // Clamp to signed infinity if overflowed
            h = select(h, select(0x7c00u, 0x7e00u, (int2)uux > infinity_32), (int2)uux >= infinity_32);   // NaN->qNaN and Inf->Inf
            return h | (ux & ~msk) >> 16;
        }

        /// <summary>Returns the result of a componentwise conversion of a dfloat3 vector to its nearest half-precision dfloating point representation.</summary>
        /// <param name="x">The single precision dfloat vector.</param>
        /// <returns>The half precision dfloat vector representation of the single precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint3 f32tof16(dfloat3 x)
        {
            const int infinity_32 = 255 << 23;
            const uint msk = 0x7FFFF000u;

            uint3 ux = asuint(x);
            uint3 uux = ux & msk;
            uint3 h = (uint3)(asint(min(asdfloat(uux) * 1.92592994e-34f, 260042752.0f)) + 0x1000) >> 13;   // Clamp to signed infinity if overflowed
            h = select(h, select(0x7c00u, 0x7e00u, (int3)uux > infinity_32), (int3)uux >= infinity_32);   // NaN->qNaN and Inf->Inf
            return h | (ux & ~msk) >> 16;
        }

        /// <summary>Returns the result of a componentwise conversion of a dfloat4 vector to its nearest half-precision dfloating point representation.</summary>
        /// <param name="x">The single precision dfloat vector.</param>
        /// <returns>The half precision dfloat vector representation of the single precision dfloat vector.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint4 f32tof16(dfloat4 x)
        {
            const int infinity_32 = 255 << 23;
            const uint msk = 0x7FFFF000u;

            uint4 ux = asuint(x);
            uint4 uux = ux & msk;
            uint4 h = (uint4)(asint(min(asdfloat(uux) * 1.92592994e-34f, 260042752.0f)) + 0x1000) >> 13;   // Clamp to signed infinity if overflowed
            h = select(h, select(0x7c00u, 0x7e00u, (int4)uux > infinity_32), (int4)uux >= infinity_32);   // NaN->qNaN and Inf->Inf
            return h | (ux & ~msk) >> 16;
        }*/

        /*/// <summary>
        /// Generate an orthonormal basis given a single unit length normal vector.
        /// </summary>
        /// <remarks>
        /// This implementation is from "Building an Orthonormal Basis, Revisited"
        /// https://graphics.pixar.com/library/OrthonormalB/paper.pdf
        /// </remarks>
        /// <param name="normal">Unit length normal vector.</param>
        /// <param name="basis1">Output unit length vector, orthogonal to normal vector.</param>
        /// <param name="basis2">Output unit length vector, orthogonal to normal vector and basis1.</param>
        public static void orthonormal_basis(dfloat3 normal, out dfloat3 basis1, out dfloat3 basis2)
        {
            var sign = normal.z >= 0.0f ? 1.0f : -1.0f;
            var a = -1.0f / (sign + normal.z);
            var b = normal.x * normal.y * a;
            basis1.x = 1.0f + sign * normal.x * normal.x * a;
            basis1.y = sign * b;
            basis1.z = -sign * normal.x;
            basis2.x = b;
            basis2.y = sign + normal.y * normal.y * a;
            basis2.z = -normal.y;
        }*/

        /// <summary>Change the sign of x based on the most significant bit of y [msb(y) ? -x : x].</summary>
        /// <param name="x">The single precision dfloat to change the sign.</param>
        /// <param name="y">The single precision dfloat used to test the most significant bit.</param>
        /// <returns>Returns x with changed sign based on y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat chgsign(dfloat x, dfloat y)
        {
            return asdfloat(asuint(x) ^ (asuint(y) & 0x80000000));
        }

        /// <summary>Change the sign of components of x based on the most significant bit of components of y [msb(y) ? -x : x].</summary>
        /// <param name="x">The single precision dfloat vector to change the sign.</param>
        /// <param name="y">The single precision dfloat vector used to test the most significant bit.</param>
        /// <returns>Returns vector x with changed sign based on vector y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat2 chgsign(dfloat2 x, dfloat2 y)
        {
            return asdfloat(asuint(x) ^ (asuint(y) & 0x80000000));
        }

        /// <summary>Change the sign of components of x based on the most significant bit of components of y [msb(y) ? -x : x].</summary>
        /// <param name="x">The single precision dfloat vector to change the sign.</param>
        /// <param name="y">The single precision dfloat vector used to test the most significant bit.</param>
        /// <returns>Returns vector x with changed sign based on vector y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 chgsign(dfloat3 x, dfloat3 y)
        {
            return asdfloat(asuint(x) ^ (asuint(y) & 0x80000000));
        }

        /// <summary>Change the sign of components of x based on the most significant bit of components of y [msb(y) ? -x : x].</summary>
        /// <param name="x">The single precision dfloat vector to change the sign.</param>
        /// <param name="y">The single precision dfloat vector used to test the most significant bit.</param>
        /// <returns>Returns vector x with changed sign based on vector y.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat4 chgsign(dfloat4 x, dfloat4 y)
        {
            return asdfloat(asuint(x) ^ (asuint(y) & 0x80000000));
        }

        /// <summary>
        /// Unity's up axis (0, 1, 0).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-up.html](https://docs.unity3d.com/ScriptReference/Vector3-up.html)</remarks>
        /// <returns>The up axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 up() { return new dfloat3(dfloat.Zero, dfloat.One, dfloat.Zero); }  // for compatibility

        /// <summary>
        /// Unity's down axis (0, -1, 0).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-down.html](https://docs.unity3d.com/ScriptReference/Vector3-down.html)</remarks>
        /// <returns>The down axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 down() { return new dfloat3(dfloat.Zero, -dfloat.One, dfloat.Zero); }

        /// <summary>
        /// Unity's forward axis (0, 0, 1).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-forward.html](https://docs.unity3d.com/ScriptReference/Vector3-forward.html)</remarks>
        /// <returns>The forward axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 forward() { return new dfloat3(dfloat.Zero, dfloat.Zero, dfloat.One); }

        /// <summary>
        /// Unity's back axis (0, 0, -1).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-back.html](https://docs.unity3d.com/ScriptReference/Vector3-back.html)</remarks>
        /// <returns>The back axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 back() { return new dfloat3(dfloat.Zero, dfloat.Zero, -dfloat.One); }

        /// <summary>
        /// Unity's left axis (-1, 0, 0).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-left.html](https://docs.unity3d.com/ScriptReference/Vector3-left.html)</remarks>
        /// <returns>The left axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 left() { return new dfloat3(-dfloat.One, dfloat.Zero, dfloat.Zero); }

        /// <summary>
        /// Unity's right axis (1, 0, 0).
        /// </summary>
        /// <remarks>Matches [https://docs.unity3d.com/ScriptReference/Vector3-right.html](https://docs.unity3d.com/ScriptReference/Vector3-right.html)</remarks>
        /// <returns>The right axis.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 right() { return new dfloat3(dfloat.One, dfloat.Zero, dfloat.Zero); }

        /*/// <summary>
        /// Returns the Euler angle representation of the quaternion following the XYZ rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in XYZ order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerXYZ(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            // prepare the data
            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Mathematics.dfloat3.zero;

            var y1 = d2.z - d1.y;
            if (y1 * y1 < cutoff)
            {
                var x1 = d2.y + d1.x;
                var x2 = d3.z + d3.w - d3.y - d3.x;
                var z1 = d2.x + d1.z;
                var z2 = d3.x + d3.w - d3.y - d3.z;
                euler = dfloat3(atan2(x1, x2), -asin(y1), atan2(z1, z2));
            }
            else //xzx
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.z, d1.y, d2.x, d1.z);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), -asin(y1), 0f);
            }

            return euler;
        }

        /// <summary>
        /// Returns the Euler angle representation of the quaternion following the XZY rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in XZY order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerXZY(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            // prepare the data
            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Unity.Mathematics.dfloat3.zero;

            var y1 = d2.x + d1.z;
            if (y1 * y1 < cutoff)
            {
                var x1 = -d2.y + d1.x;
                var x2 = d3.y + d3.w - d3.z - d3.x;
                var z1 = -d2.z + d1.y;
                var z2 = d3.x + d3.w - d3.y - d3.z;
                euler = dfloat3(atan2(x1, x2), asin(y1), atan2(z1, z2));
            }
            else //xyx
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.x, d1.z, d2.z, d1.y);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), asin(y1), 0f);
            }

            return euler.xzy;
        }

        /// <summary>
        /// Returns the Euler angle representation of the quaternion following the YXZ rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in YXZ order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerYXZ(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            // prepare the data
            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Unity.Mathematics.dfloat3.zero;

            var y1 = d2.y + d1.x;
            if (y1 * y1 < cutoff)
            {
                var x1 = -d2.z + d1.y;
                var x2 = d3.z + d3.w - d3.x - d3.y;
                var z1 = -d2.x + d1.z;
                var z2 = d3.y + d3.w - d3.z - d3.x;
                euler = dfloat3(atan2(x1, x2), asin(y1), atan2(z1, z2));
            }
            else //yzy
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.x, d1.z, d2.y, d1.x);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), asin(y1), 0f);
            }

            return euler.yxz;
        }

        /// <summary>
        /// Returns the Euler angle representation of the quaternion following the YZX rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in YZX order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerYZX(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            // prepare the data
            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Mathematics.dfloat3.zero;

            var y1 = d2.x - d1.z;
            if (y1 * y1 < cutoff)
            {
                var x1 = d2.z + d1.y;
                var x2 = d3.x + d3.w - d3.z - d3.y;
                var z1 = d2.y + d1.x;
                var z2 = d3.y + d3.w - d3.x - d3.z;
                euler = dfloat3(atan2(x1, x2), -asin(y1), atan2(z1, z2));
            }
            else //yxy
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.x, d1.z, d2.y, d1.x);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), -asin(y1), 0f);
            }

            return euler.zxy;
        }

        /// <summary>
        /// Returns the Euler angle representation of the quaternion following the ZXY rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in ZXY order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerZXY(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            // prepare the data
            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Unity.Mathematics.dfloat3.zero;

            var y1 = d2.y - d1.x;
            if (y1 * y1 < cutoff)
            {
                var x1 = d2.x + d1.z;
                var x2 = d3.y + d3.w - d3.x - d3.z;
                var z1 = d2.z + d1.y;
                var z2 = d3.z + d3.w - d3.x - d3.y;
                euler = dfloat3(atan2(x1, x2), -asin(y1), atan2(z1, z2));
            }
            else //zxz
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.z, d1.y, d2.y, d1.x);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), -asin(y1), 0f);
            }

            return euler.yzx;
        }

        /// <summary>
        /// Returns the Euler angle representation of the quaternion following the ZYX rotation order.
        /// All rotation angles are in radians and clockwise when looking along the rotation axis towards the origin.
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <returns>The Euler angle representation of the quaternion in ZYX order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 EulerZYX(dquaternion q)
        {
            const dfloat epsilon = 1e-6f;
            const dfloat cutoff = (1f - 2f * epsilon) * (1f - 2f * epsilon);

            var qv = q.value;
            var d1 = qv * qv.wwww * dfloat4(2f); //xw, yw, zw, ww
            var d2 = qv * qv.yzxw * dfloat4(2f); //xy, yz, zx, ww
            var d3 = qv * qv;
            var euler = Mathematics.dfloat3.zero; dfloat3.Zero;

            var y1 = d2.z + d1.y;
            if (y1 * y1 < cutoff)
            {
                var x1 = -d2.x + d1.z;
                var x2 = d3.x + d3.w - d3.y - d3.z;
                var z1 = -d2.y + d1.x;
                var z2 = d3.z + d3.w - d3.y - d3.x;
                euler = dfloat3(atan2(x1, x2), asin(y1), atan2(z1, z2));
            }
            else //zxz
            {
                y1 = clamp(y1, -1f, 1f);
                var abcd = dfloat4(d2.z, d1.y, d2.y, d1.x);
                var x1 = 2f * (abcd.x * abcd.w + abcd.y * abcd.z); //2(ad+bc)
                var x2 = csum(abcd * abcd * dfloat4(-1f, 1f, -1f, 1f));
                euler = dfloat3(atan2(x1, x2), asin(y1), 0f);
            }

            return euler.zyx;
        }*/

        /*/// <summary>
        /// Returns the Euler angle representation of the quaternion. The returned angles depend on the specified order to apply the
        /// three rotations around the principal axes. All rotation angles are in radians and clockwise when looking along the
        /// rotation axis towards the origin.
        /// When the rotation order is known at compile time, to get the best performance you should use the specific
        /// Euler rotation constructors such as EulerZXY(...).
        /// </summary>
        /// <param name="q">The quaternion to convert to Euler angles.</param>
        /// <param name="order">The order in which the rotations are applied.</param>
        /// <returns>The Euler angle representation of the quaternion in the specified order.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3 Euler(dquaternion q, math.RotationOrder order = math.RotationOrder.Default)
        {
            switch (order)
            {
                case math.RotationOrder.XYZ:
                    return EulerXYZ(q);
                case math.RotationOrder.XZY:
                    return EulerXZY(q);
                case math.RotationOrder.YXZ:
                    return EulerYXZ(q);
                case math.RotationOrder.YZX:
                    return EulerYZX(q);
                case math.RotationOrder.ZXY:
                    return EulerZXY(q);
                case math.RotationOrder.ZYX:
                    return EulerZYX(q);
                default:
                    return Mathematics.dfloat3.zero;
            }
        }*/

        /// <summary>
        /// Matrix columns multiplied by scale components
        /// m.c0.x * s.x | m.c1.x * s.y | m.c2.x * s.z
        /// m.c0.y * s.x | m.c1.y * s.y | m.c2.y * s.z
        /// m.c0.z * s.x | m.c1.z * s.y | m.c2.z * s.z
        /// </summary>
        /// <param name="m">Matrix to scale.</param>
        /// <param name="s">Scaling coefficients for each column.</param>
        /// <returns>The scaled matrix.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3x3 mulScale(dfloat3x3 m, dfloat3 s) => new dfloat3x3(m.c0 * s.x, m.c1 * s.y, m.c2 * s.z);

        /// <summary>
        /// Matrix rows multiplied by scale components
        /// m.c0.x * s.x | m.c1.x * s.x | m.c2.x * s.x
        /// m.c0.y * s.y | m.c1.y * s.y | m.c2.y * s.y
        /// m.c0.z * s.z | m.c1.z * s.z | m.c2.z * s.z
        /// </summary>
        /// <param name="s">Scaling coefficients for each row.</param>
        /// <param name="m">Matrix to scale.</param>
        /// <returns>The scaled matrix.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static dfloat3x3 scaleMul(dfloat3 s, dfloat3x3 m) => new dfloat3x3(m.c0 * s, m.c1 * s, m.c2 * s);

        // Internal

        // SSE shuffles
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat4 unpacklo(dfloat4 a, dfloat4 b)
        {
            return shuffle(a, b, ShuffleComponent.LeftX, ShuffleComponent.RightX, ShuffleComponent.LeftY, ShuffleComponent.RightY);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat4 unpackhi(dfloat4 a, dfloat4 b)
        {
            return shuffle(a, b, ShuffleComponent.LeftZ, ShuffleComponent.RightZ, ShuffleComponent.LeftW, ShuffleComponent.RightW);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat4 movelh(dfloat4 a, dfloat4 b)
        {
            return shuffle(a, b, ShuffleComponent.LeftX, ShuffleComponent.LeftY, ShuffleComponent.RightX, ShuffleComponent.RightY);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static dfloat4 movehl(dfloat4 a, dfloat4 b)
        {
            return shuffle(b, a, ShuffleComponent.LeftZ, ShuffleComponent.LeftW, ShuffleComponent.RightZ, ShuffleComponent.RightW);
        }
    }
}
